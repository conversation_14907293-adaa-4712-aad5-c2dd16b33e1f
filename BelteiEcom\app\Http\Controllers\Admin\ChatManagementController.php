<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChatConversation;
use App\Models\ChatMessage;
use App\Models\ChatAgentStatus;
use App\Models\User;
use App\Services\ChatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatManagementController extends Controller
{
    protected $chatService;

    public function __construct(ChatService $chatService)
    {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->chatService = $chatService;
    }

    /**
     * Display the chat management dashboard.
     */
    public function index()
    {
        $statistics = $this->chatService->getStatistics();
        $waitingConversations = ChatConversation::waiting()
                                              ->with(['customer', 'latestMessage'])
                                              ->orderBy('created_at', 'asc')
                                              ->limit(10)
                                              ->get();

        $onlineAgents = ChatAgentStatus::online()
                                      ->with('user')
                                      ->orderBy('last_activity_at', 'desc')
                                      ->get();

        return view('admin.chat.index', compact('statistics', 'waitingConversations', 'onlineAgents'));
    }

    /**
     * Display agent dashboard.
     */
    public function agent()
    {
        $agentId = Auth::id();

        // Get or create agent status
        $agentStatus = ChatAgentStatus::updateOrCreateForUser($agentId);

        // Get agent's conversations
        $activeConversations = $this->chatService->getAgentConversations($agentId, 'active');
        $waitingConversations = ChatConversation::waiting()
                                              ->with(['customer', 'latestMessage'])
                                              ->orderBy('created_at', 'asc')
                                              ->get();

        return view('admin.chat.agent', compact('agentStatus', 'activeConversations', 'waitingConversations'));
    }

    /**
     * Display conversation list.
     */
    public function conversations(Request $request)
    {
        $query = $request->get('q');
        $filters = $request->only(['status', 'agent_id', 'priority', 'date_from', 'date_to']);

        $conversations = $this->chatService->searchConversations($query, $filters);
        $agents = User::where('is_admin', true)->orderBy('name')->get();

        return view('admin.chat.conversations', compact('conversations', 'agents', 'query', 'filters'));
    }

    /**
     * Display specific conversation.
     */
    public function conversation($id)
    {
        $conversation = ChatConversation::with(['customer', 'agent'])->findOrFail($id);
        $messages = $this->chatService->getMessages($id);

        // Mark messages as read for agent
        $this->chatService->markAsRead($id);

        return view('admin.chat.conversation', compact('conversation', 'messages'));
    }

    /**
     * Send message as agent.
     */
    public function sendMessage(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        try {
            $message = $this->chatService->sendMessage(
                $id,
                $request->message,
                'agent',
                Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender_name' => $message->sender_name,
                    'sender_type' => $message->sender_type,
                    'formatted_time' => $message->formatted_time,
                    'created_at' => $message->created_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Assign conversation to agent.
     */
    public function assign(Request $request, $id)
    {
        $request->validate([
            'agent_id' => 'required|exists:users,id',
        ]);

        try {
            $conversation = $this->chatService->assignToAgent($id, $request->agent_id);

            return response()->json([
                'success' => true,
                'message' => 'Conversation assigned successfully.',
                'conversation' => $conversation,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Take conversation (assign to current agent).
     */
    public function take($id)
    {
        try {
            $conversation = $this->chatService->assignToAgent($id, Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Conversation taken successfully.',
                'redirect' => route('admin.chat.conversation', $id),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Transfer conversation to another agent.
     */
    public function transfer(Request $request, $id)
    {
        $request->validate([
            'agent_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:200',
        ]);

        try {
            $conversation = $this->chatService->transferConversation(
                $id,
                $request->agent_id,
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => 'Conversation transferred successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Close conversation.
     */
    public function close(Request $request, $id)
    {
        $request->validate([
            'reason' => 'nullable|string|max:200',
        ]);

        try {
            $this->chatService->closeConversation($id, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Conversation closed successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to close conversation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update agent status.
     */
    public function updateStatus(Request $request)
    {
        $request->validate([
            'status' => 'required|in:online,away,busy,offline',
            'status_message' => 'nullable|string|max:100',
        ]);

        try {
            $agentStatus = $this->chatService->updateAgentStatus(
                Auth::id(),
                $request->status,
                $request->status_message
            );

            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully.',
                'status' => $agentStatus->status,
                'status_color' => $agentStatus->status_color,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get new messages for agent dashboard.
     */
    public function getNewMessages(Request $request)
    {
        $agentId = Auth::id();
        $lastCheck = $request->get('last_check');

        // Get conversations assigned to this agent
        $conversations = ChatConversation::where('agent_id', $agentId)
                                        ->where('status', 'active')
                                        ->pluck('id');

        // Get new messages since last check
        $query = ChatMessage::whereIn('conversation_id', $conversations)
                           ->where('sender_type', 'customer')
                           ->where('is_read', false);

        if ($lastCheck) {
            $query->where('created_at', '>', $lastCheck);
        }

        $newMessages = $query->with(['conversation.customer'])
                           ->orderBy('created_at', 'desc')
                           ->get();

        return response()->json([
            'success' => true,
            'messages' => $newMessages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'conversation_id' => $message->conversation_id,
                    'message' => $message->message,
                    'customer_name' => $message->conversation->customer_name,
                    'formatted_time' => $message->formatted_time,
                    'created_at' => $message->created_at->toISOString(),
                ];
            }),
            'count' => $newMessages->count(),
        ]);
    }

    /**
     * Get waiting conversations count.
     */
    public function getWaitingCount()
    {
        $count = $this->chatService->getWaitingCount();

        return response()->json([
            'success' => true,
            'count' => $count,
        ]);
    }
}
