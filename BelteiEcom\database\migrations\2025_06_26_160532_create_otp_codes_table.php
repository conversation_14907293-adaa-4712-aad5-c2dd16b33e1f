<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('otp_codes', function (Blueprint $table) {
            $table->id();
            $table->string('email')->index(); // Email address (not necessarily in users table)
            $table->string('code', 6); // 6-digit OTP code
            $table->enum('type', ['login', 'password_reset'])->default('login'); // Type of OTP
            $table->enum('status', ['pending', 'used', 'expired'])->default('pending');
            $table->unsignedBigInteger('user_id')->nullable(); // User ID if email exists in system
            $table->string('ip_address')->nullable(); // IP address of requester
            $table->string('user_agent')->nullable(); // User agent
            $table->timestamp('expires_at'); // When the OTP expires (1 minute)
            $table->timestamp('used_at')->nullable(); // When the OTP was used
            $table->timestamps();

            // Indexes for performance
            $table->index(['email', 'code', 'status']);
            $table->index(['expires_at', 'status']);
            $table->index('user_id');

            // Foreign key constraint (nullable)
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('otp_codes');
    }
};
