@extends('admin.layouts.app')

@section('styles')
<style>
    .admin-profile {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .admin-profile::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
    }

    .admin-profile:hover::before {
        right: -30%;
    }

    .admin-avatar-large {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        border: 4px solid rgba(255,255,255,0.3);
    }

    .admin-info {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .admin-details h2 {
        margin-bottom: 0.5rem;
        font-size: 2rem;
    }

    .admin-meta {
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .admin-badges {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .admin-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .info-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .section-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        padding: 1.5rem;
        margin: 0;
    }

    .section-body {
        padding: 2rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #e3e6f0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #2c3e50;
    }

    .info-value {
        color: #6c757d;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #fa709a;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }
</style>
@endsection

@section('content')
    <!-- Admin Profile Header -->
    <div class="admin-profile fade-in">
        <div class="admin-info">
            <div class="admin-avatar-large" @if($admin->profile_picture) style="background-image: url('{{ asset('storage/' . $admin->profile_picture) }}'); background-size: cover; background-position: center;" @endif>
                @if(!$admin->profile_picture)
                    {{ substr($admin->name, 0, 1) }}
                @endif
            </div>
            <div class="admin-details flex-grow-1">
                <h2>{{ $admin->name }}</h2>
                <div class="admin-meta">
                    <div><i class="fas fa-envelope"></i> {{ $admin->email }}</div>
                    @if($admin->phone)
                        <div><i class="fas fa-phone"></i> {{ $admin->phone }}</div>
                    @endif
                    <div><i class="fas fa-calendar"></i> Admin since {{ $admin->created_at->format('F j, Y') }}</div>
                </div>
                <div class="admin-badges">
                    <span class="admin-badge">
                        <i class="fas fa-shield-alt"></i> Administrator
                    </span>
                    @if($admin->id === auth()->id())
                        <span class="admin-badge">
                            <i class="fas fa-user"></i> Current User
                        </span>
                    @endif
                    @if($admin->created_at->diffInDays(now()) <= 7)
                        <span class="admin-badge">
                            <i class="fas fa-star"></i> New Admin
                        </span>
                    @endif
                </div>
            </div>
            <div class="action-buttons">
                <a href="{{ route('admin.admins.edit', $admin->id) }}" class="btn btn-light">
                    <i class="fas fa-edit"></i> Edit Admin
                </a>
                <a href="{{ route('admin.admins.index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left"></i> Back to Admins
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="stat-number">{{ $admin->created_at->diffInDays(now()) }}</div>
            <div class="stat-label">Days as Admin</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number">{{ \App\Models\User::where('is_admin', true)->count() }}</div>
            <div class="stat-label">Total Admins</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-user-friends"></i>
            </div>
            <div class="stat-number">{{ \App\Models\User::where('is_admin', false)->count() }}</div>
            <div class="stat-label">Total Customers</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number">{{ $admin->updated_at->diffForHumans() }}</div>
            <div class="stat-label">Last Updated</div>
        </div>
    </div>

    <!-- Admin Information -->
    <div class="info-section slide-up" style="animation-delay: 0.4s;">
        <div class="section-header">
            <h5 class="mb-0">
                <i class="fas fa-user-shield"></i> Admin Information
            </h5>
        </div>
        <div class="section-body">
            <div class="info-item">
                <div class="info-label">
                    <i class="fas fa-user"></i> Full Name
                </div>
                <div class="info-value">{{ $admin->name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">
                    <i class="fas fa-envelope"></i> Email Address
                </div>
                <div class="info-value">{{ $admin->email }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">
                    <i class="fas fa-phone"></i> Phone Number
                </div>
                <div class="info-value">{{ $admin->phone ?? 'Not provided' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">
                    <i class="fas fa-calendar-plus"></i> Created Date
                </div>
                <div class="info-value">{{ $admin->created_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">
                    <i class="fas fa-calendar-edit"></i> Last Updated
                </div>
                <div class="info-value">{{ $admin->updated_at->format('F j, Y \a\t g:i A') }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">
                    <i class="fas fa-shield-alt"></i> Admin Status
                </div>
                <div class="info-value">
                    <span class="badge badge-success">Active Administrator</span>
                </div>
            </div>
        </div>
    </div>
@endsection
