<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Services\TelegramService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TelegramController extends Controller
{
    protected $telegramService;

    public function __construct(TelegramService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    /**
     * Send order notification to Telegram
     */
    public function sendOrderNotification(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'order_id' => 'required|exists:orders,id',
                'bot_token' => 'required|string',
                'chat_id' => 'required|string',
            ]);

            // Set Telegram credentials
            $this->telegramService->setBotToken($request->bot_token);
            $this->telegramService->setChatId($request->chat_id);

            // Get order
            $order = Order::with(['user', 'orderItems.product'])->findOrFail($request->order_id);

            // Send notification
            $result = $this->telegramService->sendOrderNotification($order);

            if ($result) {
                return response()->json(['success' => true, 'message' => 'Notification sent successfully']);
            } else {
                return response()->json(['success' => false, 'message' => 'Failed to send notification'], 500);
            }
        } catch (\Exception $e) {
            Log::error('Telegram Notification Error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
