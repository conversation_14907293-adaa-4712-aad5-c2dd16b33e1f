<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Display a listing of products.
     */
    public function index()
    {
        $products = Product::with('category')->paginate(10);
        return view('admin.products.index', compact('products'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $categories = Category::all();
        return view('admin.products.create', compact('categories'));
    }

    /**
     * Store a newly created product.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->except('image');

        // Handle image upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $file->getClientOriginalExtension();
            $timestamp = time();
            $filename = $originalName . '-' . $timestamp . '.' . $extension;

            // Store with custom filename
            $imagePath = $file->storeAs('products', $filename, 'public');
            $data['image'] = $imagePath;

            // Ensure file is accessible via web (Windows XAMPP fix)
            $sourcePath = storage_path('app/public/' . $imagePath);
            $publicPath = public_path('storage/' . $imagePath);

            if (file_exists($sourcePath) && !file_exists($publicPath)) {
                // Create directory if it doesn't exist
                $publicDir = dirname($publicPath);
                if (!is_dir($publicDir)) {
                    mkdir($publicDir, 0755, true);
                }
                // Copy file to public storage
                copy($sourcePath, $publicPath);
            }

            // Debug logging
            \Log::info('Image upload debug:', [
                'original_name' => $file->getClientOriginalName(),
                'generated_filename' => $filename,
                'stored_path' => $imagePath,
                'timestamp' => $timestamp
            ]);
        }

        Product::create($data);

        return redirect()->route('admin.products.index')
                         ->with('success', 'Product created successfully!');
    }

    /**
     * Show the form for editing a product.
     */
    public function edit($id)
    {
        $product = Product::findOrFail($id);
        $categories = Category::all();

        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified product.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $product = Product::findOrFail($id);
        $data = $request->except('image');

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }

            $file = $request->file('image');
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $file->getClientOriginalExtension();
            $timestamp = time();
            $filename = $originalName . '-' . $timestamp . '.' . $extension;

            // Store with custom filename
            $imagePath = $file->storeAs('products', $filename, 'public');
            $data['image'] = $imagePath;

            // Ensure file is accessible via web (Windows XAMPP fix)
            $sourcePath = storage_path('app/public/' . $imagePath);
            $publicPath = public_path('storage/' . $imagePath);

            if (file_exists($sourcePath) && !file_exists($publicPath)) {
                // Create directory if it doesn't exist
                $publicDir = dirname($publicPath);
                if (!is_dir($publicDir)) {
                    mkdir($publicDir, 0755, true);
                }
                // Copy file to public storage
                copy($sourcePath, $publicPath);
            }

            // Debug logging
            \Log::info('Image update debug:', [
                'product_id' => $product->id,
                'original_name' => $file->getClientOriginalName(),
                'generated_filename' => $filename,
                'stored_path' => $imagePath,
                'timestamp' => $timestamp
            ]);
        }

        $product->update($data);

        return redirect()->route('admin.products.index')
                         ->with('success', 'Product updated successfully!');
    }

    /**
     * Remove the specified product.
     */
    public function destroy($id)
    {
        $product = Product::findOrFail($id);

        // Delete product image if exists
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        $product->delete();

        return redirect()->route('admin.products.index')
                         ->with('success', 'Product deleted successfully!');
    }
}
