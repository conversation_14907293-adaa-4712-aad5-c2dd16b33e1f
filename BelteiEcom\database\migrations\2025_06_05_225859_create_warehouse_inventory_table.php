<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(0);
            $table->integer('reserved_quantity')->default(0); // Reserved for pending orders
            $table->integer('available_quantity')->storedAs('quantity - reserved_quantity');
            $table->integer('reorder_level')->default(10); // Minimum stock level
            $table->integer('max_stock_level')->nullable(); // Maximum stock level
            $table->decimal('cost_price', 10, 2)->nullable(); // Cost price at this warehouse
            $table->string('location_code')->nullable(); // Specific location within warehouse (e.g., A1-B2)
            $table->date('last_restocked_at')->nullable();
            $table->timestamps();

            // Ensure unique product per warehouse
            $table->unique(['warehouse_id', 'product_id']);

            // Indexes for performance
            $table->index(['product_id', 'quantity']);
            $table->index(['warehouse_id', 'quantity']);
            $table->index(['quantity', 'reorder_level']);
            $table->index('available_quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_inventory');
    }
};
