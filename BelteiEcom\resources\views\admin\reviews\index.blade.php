@extends('admin.layouts.app')

@section('title', 'Review Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-star"></i> Review Management
                    </h3>
                </div>

                <!-- Filters -->
                <div class="card-body">
                    <form method="GET" class="row mb-4">
                        <div class="col-md-3">
                            <select name="status" class="form-control">
                                <option value="">All Reviews</option>
                                <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="rating" class="form-control">
                                <option value="">All Ratings</option>
                                @for($i = 5; $i >= 1; $i--)
                                    <option value="{{ $i }}" {{ request('rating') == $i ? 'selected' : '' }}>{{ $i }} Stars</option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" name="search" class="form-control" placeholder="Search reviews..." value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </form>

                    <!-- Reviews Table -->
                    @if($reviews->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>User</th>
                                        <th>Rating</th>
                                        <th>Review</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($reviews as $review)
                                        <tr class="{{ $review->is_featured ? 'table-warning' : '' }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($review->product->image)
                                                        <img src="{{ asset('storage/' . $review->product->image) }}" 
                                                             alt="{{ $review->product->name }}" 
                                                             style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px; margin-right: 10px;">
                                                    @endif
                                                    <div>
                                                        <strong>{{ $review->product->name }}</strong><br>
                                                        <small class="text-muted">${{ number_format($review->product->price, 2) }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <strong>{{ $review->user->name }}</strong>
                                                        {!! $review->user->badge_display !!}
                                                        @if($review->user->is_banned)
                                                            <span class="badge badge-danger">Banned</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {!! $review->stars_html !!}
                                                    <span class="ml-2">{{ $review->rating }}/5</span>
                                                </div>
                                            </td>
                                            <td>
                                                @if($review->title)
                                                    <strong>{{ Str::limit($review->title, 30) }}</strong><br>
                                                @endif
                                                <small>{{ Str::limit($review->comment, 50) }}</small>
                                                @if($review->images->count() > 0)
                                                    <br><small class="text-info">
                                                        <i class="fas fa-image"></i> {{ $review->images->count() }} image(s)
                                                    </small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($review->is_approved)
                                                    <span class="badge badge-success">Approved</span>
                                                @else
                                                    <span class="badge badge-warning">Pending</span>
                                                @endif
                                                @if($review->is_featured)
                                                    <br><span class="badge badge-info">Featured</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $review->created_at->format('M j, Y') }}</small><br>
                                                <small class="text-muted">{{ $review->created_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    @if(!$review->is_approved)
                                                        <form action="{{ route('admin.reviews.approve', $review) }}" method="POST" style="display: inline;">
                                                            @csrf
                                                            @method('PATCH')
                                                            <button type="submit" class="btn btn-success btn-sm">
                                                                <i class="fas fa-check"></i> Approve
                                                            </button>
                                                        </form>
                                                    @else
                                                        <form action="{{ route('admin.reviews.reject', $review) }}" method="POST" style="display: inline;">
                                                            @csrf
                                                            @method('PATCH')
                                                            <button type="submit" class="btn btn-warning btn-sm">
                                                                <i class="fas fa-times"></i> Reject
                                                            </button>
                                                        </form>
                                                    @endif

                                                    <form action="{{ route('admin.reviews.toggle-featured', $review) }}" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('PATCH')
                                                        <button type="submit" class="btn btn-info btn-sm">
                                                            @if($review->is_featured)
                                                                <i class="far fa-star"></i> Unfeature
                                                            @else
                                                                <i class="fas fa-star"></i> Feature
                                                            @endif
                                                        </button>
                                                    </form>

                                                    @if(!$review->user->is_banned)
                                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#banModal{{ $review->user->id }}">
                                                            <i class="fas fa-ban"></i> Ban User
                                                        </button>
                                                    @else
                                                        <form action="{{ route('admin.users.unban', $review->user) }}" method="POST" style="display: inline;">
                                                            @csrf
                                                            @method('PATCH')
                                                            <button type="submit" class="btn btn-success btn-sm">
                                                                <i class="fas fa-check"></i> Unban
                                                            </button>
                                                        </form>
                                                    @endif

                                                    <form action="{{ route('admin.reviews.destroy', $review) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Ban User Modal -->
                                        <div class="modal fade" id="banModal{{ $review->user->id }}" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Ban User: {{ $review->user->name }}</h5>
                                                        <button type="button" class="close" data-dismiss="modal">
                                                            <span>&times;</span>
                                                        </button>
                                                    </div>
                                                    <form action="{{ route('admin.users.ban', $review->user) }}" method="POST">
                                                        @csrf
                                                        @method('PATCH')
                                                        <div class="modal-body">
                                                            <div class="form-group">
                                                                <label>Ban Reason *</label>
                                                                <textarea name="ban_reason" class="form-control" rows="3" required placeholder="Reason for banning this user..."></textarea>
                                                            </div>
                                                            <div class="form-group">
                                                                <label>Ban Duration (days)</label>
                                                                <input type="number" name="ban_duration" class="form-control" placeholder="Leave empty for permanent ban">
                                                                <small class="text-muted">Leave empty for permanent ban</small>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-danger">Ban User</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $reviews->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No reviews found</h5>
                            <p class="text-muted">Reviews will appear here once customers start reviewing products.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
