@extends('layouts.app')

@section('title', 'QR Login Error')

@section('styles')
<style>
    .qr-error-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .qr-error-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 500px;
        width: 100%;
        text-align: center;
    }

    .qr-error-icon {
        font-size: 4rem;
        color: #ff6b6b;
        margin-bottom: 1rem;
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .qr-error-title {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
    }

    .qr-error-message {
        color: #666;
        font-size: 1.1rem;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .qr-error-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: transparent;
        color: #666;
        border: 2px solid #e0e0e0;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        border-color: #4ecdc4;
        color: #4ecdc4;
        text-decoration: none;
    }

    .error-details {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }

    .error-details h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .error-details ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
    }

    .error-details li {
        margin-bottom: 0.5rem;
    }
</style>
@endsection

@section('content')
<div class="qr-error-container">
    <div class="qr-error-card">
        <div class="qr-error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="qr-error-title">QR Login Error</h1>
        
        <p class="qr-error-message">
            {{ $message }}
        </p>

        <div class="error-details">
            <h4><i class="fas fa-info-circle"></i> What you can do:</h4>
            <ul>
                <li>Try scanning a new QR code from the login page</li>
                <li>Make sure you're logged in to your account</li>
                <li>Check that the QR code hasn't expired (valid for 5 minutes)</li>
                <li>Contact support if the problem persists</li>
            </ul>
        </div>

        <div class="qr-error-actions">
            <a href="{{ route('login') }}" class="btn-primary">
                <i class="fas fa-sign-in-alt"></i> Go to Login Page
            </a>
            <a href="{{ route('home') }}" class="btn-secondary">
                <i class="fas fa-home"></i> Back to Home
            </a>
        </div>
    </div>
</div>
@endsection
