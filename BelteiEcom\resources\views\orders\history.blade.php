@extends('layouts.app')

@section('title', 'Order History')

@section('content')
<div class="orders-page">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-text">
                <h1 class="page-title">
                    <i class="fas fa-history"></i>
                    Order History
                </h1>
                <p class="page-subtitle">Track and manage all your orders in one place</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('products.index') }}" class="btn btn-primary">
                    <i class="fas fa-shopping-bag"></i>
                    Continue Shopping
                </a>
            </div>
        </div>
    </div>

    @if($orders->isEmpty())
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <h3 class="empty-state-title">No Orders Yet</h3>
                <p class="empty-state-description">
                    You haven't placed any orders yet. Start shopping to see your order history here.
                </p>
                <div class="empty-state-actions">
                    <a href="{{ route('products.index') }}" class="btn btn-primary">
                        <i class="fas fa-shopping-bag"></i>
                        Browse Products
                    </a>
                </div>
            </div>
        </div>
    @else
        <!-- Orders Grid -->
        <div class="orders-grid">
            @foreach($orders as $order)
                <div class="order-card">
                    <div class="order-card-header">
                        <div class="order-info">
                            <h3 class="order-number">
                                <a href="{{ route('orders.show', $order->id) }}">
                                    Order #{{ $order->id }}
                                </a>
                            </h3>
                            <p class="order-date">
                                <i class="fas fa-calendar-alt"></i>
                                {{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('M j, Y') }} (Cambodia Time)
                            </p>
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-{{ $order->status }}">
                                <i class="fas
                                    @if($order->status == 'pending_payment') fa-credit-card
                                    @elseif($order->status == 'new') fa-clock
                                    @elseif($order->status == 'processing') fa-cog
                                    @elseif($order->status == 'shipped') fa-truck
                                    @elseif($order->status == 'delivered') fa-check-circle
                                    @elseif($order->status == 'cancelled') fa-times-circle
                                    @endif
                                "></i>
                                {{ $order->status == 'pending_payment' ? 'Pending Payment' : ucfirst($order->status) }}
                            </span>
                        </div>
                    </div>

                    <div class="order-card-body">
                        <!-- Order Items Preview -->
                        <div class="order-items-preview">
                            @php
                                $itemCount = $order->orderItems->count();
                                $displayItems = $order->orderItems->take(3);
                            @endphp

                            <div class="items-images">
                                @foreach($displayItems as $item)
                                    <div class="item-image">
                                        @if($item->product->image)
                                            <img src="{{ asset('storage/' . $item->product->image) }}"
                                                 alt="{{ $item->product->name }}"
                                                 title="{{ $item->product->name }}">
                                        @else
                                            <div class="no-image">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach

                                @if($itemCount > 3)
                                    <div class="more-items">
                                        <span>+{{ $itemCount - 3 }}</span>
                                    </div>
                                @endif
                            </div>

                            <div class="items-summary">
                                <p class="items-count">
                                    {{ $itemCount }} {{ Str::plural('item', $itemCount) }}
                                </p>
                                <p class="order-total">
                                    ${{ number_format($order->total_amount, 2) }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="order-card-footer">
                        <div class="order-actions">
                            <a href="{{ route('orders.show', $order->id) }}" class="btn btn-outline">
                                <i class="fas fa-eye"></i>
                                View Details
                            </a>
                            <a href="{{ route('orders.receipt.download', $order->pdf_token) }}" class="btn btn-pdf">
                                <i class="fas fa-file-pdf"></i>
                                Download PDF
                            </a>
                            @if($order->status == 'delivered')
                                <button class="btn btn-secondary" onclick="reorderItems({{ $order->id }})">
                                    <i class="fas fa-redo"></i>
                                    Reorder
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($orders->hasPages())
            <div class="pagination-wrapper">
                {{ $orders->links() }}
            </div>
        @endif
    @endif
</div>
@endsection

@section('styles')
<style>
    /* Orders Page Styles */
    .orders-page {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        margin-bottom: 3rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateY(0px) rotate(0deg); }
        100% { transform: translateY(-20px) rotate(360deg); }
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-title i {
        font-size: 2rem;
        opacity: 0.9;
    }

    .page-subtitle {
        font-size: 1.1rem;
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }

    .header-actions .btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .header-actions .btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 2rem;
        opacity: 0.7;
    }

    .empty-state-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 1rem;
    }

    .empty-state-description {
        font-size: 1.1rem;
        color: #636e72;
        margin-bottom: 2rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Orders Grid */
    .orders-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    /* Order Card */
    .order-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid rgba(255,255,255,0.2);
    }

    .order-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .order-card-header {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .order-number {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 700;
    }

    .order-number a {
        color: #667eea;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .order-number a:hover {
        color: #764ba2;
    }

    .order-date {
        margin: 0.5rem 0 0 0;
        color: #636e72;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .order-date i {
        opacity: 0.7;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending_payment {
        background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%);
        color: white;
    }

    .status-new {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }

    .status-processing {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
    }

    .status-shipped {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
    }

    .status-delivered {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        color: white;
    }

    /* Order Card Body */
    .order-card-body {
        padding: 1.5rem;
    }

    .order-items-preview {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .items-images {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .item-image {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        overflow: hidden;
        border: 2px solid #f1f3f4;
        position: relative;
    }

    .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #636e72;
    }

    .more-items {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .items-summary {
        text-align: right;
    }

    .items-count {
        margin: 0;
        color: #636e72;
        font-size: 0.9rem;
    }

    .order-total {
        margin: 0.25rem 0 0 0;
        font-size: 1.3rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Order Card Footer */
    .order-card-footer {
        padding: 1.5rem;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }

    .order-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        color: white;
        border: 2px solid transparent;
    }

    .btn-secondary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .btn-pdf {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        color: white;
        border: 2px solid transparent;
    }

    .btn-pdf:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        gap: 0.5rem;
    }

    .pagination li a, .pagination li span {
        display: block;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        color: #667eea;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .pagination li a:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
    }

    .pagination li.active span {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }

    .pagination li.disabled span {
        color: #6c757d;
        cursor: not-allowed;
        opacity: 0.5;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .orders-page {
            padding: 1rem;
        }

        .page-header {
            padding: 2rem 1.5rem;
            margin-bottom: 2rem;
        }

        .header-content {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .page-title {
            font-size: 2rem;
        }

        .orders-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .order-card-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .order-actions {
            flex-direction: column;
        }

        .order-actions .btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .page-title {
            font-size: 1.8rem;
        }

        .order-card-header,
        .order-card-body,
        .order-card-footer {
            padding: 1rem;
        }

        .items-images {
            flex-wrap: wrap;
        }

        .order-items-preview {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .items-summary {
            text-align: left;
            width: 100%;
        }
    }
</style>

<script>
function reorderItems(orderId) {
    // Add reorder functionality here
    alert('Reorder functionality will be implemented soon!');
}
</script>
@endsection
