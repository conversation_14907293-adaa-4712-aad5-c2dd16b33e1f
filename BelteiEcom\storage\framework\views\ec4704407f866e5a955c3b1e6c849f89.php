<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>QR Login Test - BelteiEcom</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }

        .test-header p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid #e9ecef;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .qr-display {
            text-align: center;
            margin: 2rem 0;
        }

        .qr-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #ccc;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            background: white;
        }

        .qr-placeholder i {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 1rem;
        }

        .qr-code-img {
            width: 200px;
            height: 200px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            margin: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        .status-display {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #4ecdc4;
        }

        .status-display.error {
            border-left-color: #ff6b6b;
        }

        .status-display.success {
            border-left-color: #28a745;
        }

        .code-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 1.2rem;
            text-align: center;
            margin: 1rem 0;
            letter-spacing: 0.2rem;
        }

        .instructions {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .instructions h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }

        .instructions ol {
            color: #333;
            line-height: 1.6;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .test-container {
                padding: 1rem;
            }
            
            .test-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-qrcode"></i> QR Login System Test</h1>
            <p>Test the complete QR code login flow for BelteiEcom. This demonstrates how users can log in by scanning QR codes with their mobile devices.</p>
        </div>

        <div class="test-grid">
            <!-- QR Code Generation Section -->
            <div class="test-section">
                <h2><i class="fas fa-desktop"></i> Desktop Login</h2>
                <p>This simulates the login page where users can generate QR codes to log in.</p>
                
                <div class="qr-display">
                    <div id="qr-placeholder" class="qr-placeholder">
                        <i class="fas fa-qrcode"></i>
                        <p>Click "Generate QR Code" to start</p>
                    </div>
                    <img id="qr-code-image" class="qr-code-img" style="display: none;" alt="QR Code">
                </div>

                <div style="text-align: center;">
                    <button id="generate-btn" class="btn" onclick="generateQR()">
                        <i class="fas fa-qrcode"></i> Generate QR Code
                    </button>
                    <button id="refresh-btn" class="btn btn-secondary" onclick="refreshQR()" style="display: none;">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>

                <div id="one-time-code" class="code-display" style="display: none;">
                    <div>One-time code: <span id="code-value"></span></div>
                </div>

                <div id="status" class="status-display">
                    <strong>Status:</strong> <span id="status-text">Ready to generate QR code</span>
                </div>
            </div>

            <!-- Mobile Scanner Section -->
            <div class="test-section">
                <h2><i class="fas fa-mobile-alt"></i> Mobile Scanner</h2>
                <p>Use these links to test the mobile scanning experience.</p>

                <div style="text-align: center; margin: 2rem 0;">
                    <a href="<?php echo e(route('profile.qr-scanner')); ?>" class="btn" target="_blank">
                        <i class="fas fa-camera"></i> Open QR Scanner
                    </a>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-secondary" target="_blank">
                        <i class="fas fa-sign-in-alt"></i> Login Page
                    </a>
                </div>

                <div class="instructions">
                    <h3>Testing Instructions:</h3>
                    <ol>
                        <li>Click "Generate QR Code" on the left</li>
                        <li>Open "QR Scanner" in a new tab/window</li>
                        <li>Click "Start Camera" in the scanner</li>
                        <li>Use the "Test" button to manually input the QR URL</li>
                        <li>Or scan the QR code with your phone camera</li>
                        <li>Confirm the login on the scanning device</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-info-circle"></i> How QR Login Works:</h3>
            <ol>
                <li><strong>Generate:</strong> User visits login page and generates a QR code</li>
                <li><strong>Scan:</strong> User scans QR code with their authenticated mobile device</li>
                <li><strong>Confirm:</strong> Mobile device shows confirmation screen</li>
                <li><strong>Authenticate:</strong> User confirms and the original device is logged in</li>
            </ol>
        </div>
    </div>

    <script>
        let currentSession = null;
        let statusPoller = null;

        async function generateQR() {
            try {
                updateStatus('Generating QR code...', 'info');
                
                const response = await fetch('<?php echo e(route("qr-login.generate")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    currentSession = data;
                    
                    // Show QR code
                    document.getElementById('qr-placeholder').style.display = 'none';
                    document.getElementById('qr-code-image').style.display = 'block';
                    document.getElementById('qr-code-image').src = data.qr_image;
                    
                    // Show one-time code
                    document.getElementById('one-time-code').style.display = 'block';
                    document.getElementById('code-value').textContent = data.one_time_code;
                    
                    // Update buttons
                    document.getElementById('generate-btn').style.display = 'none';
                    document.getElementById('refresh-btn').style.display = 'inline-flex';
                    
                    updateStatus('QR code generated! Scan with your mobile device.', 'success');
                    
                    // Start polling for status
                    startPolling(data.token);
                } else {
                    updateStatus('Failed to generate QR code', 'error');
                }
            } catch (error) {
                updateStatus('Network error occurred', 'error');
            }
        }

        function refreshQR() {
            if (statusPoller) {
                clearInterval(statusPoller);
            }
            
            // Reset UI
            document.getElementById('qr-placeholder').style.display = 'flex';
            document.getElementById('qr-code-image').style.display = 'none';
            document.getElementById('one-time-code').style.display = 'none';
            document.getElementById('generate-btn').style.display = 'inline-flex';
            document.getElementById('refresh-btn').style.display = 'none';
            
            currentSession = null;
            updateStatus('Ready to generate QR code', 'info');
        }

        function startPolling(token) {
            statusPoller = setInterval(async () => {
                try {
                    const response = await fetch('<?php echo e(route("qr-login.status")); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        body: JSON.stringify({ token })
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        if (data.status === 'scanned') {
                            updateStatus(`Scanned by ${data.user.name}. Waiting for confirmation...`, 'info');
                        } else if (data.status === 'authenticated') {
                            updateStatus('Authentication successful! You would be logged in now.', 'success');
                            clearInterval(statusPoller);
                        }
                    }
                } catch (error) {
                    console.error('Polling error:', error);
                }
            }, 2000);
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            
            statusDiv.className = `status-display ${type}`;
            statusText.textContent = message;
        }

        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            if (statusPoller) {
                clearInterval(statusPoller);
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/test-qr-login.blade.php ENDPATH**/ ?>