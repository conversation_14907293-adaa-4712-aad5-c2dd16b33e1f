@extends('layouts.app')

@section('title', 'Checkout')

@section('styles')
<link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
<style>
    .checkout-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .checkout-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .checkout-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .checkout-header p {
        color: #666;
        font-size: 1.1rem;
    }

    .checkout-progress {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 3rem;
        gap: 1rem;
    }

    .progress-step {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .progress-step.completed {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .progress-step.active {
        background: #f8f9ff;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .progress-step.pending {
        background: #f8f9fa;
        color: #666;
        border: 2px solid #e9ecef;
    }

    .progress-arrow {
        color: #ccc;
        font-size: 1.2rem;
    }

    .checkout-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 3rem;
        align-items: start;
    }

    .checkout-form {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-section {
        padding: 2rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #333;
        margin: 0;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-input {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .form-input.is-invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    .location-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .location-btn {
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        background: white;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-align: center;
    }

    .location-btn:hover {
        border-color: #667eea;
        background: #f8f9ff;
        transform: translateY(-1px);
    }

    .location-btn.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .payment-methods {
        display: grid;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .payment-option {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .payment-option:hover {
        border-color: #667eea;
        background: #f8f9ff;
    }

    .payment-option.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    }

    .payment-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }

    .payment-radio {
        width: 20px;
        height: 20px;
        border: 2px solid #e9ecef;
        border-radius: 50%;
        position: relative;
        transition: all 0.3s ease;
    }

    .payment-option.selected .payment-radio {
        border-color: #667eea;
        background: #667eea;
    }

    .payment-option.selected .payment-radio::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
    }

    .payment-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .payment-info h4 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
    }

    .payment-info p {
        margin: 0;
        font-size: 0.9rem;
        color: #666;
    }

    .order-summary {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .summary-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        text-align: center;
    }

    .summary-header h3 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 700;
    }

    .summary-content {
        padding: 1.5rem;
    }

    .order-item {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .order-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .item-image {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        overflow: hidden;
        flex-shrink: 0;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .item-details {
        flex: 1;
    }

    .item-name {
        font-weight: 700;
        color: #333;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .item-quantity-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: #666;
    }

    .item-total {
        font-weight: 700;
        color: #667eea;
    }

    .summary-totals {
        border-top: 2px solid #f0f0f0;
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }

    .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
    }

    .total-row.final {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        border-top: 2px solid #667eea;
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .checkout-actions {
        padding: 1.5rem;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .btn-back {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: white;
        color: #667eea;
        text-decoration: none;
        border-radius: 10px;
        border: 2px solid #667eea;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-place-order {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.875rem 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        font-weight: 700;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-place-order:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .address-search-container {
        position: relative;
        margin-bottom: 1rem;
    }

    .address-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2px solid #667eea;
        border-top: none;
        border-radius: 0 0 10px 10px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .address-suggestion {
        padding: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f1f1;
        transition: background 0.2s ease;
    }

    .address-suggestion:hover {
        background: #f8f9ff;
    }

    .address-suggestion:last-child {
        border-bottom: none;
    }

    .suggestion-main {
        font-weight: 600;
        color: #333;
    }

    .suggestion-secondary {
        font-size: 0.85rem;
        color: #666;
        margin-top: 0.2rem;
    }

    #map-container {
        height: 300px;
        border-radius: 10px;
        margin-bottom: 1rem;
        display: none;
        border: 2px solid #e9ecef;
        overflow: hidden;
    }

    .location-status {
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        display: none;
        font-weight: 600;
    }

    .location-status.success {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(34, 139, 34, 0.1) 100%);
        color: #155724;
        border: 2px solid #28a745;
    }

    .location-status.error {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);
        color: #721c24;
        border: 2px solid #dc3545;
    }

    .location-status.info {
        background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(13, 110, 253, 0.1) 100%);
        color: #0c5460;
        border: 2px solid #17a2b8;
    }

    .current-location-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        display: none;
        border: 2px solid #667eea;
    }

    .loading-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .checkout-container {
            padding: 1rem;
        }

        .checkout-header h1 {
            font-size: 2rem;
        }

        .checkout-progress {
            flex-direction: column;
            gap: 0.5rem;
        }

        .progress-step {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }

        .checkout-content {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .location-options {
            grid-template-columns: 1fr 1fr;
        }

        .location-btn {
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .section-icon {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }

        .section-title {
            font-size: 1.1rem;
        }

        .checkout-actions {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-back,
        .btn-place-order {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .checkout-header h1 {
            font-size: 1.75rem;
        }

        .location-options {
            grid-template-columns: 1fr;
        }

        .payment-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .order-item {
            flex-direction: column;
            text-align: center;
        }

        .item-image {
            align-self: center;
        }
    }

    /* Cash on Delivery Confirmation Modal */
    .cod-confirmation-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .cod-confirmation-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .cod-confirmation-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(5px);
    }

    .cod-confirmation-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        transition: transform 0.3s ease;
    }

    .cod-confirmation-modal.show .cod-confirmation-content {
        transform: translate(-50%, -50%) scale(1);
    }

    .cod-confirmation-header {
        background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        border-radius: 20px 20px 0 0;
    }

    .cod-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .cod-confirmation-header h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
    }

    .cod-confirmation-body {
        padding: 2rem;
    }

    .cod-confirmation-body p {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
        text-align: center;
        color: #2c3e50;
    }

    .cod-details {
        background: #f8f9fc;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .cod-detail-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .cod-detail-item:last-child {
        margin-bottom: 0;
    }

    .cod-detail-item i {
        color: #ff6b35;
        font-size: 1.2rem;
        width: 20px;
        text-align: center;
    }

    .cod-detail-item span {
        color: #2c3e50;
        font-weight: 500;
    }

    .cod-confirmation-actions {
        padding: 0 2rem 2rem;
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    .cod-btn-cancel,
    .cod-btn-confirm {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }

    .cod-btn-cancel {
        background: #6c757d;
        color: white;
    }

    .cod-btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .cod-btn-confirm {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .cod-btn-confirm:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(67, 233, 123, 0.4);
    }

    @media (max-width: 768px) {
        .cod-confirmation-content {
            width: 95%;
            margin: 1rem;
        }

        .cod-confirmation-header {
            padding: 1.5rem;
        }

        .cod-confirmation-body {
            padding: 1.5rem;
        }

        .cod-confirmation-actions {
            flex-direction: column;
            padding: 0 1.5rem 1.5rem;
        }

        .cod-btn-cancel,
        .cod-btn-confirm {
            width: 100%;
            justify-content: center;
        }
    }

    /* Final COD Confirmation Modal */
    .final-cod-confirmation-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10001;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .final-cod-confirmation-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .final-cod-confirmation-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(8px);
    }

    .final-cod-confirmation-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        background: white;
        border-radius: 20px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
        max-width: 550px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        transition: transform 0.3s ease;
    }

    .final-cod-confirmation-modal.show .final-cod-confirmation-content {
        transform: translate(-50%, -50%) scale(1);
    }

    .final-cod-confirmation-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        border-radius: 20px 20px 0 0;
    }

    .final-cod-icon {
        font-size: 3.5rem;
        margin-bottom: 1rem;
        display: block;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .final-cod-confirmation-header h3 {
        margin: 0;
        font-size: 1.6rem;
        font-weight: 700;
    }

    .final-cod-confirmation-body {
        padding: 2rem;
    }

    .final-cod-confirmation-body p {
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
        text-align: center;
        color: #dc3545;
        font-weight: 600;
    }

    .final-cod-warning {
        background: #fff3cd;
        border: 2px solid #ffeaa7;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .warning-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: white;
        border-radius: 10px;
        border-left: 4px solid #dc3545;
    }

    .warning-item:last-child {
        margin-bottom: 0;
    }

    .warning-item i {
        color: #dc3545;
        font-size: 1.2rem;
        width: 20px;
        text-align: center;
    }

    .warning-item span {
        color: #2c3e50;
        font-weight: 500;
    }

    .final-cod-note {
        background: #d1ecf1;
        border: 2px solid #bee5eb;
        border-radius: 10px;
        padding: 1rem;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        color: #0c5460;
        font-weight: 500;
    }

    .final-cod-note i {
        color: #17a2b8;
        margin-top: 0.2rem;
    }

    .final-cod-confirmation-actions {
        padding: 0 2rem 2rem;
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    .final-cod-btn-cancel,
    .final-cod-btn-confirm {
        padding: 1rem 2rem;
        border: none;
        border-radius: 10px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;
    }

    .final-cod-btn-cancel {
        background: #6c757d;
        color: white;
    }

    .final-cod-btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .final-cod-btn-confirm {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }

    .final-cod-btn-confirm:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
    }

    @media (max-width: 768px) {
        .final-cod-confirmation-content {
            width: 95%;
            margin: 1rem;
        }

        .final-cod-confirmation-header {
            padding: 1.5rem;
        }

        .final-cod-confirmation-body {
            padding: 1.5rem;
        }

        .final-cod-confirmation-actions {
            flex-direction: column;
            padding: 0 1.5rem 1.5rem;
        }

        .final-cod-btn-cancel,
        .final-cod-btn-confirm {
            width: 100%;
            justify-content: center;
        }
    }



    /* Add only the necessary Bakong styles inline to avoid conflicts */
    .qrcode-container {
        width: 100%;
        display: flex;
        overflow: auto;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background-color: rgba(217, 217, 217, 0);
    }

    .qrcode-body {
        width: 252px;
        height: 351px;
        margin: auto;
        box-shadow: 0px 0px 20px 10px rgba(0, 0, 0, 0.12);
        background-color: #ffffff;
        position: relative;
    }

    .qrcode-loadingpic {
        top: -43px;
        right: 61px;
        width: 25px;
        position: absolute;
        object-fit: cover;
    }

    .qrcode-minutes {
        top: -37px;
        right: 18px;
        position: absolute;
        font-style: normal;
        font-weight: 700;
    }

    .qrcode-name {
        top: 55px;
        left: 19px;
        position: absolute;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
    }

    .qrcode-currency {
        top: 70px;
        left: 18px;
        position: absolute;
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
    }

    .qrcode-amount {
        top: 70px;
        left: 31px;
        position: absolute;
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
    }

    .qrcode-head {
        flex: 0 0 auto;
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        flex-direction: column;
    }

    .qrcode-header {
        flex: 0 0 auto;
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        flex-direction: column;
    }

    .qrcode-container1 {
        flex: 0 0 auto;
        width: 100%;
        height: auto;
        display: flex;
        position: relative;
        align-items: center;
        flex-direction: column;
    }

    .qrcode-container2 {
        right: 0px;
        width: 28px;
        bottom: -19px;
        height: 23px;
        position: absolute;
        border-top: 0px solid transparent;
        border-right: 20px solid #E1232E;
        border-bottom: 25px solid transparent;
    }

    .qrcode-container3 {
        flex: 0 0 auto;
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        flex-direction: column;
    }

    .qrcode-container4 {
        height: 36px;
        flex: 0 0 auto;
        width: 100%;
        display: flex;
        position: relative;
        align-items: flex-start;
        border-radius: 4px;
        background-color: #E1232E;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
    }

    .qrcode-image {
        top: 0px;
        left: 0px;
        right: 0px;
        width: 48px;
        bottom: 0px;
        height: auto;
        margin: auto;
        position: absolute;
        object-fit: cover;
    }

    .qrcode-line {
        top: 99px;
        flex: 0 0 auto;
        left: 0px;
        right: 0px;
        width: 100%;
        height: 0px;
        margin: auto;
        display: flex;
        position: absolute;
        align-items: flex-start;
        border-color: #dadada;
        border-style: dotted;
        border-top-width: 1px;
        border-left-width: 0px;
        border-right-width: 0px;
        border-bottom-width: 0px;
    }

    .qrcode-qrcode {
        left: 0px;
        right: 0px;
        margin: auto;
        align-items: flex-start;
        justify-content: flex-start;
    }

    .qrcode-qr {
        top: 0px;
        left: 0px;
        right: 0px;
        width: 100%;
        bottom: 0px;
        height: 100%;
        margin: auto;
        position: absolute;
        object-fit: cover;
    }

    .qrcode-logo {
        top: 0px;
        left: 0px;
        right: 0px;
        width: 40px;
        bottom: 0px;
        height: auto;
        margin: auto;
        position: absolute;
        object-fit: cover;
    }

    .qrcode-banklogo {
        left: 0px;
        right: 0px;
        width: 96px;
        bottom: -40px;
        height: auto;
        margin: auto;
        position: absolute;
        object-fit: cover;
    }
</style>
@endsection

@section('content')
<div class="checkout-container">
    <!-- Checkout Header -->
    <div class="checkout-header">
        <h1><i class="fas fa-shopping-cart"></i> Secure Checkout</h1>
        <p>Complete your order safely and securely</p>
    </div>

    <!-- Progress Steps -->
    <div class="checkout-progress">
        <div class="progress-step completed">
            <i class="fas fa-shopping-cart"></i>
            <span>Cart</span>
        </div>
        <i class="fas fa-chevron-right progress-arrow"></i>
        <div class="progress-step active">
            <i class="fas fa-credit-card"></i>
            <span>Checkout</span>
        </div>
        <i class="fas fa-chevron-right progress-arrow"></i>
        <div class="progress-step pending">
            <i class="fas fa-check-circle"></i>
            <span>Complete</span>
        </div>
    </div>

    <!-- Checkout Content -->
    <div class="checkout-content">
        <!-- Checkout Form -->
        <div class="checkout-form">
            <form action="{{ route('orders.place') }}" method="POST" id="checkout-form">
                @csrf

                <!-- Contact Information Section -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 class="section-title">Contact Information</h3>
                    </div>

                    <div class="form-group">
                        <label for="phone_number" class="form-label">
                            <i class="fas fa-phone"></i> Phone Number
                        </label>
                        <input type="text" name="phone_number" id="phone_number"
                               class="form-input @error('phone_number') is-invalid @enderror"
                               value="{{ old('phone_number', Auth::user()->phone) }}"
                               placeholder="Enter your phone number" required>
                        @error('phone_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Shipping Address Section -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h3 class="section-title">Shipping Address</h3>
                    </div>

                    <div class="location-options">
                        <button type="button" class="location-btn" onclick="getCurrentLocation()">
                            <i class="fas fa-crosshairs"></i> Current Location
                        </button>
                        <button type="button" class="location-btn" onclick="toggleMapSearch()">
                            <i class="fas fa-map"></i> Search on Map
                        </button>
                        <button type="button" class="location-btn" onclick="toggleManualEntry()">
                            <i class="fas fa-edit"></i> Enter Manually
                        </button>
                    </div>

                    <div class="location-status" id="location-status"></div>
                    <div class="current-location-info" id="current-location-info"></div>

                    <div class="address-search-container" id="address-search-container" style="display: none;">
                        <input type="text" id="address-search" class="form-input" placeholder="Search for an address...">
                        <div class="address-suggestions" id="address-suggestions"></div>
                    </div>

                    <div id="map-container"></div>

                    <div class="form-group">
                        <label for="shipping_address" class="form-label">
                            <i class="fas fa-home"></i> Delivery Address
                        </label>
                        <textarea name="shipping_address" id="shipping_address"
                                  class="form-input @error('shipping_address') is-invalid @enderror"
                                  rows="3"
                                  placeholder="Enter your complete delivery address"
                                  required>{{ old('shipping_address') }}</textarea>
                        @error('shipping_address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Payment Method Section -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <h3 class="section-title">Payment Method</h3>
                    </div>

                    <div class="payment-methods">
                        <div class="payment-option selected" onclick="selectPayment('khqr')">
                            <div class="payment-header">
                                <div class="payment-radio"></div>
                                <div class="payment-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="payment-info">
                                    <h4>Bakong Payment</h4>
                                    <p>Pay securely with Bakong mobile payment</p>
                                </div>
                            </div>
                        </div>

                        <div class="payment-option" onclick="selectPayment('cash_on_delivery')">
                            <div class="payment-header">
                                <div class="payment-radio"></div>
                                <div class="payment-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="payment-info">
                                    <h4>Cash on Delivery</h4>
                                    <p>Pay with cash when your order arrives</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" name="payment_method" id="payment_method" value="khqr">

                    <!-- Hidden fields for coordinates -->
                    <input type="hidden" id="latitude" name="latitude" value="">
                    <input type="hidden" id="longitude" name="longitude" value="">
                </div>

                <!-- Order Actions -->
                <div class="checkout-actions">
                    <a href="{{ route('cart.index') }}" class="btn-back">
                        <i class="fas fa-arrow-left"></i> Back to Cart
                    </a>
                    <button type="submit" class="btn-place-order" onclick="console.log('Button clicked!'); return confirmOrderPlacement(event)">
                        <i class="fas fa-lock"></i> Place Order Securely
                    </button>

                    <!-- Hidden flag to track COD confirmation -->
                    <input type="hidden" id="cod_confirmed" name="cod_confirmed" value="0">
                </div>
            </form>
        </div>

        <!-- Order Summary -->
        <div class="order-summary">
            <div class="summary-header">
                <h3><i class="fas fa-receipt"></i> Order Summary</h3>
            </div>

            <div class="summary-content">
                @foreach($cartItems as $item)
                    <div class="order-item">
                        <div class="item-image">
                            @if($item->product->image)
                                <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product->name }}">
                            @else
                                <img src="https://via.placeholder.com/60x60?text=No+Image" alt="No Image">
                            @endif
                        </div>
                        <div class="item-details">
                            <div class="item-name">{{ $item->product->name }}</div>
                            <div class="item-quantity-price">
                                <span>{{ $item->quantity }} x ${{ number_format($item->product->price, 2) }}</span>
                                <span class="item-total">${{ number_format($item->product->price * $item->quantity, 2) }}</span>
                            </div>
                        </div>
                    </div>
                @endforeach

                <div class="summary-totals">
                    <div class="total-row">
                        <span>Subtotal ({{ $cartItems->count() }} items):</span>
                        <span>${{ number_format($total, 2) }}</span>
                    </div>

                    <div class="total-row">
                        <span>Shipping:</span>
                        <span style="color: #28a745; font-weight: 600;">Free</span>
                    </div>

                    <div class="total-row">
                        <span>Tax:</span>
                        <span>Included</span>
                    </div>

                    <div class="total-row final">
                        <span>Total:</span>
                        <span>${{ number_format($total, 2) }}</span>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 1.5rem; font-size: 0.85rem; color: #666;">
                    <i class="fas fa-shield-alt"></i> Your payment information is secure and encrypted
                </div>
            </div>
        </div>
    </div>
</div>



<script>
function selectPayment(method) {
    // If Cash on Delivery is selected, show confirmation dialog
    if (method === 'cash_on_delivery') {
        showCODConfirmation(event.currentTarget);
        return;
    }

    // For all payment methods (including Bakong), just update selection
    updatePaymentSelection(event.currentTarget, method);
}

function showCODConfirmation(selectedOption) {
    // Create custom confirmation modal
    const modal = document.createElement('div');
    modal.className = 'cod-confirmation-modal';
    modal.innerHTML = `
        <div class="cod-confirmation-overlay"></div>
        <div class="cod-confirmation-content">
            <div class="cod-confirmation-header">
                <i class="fas fa-money-bill-wave cod-icon"></i>
                <h3>Cash on Delivery Confirmation</h3>
            </div>
            <div class="cod-confirmation-body">
                <p><strong>Are you sure you want to proceed with Cash on Delivery?</strong></p>
                <div class="cod-details">
                    <div class="cod-detail-item">
                        <i class="fas fa-truck"></i>
                        <span>Payment will be collected when your order is delivered</span>
                    </div>
                    <div class="cod-detail-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Please have the exact amount ready: <strong>{{ '$' . number_format($total, 2) }}</strong></span>
                    </div>
                    <div class="cod-detail-item">
                        <i class="fas fa-clock"></i>
                        <span>Delivery may take 2-5 business days</span>
                    </div>
                </div>
            </div>
            <div class="cod-confirmation-actions">
                <button class="cod-btn-cancel" onclick="closeCODConfirmation()">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button class="cod-btn-confirm" onclick="confirmCOD()">
                    <i class="fas fa-check"></i> Yes, Proceed with COD
                </button>
            </div>
        </div>
    `;

    // Store the selected option for later use
    modal.selectedOption = selectedOption;

    document.body.appendChild(modal);

    // Add animation
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function confirmCOD() {
    const modal = document.querySelector('.cod-confirmation-modal');
    updatePaymentSelection(modal.selectedOption, 'cash_on_delivery');
    closeCODConfirmation();
}

function closeCODConfirmation() {
    const modal = document.querySelector('.cod-confirmation-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function updatePaymentSelection(selectedOption, method) {
    // Remove selected class from all options
    document.querySelectorAll('.payment-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Add selected class to clicked option
    selectedOption.classList.add('selected');

    // Update hidden input
    document.getElementById('payment_method').value = method;
}









// Function to submit order data for Bakong payment
function submitOrderForBakongPayment() {
    console.log('Submitting order for Bakong payment...');

    // Get form
    const form = document.getElementById('checkout-form');

    // Check if form exists
    if (!form) {
        console.error('Checkout form not found');
        alert('Error: Checkout form not found. Please refresh the page and try again.');
        return;
    }

    // Set payment method to Bakong
    const paymentMethodInput = document.getElementById('payment_method');
    if (paymentMethodInput) {
        paymentMethodInput.value = 'khqr';
    }

    console.log('Form found, submitting for Bakong payment...');

    // Submit the form normally - this will trigger the OrderController
    // which will handle the redirect to Bakong payment page
    form.submit();
}

function confirmOrderPlacement(event) {
    console.log('confirmOrderPlacement called');
    const paymentMethod = document.getElementById('payment_method').value;
    const codConfirmed = document.getElementById('cod_confirmed').value;

    console.log('Payment method:', paymentMethod);
    console.log('COD confirmed:', codConfirmed);

    // If Bakong payment is selected, redirect to proper Bakong flow
    if (paymentMethod === 'khqr') {
        console.log('Bakong payment selected, redirecting to Bakong request page');
        event.preventDefault();

        // Submit form data to create order first, then redirect to Bakong
        submitOrderForBakongPayment();
        return false;
    }

    // If Cash on Delivery is selected and not yet confirmed, show confirmation
    if (paymentMethod === 'cash_on_delivery' && codConfirmed === '0') {
        event.preventDefault();
        showFinalCODConfirmation();
        return false;
    }

    // For other payment methods or confirmed COD, proceed normally
    return true;
}

function showFinalCODConfirmation() {
    // Create final confirmation modal
    const modal = document.createElement('div');
    modal.className = 'final-cod-confirmation-modal';
    modal.innerHTML = `
        <div class="final-cod-confirmation-overlay"></div>
        <div class="final-cod-confirmation-content">
            <div class="final-cod-confirmation-header">
                <i class="fas fa-exclamation-triangle final-cod-icon"></i>
                <h3>Final Confirmation Required</h3>
            </div>
            <div class="final-cod-confirmation-body">
                <p><strong>⚠️ Are you absolutely sure you want to place this Cash on Delivery order?</strong></p>
                <div class="final-cod-warning">
                    <div class="warning-item">
                        <i class="fas fa-ban"></i>
                        <span><strong>Once you click confirm, you cannot cancel this order</strong></span>
                    </div>
                    <div class="warning-item">
                        <i class="fas fa-truck"></i>
                        <span>Your order will be immediately sent to our delivery team</span>
                    </div>
                    <div class="warning-item">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>You must pay <strong>{{ '$' . number_format($total, 2) }}</strong> in cash upon delivery</span>
                    </div>
                    <div class="warning-item">
                        <i class="fas fa-clock"></i>
                        <span>Delivery will be scheduled within 2-5 business days</span>
                    </div>
                </div>
                <div class="final-cod-note">
                    <i class="fas fa-info-circle"></i>
                    <strong>Important:</strong> Please ensure you will be available at the delivery address and have the exact amount ready.
                </div>
            </div>
            <div class="final-cod-confirmation-actions">
                <button class="final-cod-btn-cancel" onclick="closeFinalCODConfirmation()">
                    <i class="fas fa-times"></i> Cancel Order
                </button>
                <button class="final-cod-btn-confirm" onclick="proceedWithCODOrder()">
                    <i class="fas fa-check-circle"></i> Yes, Place COD Order
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add animation
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function proceedWithCODOrder() {
    // Close the modal
    closeFinalCODConfirmation();

    // Set the confirmation flag
    document.getElementById('cod_confirmed').value = '1';

    // Trigger the form submission normally by clicking the submit button
    const submitButton = document.querySelector('.btn-place-order');
    submitButton.click();
}

function closeFinalCODConfirmation() {
    const modal = document.querySelector('.final-cod-confirmation-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Location Services Implementation
let searchTimeout = null;

function getCurrentLocation() {
    const statusDiv = document.getElementById('location-status');
    const addressTextarea = document.getElementById('shipping_address');

    // Reset button states
    resetLocationButtons();
    document.querySelector('[onclick="getCurrentLocation()"]').classList.add('active');

    if (!navigator.geolocation) {
        showLocationStatus('Geolocation is not supported by this browser.', 'error');
        return;
    }

    showLocationStatus('<span class="loading-spinner"></span> Getting your current location...', 'info');

    navigator.geolocation.getCurrentPosition(
        async (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            try {
                // Use OpenStreetMap Nominatim for reverse geocoding (free service)
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);

                if (response.ok) {
                    const data = await response.json();
                    if (data.display_name) {
                        addressTextarea.value = data.display_name;
                        showLocationStatus('✅ Current location detected successfully!', 'success');
                        showCurrentLocationInfo(lat, lng, data.display_name);
                    } else {
                        throw new Error('No address found');
                    }
                } else {
                    throw new Error('Geocoding service unavailable');
                }
            } catch (error) {
                console.error('Reverse geocoding error:', error);
                // Fallback to coordinates
                const manualAddress = `Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                addressTextarea.value = manualAddress;
                showLocationStatus('📍 Location detected. Please add more address details manually.', 'info');
            }
        },
        (error) => {
            let errorMessage = 'Unable to get your location. ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'Location access denied by user.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'Location information unavailable.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'Location request timed out.';
                    break;
                default:
                    errorMessage += 'Unknown error occurred.';
                    break;
            }
            showLocationStatus(errorMessage, 'error');
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
        }
    );
}

function toggleMapSearch() {
    const searchContainer = document.getElementById('address-search-container');
    const searchInput = document.getElementById('address-search');

    // Reset button states
    resetLocationButtons();
    document.querySelector('[onclick="toggleMapSearch()"]').classList.add('active');

    // Show search container
    searchContainer.style.display = 'block';
    searchInput.focus();

    showLocationStatus('💡 Type at least 3 characters to search for addresses', 'info');

    // Add search functionality
    searchInput.addEventListener('input', handleAddressSearch);
}

function toggleManualEntry() {
    const addressTextarea = document.getElementById('shipping_address');

    // Reset button states
    resetLocationButtons();
    document.querySelector('[onclick="toggleManualEntry()"]').classList.add('active');

    // Hide search container
    document.getElementById('address-search-container').style.display = 'none';
    hideLocationStatus();

    // Focus on textarea
    addressTextarea.focus();
    showLocationStatus('✏️ Enter your address manually in the text area below', 'info');
}

function handleAddressSearch() {
    const query = document.getElementById('address-search').value.trim();

    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    if (query.length < 3) {
        hideSuggestions();
        return;
    }

    searchTimeout = setTimeout(() => {
        searchAddresses(query);
    }, 500);
}

async function searchAddresses(query) {
    try {
        showLocationStatus('🔍 Searching addresses...', 'info');

        // Use OpenStreetMap Nominatim (completely free)
        const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=kh&limit=5&addressdetails=1`);

        if (!response.ok) {
            throw new Error('Search failed');
        }

        const data = await response.json();
        displaySuggestions(data);

        if (data.length > 0) {
            showLocationStatus(`✅ Found ${data.length} address suggestions`, 'success');
        } else {
            showLocationStatus('❌ No addresses found. Try different keywords.', 'error');
        }

    } catch (error) {
        console.error('Address search error:', error);
        showLocationStatus('❌ Address search failed. Please try manual entry.', 'error');
    }
}

function displaySuggestions(results) {
    const container = document.getElementById('address-suggestions');
    container.innerHTML = '';

    if (results.length === 0) {
        container.innerHTML = '<div class="address-suggestion">No addresses found</div>';
        container.style.display = 'block';
        return;
    }

    results.forEach(result => {
        const suggestion = document.createElement('div');
        suggestion.className = 'address-suggestion';
        suggestion.innerHTML = `
            <div class="suggestion-main">${result.display_name.split(',')[0]}</div>
            <div class="suggestion-secondary">${result.display_name}</div>
        `;

        suggestion.addEventListener('click', () => {
            selectAddress(result.display_name, parseFloat(result.lat), parseFloat(result.lon));
        });

        container.appendChild(suggestion);
    });

    container.style.display = 'block';
}

function selectAddress(address, lat, lng) {
    document.getElementById('shipping_address').value = address;
    hideSuggestions();
    showLocationStatus('✅ Address selected successfully!', 'success');

    if (lat && lng) {
        document.getElementById('latitude').value = lat;
        document.getElementById('longitude').value = lng;
        showCurrentLocationInfo(lat, lng, address);
    }
}

function hideSuggestions() {
    document.getElementById('address-suggestions').style.display = 'none';
}

function resetLocationButtons() {
    document.querySelectorAll('.location-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById('address-search-container').style.display = 'none';
    hideSuggestions();
}

function showLocationStatus(message, type) {
    const status = document.getElementById('location-status');
    status.innerHTML = message;
    status.className = `location-status ${type}`;
    status.style.display = 'block';
}

function hideLocationStatus() {
    document.getElementById('location-status').style.display = 'none';
    document.getElementById('current-location-info').style.display = 'none';
}

function showCurrentLocationInfo(lat, lng, address) {
    const info = document.getElementById('current-location-info');
    info.innerHTML = `
        <strong>📍 Location Information:</strong><br>
        <small>Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}</small><br>
        <small>Address: ${address}</small>
    `;
    info.style.display = 'block';
}



// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set manual entry as default
    toggleManualEntry();

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.address-search-container')) {
            hideSuggestions();
        }
    });


});
</script>
@endsection

@section('scripts')
<script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
<script>
// Configuration - You'll need to get a free Mapbox API key
const MAPBOX_ACCESS_TOKEN = 'pk.eyJ1IjoiYmVsdGVpZWNvbSIsImEiOiJjbTVqZGNqZGcwMGNzMmxzZGNqZGNqZGNqIn0.example'; // Replace with your actual token

// Global variables
let map = null;
let marker = null;
let searchTimeout = null;

// Initialize the location functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeLocationFeatures();
});

function initializeLocationFeatures() {
    // Button event listeners
    document.getElementById('search-btn').addEventListener('click', () => activateMode('search'));
    document.getElementById('current-location-btn').addEventListener('click', () => activateMode('current'));
    document.getElementById('map-pin-btn').addEventListener('click', () => activateMode('map'));
    document.getElementById('manual-btn').addEventListener('click', () => activateMode('manual'));

    // Search input event listener
    document.getElementById('address-search').addEventListener('input', handleAddressSearch);

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.address-search-container')) {
            hideSuggestions();
        }
    });
}

function activateMode(mode) {
    // Update button states
    document.querySelectorAll('.location-btn').forEach(btn => btn.classList.remove('active'));

    // Hide all containers
    document.getElementById('search-container').style.display = 'none';
    document.getElementById('map-container').style.display = 'none';
    hideStatus();

    switch(mode) {
        case 'search':
            document.getElementById('search-btn').classList.add('active');
            document.getElementById('search-container').style.display = 'block';
            document.getElementById('address-search').focus();
            break;

        case 'current':
            document.getElementById('current-location-btn').classList.add('active');
            getCurrentLocation();
            break;

        case 'map':
            document.getElementById('map-pin-btn').classList.add('active');
            document.getElementById('map-container').style.display = 'block';
            initializeMap();
            break;

        case 'manual':
            document.getElementById('manual-btn').classList.add('active');
            document.getElementById('shipping_address').focus();
            break;
    }
}

function handleAddressSearch() {
    const query = document.getElementById('address-search').value.trim();

    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    if (query.length < 3) {
        hideSuggestions();
        return;
    }

    searchTimeout = setTimeout(() => {
        searchAddresses(query);
    }, 300);
}

async function searchAddresses(query) {
    try {
        showStatus('Searching addresses...', 'info');

        // Using Mapbox Geocoding API (free tier: 100,000 requests/month)
        const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${MAPBOX_ACCESS_TOKEN}&country=KH&limit=5`);

        if (!response.ok) {
            throw new Error('Search failed');
        }

        const data = await response.json();
        displaySuggestions(data.features);
        hideStatus();

    } catch (error) {
        console.error('Address search error:', error);
        showStatus('Address search failed. Please try manual entry.', 'error');

        // Fallback to OpenStreetMap Nominatim (completely free)
        try {
            const fallbackResponse = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=kh&limit=5`);
            const fallbackData = await fallbackResponse.json();

            if (fallbackData.length > 0) {
                displayNominatimSuggestions(fallbackData);
                showStatus('Using alternative search service', 'info');
            }
        } catch (fallbackError) {
            console.error('Fallback search error:', fallbackError);
        }
    }
}

function displaySuggestions(features) {
    const container = document.getElementById('address-suggestions');
    container.innerHTML = '';

    if (features.length === 0) {
        container.innerHTML = '<div class="address-suggestion">No addresses found</div>';
        container.style.display = 'block';
        return;
    }

    features.forEach(feature => {
        const suggestion = document.createElement('div');
        suggestion.className = 'address-suggestion';
        suggestion.innerHTML = `
            <div class="suggestion-main">${feature.place_name.split(',')[0]}</div>
            <div class="suggestion-secondary">${feature.place_name}</div>
        `;

        suggestion.addEventListener('click', () => {
            selectAddress(feature.place_name, feature.center[1], feature.center[0]);
        });

        container.appendChild(suggestion);
    });

    container.style.display = 'block';
}

function displayNominatimSuggestions(results) {
    const container = document.getElementById('address-suggestions');
    container.innerHTML = '';

    results.forEach(result => {
        const suggestion = document.createElement('div');
        suggestion.className = 'address-suggestion';
        suggestion.innerHTML = `
            <div class="suggestion-main">${result.display_name.split(',')[0]}</div>
            <div class="suggestion-secondary">${result.display_name}</div>
        `;

        suggestion.addEventListener('click', () => {
            selectAddress(result.display_name, parseFloat(result.lat), parseFloat(result.lon));
        });

        container.appendChild(suggestion);
    });

    container.style.display = 'block';
}

function selectAddress(address, lat, lng) {
    document.getElementById('shipping_address').value = address;
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;
    hideSuggestions();
    showStatus('Address selected successfully!', 'success');
}

function hideSuggestions() {
    document.getElementById('address-suggestions').style.display = 'none';
}

function getCurrentLocation() {
    if (!navigator.geolocation) {
        showStatus('Geolocation is not supported by this browser.', 'error');
        return;
    }

    showStatus('<span class="loading-spinner"></span> Getting your current location...', 'info');

    navigator.geolocation.getCurrentPosition(
        async (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;

            try {
                // Reverse geocoding to get address
                const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${MAPBOX_ACCESS_TOKEN}`);

                if (response.ok) {
                    const data = await response.json();
                    if (data.features.length > 0) {
                        const address = data.features[0].place_name;
                        selectAddress(address, lat, lng);
                        showCurrentLocationInfo(lat, lng, address);
                    }
                } else {
                    throw new Error('Reverse geocoding failed');
                }
            } catch (error) {
                console.error('Reverse geocoding error:', error);

                // Fallback to Nominatim
                try {
                    const fallbackResponse = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`);
                    const fallbackData = await fallbackResponse.json();

                    if (fallbackData.display_name) {
                        selectAddress(fallbackData.display_name, lat, lng);
                        showCurrentLocationInfo(lat, lng, fallbackData.display_name);
                    }
                } catch (fallbackError) {
                    // Manual coordinate entry as last resort
                    const manualAddress = `Latitude: ${lat.toFixed(6)}, Longitude: ${lng.toFixed(6)}`;
                    selectAddress(manualAddress, lat, lng);
                    showStatus('Location detected. Please add more details to the address.', 'info');
                }
            }
        },
        (error) => {
            let errorMessage = 'Unable to get your location. ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'Location access denied by user.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'Location information unavailable.';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'Location request timed out.';
                    break;
                default:
                    errorMessage += 'Unknown error occurred.';
                    break;
            }
            showStatus(errorMessage, 'error');
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
        }
    );
}

function showCurrentLocationInfo(lat, lng, address) {
    const info = document.getElementById('current-location-info');
    info.innerHTML = `
        <strong>📍 Current Location Detected:</strong><br>
        <small>Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}</small><br>
        <small>Address: ${address}</small>
    `;
    info.style.display = 'block';
    showStatus('Current location set successfully!', 'success');
}

function initializeMap() {
    if (!MAPBOX_ACCESS_TOKEN || MAPBOX_ACCESS_TOKEN.includes('example')) {
        showStatus('Map service not configured. Please use other location methods.', 'error');
        return;
    }

    // Default to Phnom Penh, Cambodia
    const defaultLat = 11.5564;
    const defaultLng = 104.9282;

    mapboxgl.accessToken = MAPBOX_ACCESS_TOKEN;

    map = new mapboxgl.Map({
        container: 'map-container',
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [defaultLng, defaultLat],
        zoom: 12
    });

    map.on('load', () => {
        showStatus('Click on the map to pin your location', 'info');
    });

    map.on('click', async (e) => {
        const lat = e.lngLat.lat;
        const lng = e.lngLat.lng;

        // Remove existing marker
        if (marker) {
            marker.remove();
        }

        // Add new marker
        marker = new mapboxgl.Marker()
            .setLngLat([lng, lat])
            .addTo(map);

        // Reverse geocoding
        try {
            const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${MAPBOX_ACCESS_TOKEN}`);

            if (response.ok) {
                const data = await response.json();
                if (data.features.length > 0) {
                    const address = data.features[0].place_name;
                    selectAddress(address, lat, lng);
                }
            }
        } catch (error) {
            console.error('Reverse geocoding error:', error);
            const manualAddress = `Pinned Location: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            selectAddress(manualAddress, lat, lng);
        }
    });
}

function showStatus(message, type) {
    const status = document.getElementById('location-status');
    status.innerHTML = message;
    status.className = `location-status ${type}`;
    status.style.display = 'block';
}

function hideStatus() {
    document.getElementById('location-status').style.display = 'none';
    document.getElementById('current-location-info').style.display = 'none';
}
</script>
@endsection
