@extends('layouts.app')

@section('title', 'Edit Profile')

@section('styles')
<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .breadcrumb-nav {
        margin-bottom: 1rem;
    }

    .breadcrumb-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0;
        transition: all 0.3s ease;
    }

    .breadcrumb-link:hover {
        color: #5a67d8;
        text-decoration: none;
        transform: translateX(-2px);
    }

    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        text-align: center;
    }

    .profile-header h1 {
        font-size: 1.2rem;
        margin-bottom: 0.25rem;
    }

    .profile-header p {
        font-size: 0.85rem;
        margin: 0;
        opacity: 0.9;
    }

    .profile-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .profile-picture-section {
        text-align: center;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 15px;
    }

    .current-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        overflow: hidden;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .current-avatar:hover {
        transform: scale(1.05);
    }

    .current-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 50%;
    }

    .current-avatar:hover .avatar-overlay {
        opacity: 1;
    }

    .avatar-overlay i {
        font-size: 1.5rem;
        color: white;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background: #c82333;
        color: white;
        text-decoration: none;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
    }

    .file-input-wrapper {
        position: relative;
        display: inline-block;
        margin-top: 1rem;
    }

    .file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .file-input-label {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: #667eea;
        color: white;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .file-input-label:hover {
        background: #5a67d8;
    }

    .alert {
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .alert-danger {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @media (max-width: 768px) {
        .profile-container {
            padding: 1rem;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .btn {
            text-align: center;
            justify-content: center;
        }
    }
</style>
@endsection

@section('content')
<div class="profile-container">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
        <a href="{{ route('profile.index') }}" class="breadcrumb-link">
            <i class="fas fa-arrow-left"></i> Back to Profile
        </a>
    </nav>

    <!-- Profile Header -->
    <div class="profile-header">
        <h1><i class="fas fa-user-edit"></i> Edit Profile</h1>
        <p>Update your personal information and profile picture</p>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <ul style="margin: 0; padding-left: 1.5rem;">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="profile-card">
        <!-- Profile Picture Section -->
        <div class="profile-picture-section">
            <div class="current-avatar" onclick="document.getElementById('profile_picture').click()">
                @php
                    $profilePictureUrl = App\Http\Controllers\UserProfileController::getProfilePictureUrl($user);
                @endphp
                @if($profilePictureUrl)
                    <img src="{{ $profilePictureUrl }}" alt="{{ $user->name }}">
                @else
                    {{ App\Http\Controllers\UserProfileController::getUserInitials($user) }}
                @endif
                <div class="avatar-overlay">
                    <i class="fas fa-camera"></i>
                </div>
            </div>
            <h5>{{ $user->name }}</h5>
            <p style="color: #666; margin-bottom: 1rem;">Click on the avatar to change your profile picture</p>
            
            @if($user->profile_picture)
                <form action="{{ route('profile.remove-picture') }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to remove your profile picture?')">
                        <i class="fas fa-trash"></i> Remove Picture
                    </button>
                </form>
            @endif
        </div>

        <!-- Profile Form -->
        <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <!-- Hidden file input -->
            <input type="file" id="profile_picture" name="profile_picture" accept="image/*" style="display: none;" onchange="previewImage(this)">

            <div class="form-group">
                <label for="name" class="form-label">Full Name</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $user->name) }}" required>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control" id="email" name="email" value="{{ old('email', $user->email) }}" required>
            </div>

            <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="text" class="form-control" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" placeholder="Enter your phone number">
            </div>

            <div class="form-group">
                <label for="address" class="form-label">Address</label>
                <textarea class="form-control" id="address" name="address" rows="3" placeholder="Enter your address">{{ old('address', $user->address) }}</textarea>
            </div>

            <div class="form-actions">
                <a href="{{ route('profile.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const avatar = document.querySelector('.current-avatar');
            avatar.innerHTML = `
                <img src="${e.target.result}" alt="Preview">
                <div class="avatar-overlay">
                    <i class="fas fa-camera"></i>
                </div>
            `;
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
@endsection
