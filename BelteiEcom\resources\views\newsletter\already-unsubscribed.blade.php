@extends('layouts.app')

@section('title', 'Already Unsubscribed')

@section('content')
<style>
    .already-unsubscribed-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }

    .already-unsubscribed-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="300" cy="200" r="120" fill="url(%23a)"/><circle cx="700" cy="400" r="100" fill="url(%23a)"/><circle cx="200" cy="600" r="80" fill="url(%23a)"/><circle cx="800" cy="700" r="150" fill="url(%23a)"/></svg>') no-repeat center center;
        background-size: cover;
        opacity: 0.3;
    }

    .already-unsubscribed-card {
        background: white;
        border-radius: 30px;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        max-width: 600px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: slideUp 0.8s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .already-unsubscribed-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        text-align: center;
        padding: 3rem 2rem;
        position: relative;
    }

    .already-unsubscribed-header::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }

    .info-icon {
        font-size: 5rem;
        margin-bottom: 1rem;
        display: block;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
    }

    .already-unsubscribed-title {
        font-size: 2.8rem;
        font-weight: 800;
        margin: 0 0 1rem 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .already-unsubscribed-subtitle {
        font-size: 1.3rem;
        opacity: 0.95;
        margin: 0;
        font-weight: 300;
    }

    .already-unsubscribed-content {
        padding: 3rem 2.5rem;
        text-align: center;
    }

    .info-badge {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 0 auto 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 30px rgba(67, 233, 123, 0.3);
        animation: glow 2s infinite alternate;
    }

    @keyframes glow {
        from {
            box-shadow: 0 10px 30px rgba(67, 233, 123, 0.3);
        }
        to {
            box-shadow: 0 15px 40px rgba(67, 233, 123, 0.5);
        }
    }

    .info-badge i {
        font-size: 2.5rem;
        color: white;
    }

    .main-message {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        line-height: 1.3;
    }

    .sub-message {
        font-size: 1.1rem;
        color: #6c757d;
        line-height: 1.7;
        margin-bottom: 2.5rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .email-highlight {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    .status-section {
        background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 2.5rem;
        margin: 2rem 0;
        border: 1px solid rgba(79, 172, 254, 0.1);
    }

    .status-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-top: 1.5rem;
    }

    .status-item {
        text-align: center;
    }

    .status-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
    }

    .status-label {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .status-value {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .status-badge {
        background: #6c757d;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        display: inline-block;
        margin-top: 0.5rem;
    }

    .resubscribe-section {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
        border-radius: 20px;
        padding: 2.5rem;
        margin: 2rem 0;
        border: 2px solid rgba(67, 233, 123, 0.2);
    }

    .resubscribe-title {
        color: #2c3e50;
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin: 2.5rem 0;
    }

    .btn-modern {
        padding: 1rem 2.5rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        min-width: 180px;
        justify-content: center;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(79, 172, 254, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success-modern {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .btn-success-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(67, 233, 123, 0.4);
        color: white;
        text-decoration: none;
    }

    .contact-section {
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #f1f3f4;
        text-align: center;
    }

    .contact-text {
        color: #6c757d;
        font-size: 0.95rem;
        margin: 0;
    }

    .contact-link {
        color: #4facfe;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .contact-link:hover {
        color: #00f2fe;
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .already-unsubscribed-container {
            padding: 1rem;
        }

        .already-unsubscribed-content {
            padding: 2rem 1.5rem;
        }

        .already-unsubscribed-title {
            font-size: 2.2rem;
        }

        .main-message {
            font-size: 1.5rem;
        }

        .status-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-modern {
            width: 100%;
            max-width: 280px;
        }
    }
</style>

<div class="already-unsubscribed-container">
    <div class="already-unsubscribed-card">
        <!-- Header -->
        <div class="already-unsubscribed-header">
            <span class="info-icon">ℹ️</span>
            <h1 class="already-unsubscribed-title">Already Unsubscribed</h1>
            <p class="already-unsubscribed-subtitle">You're not receiving our emails</p>
        </div>

        <!-- Content -->
        <div class="already-unsubscribed-content">
            <div class="info-badge">
                <i class="fas fa-info-circle"></i>
            </div>

            <h2 class="main-message">You're Already Unsubscribed</h2>

            <p class="sub-message">
                <span class="email-highlight">{{ $newsletter->email }}</span> was previously unsubscribed from our newsletter.
                <br><br>
                @if($newsletter->unsubscribed_at)
                    You unsubscribed on <strong>{{ $newsletter->unsubscribed_at->format('F j, Y \a\t g:i A') }}</strong>
                @else
                    You are not currently subscribed to our newsletter.
                @endif
            </p>

            <!-- Status Info -->
            <div class="status-section">
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-icon">📧</span>
                        <div class="status-label">Email Status</div>
                        <div class="status-value">
                            <span class="status-badge">Unsubscribed</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <span class="status-icon">📅</span>
                        <div class="status-label">Since</div>
                        <div class="status-value">
                            @if($newsletter->unsubscribed_at)
                                {{ $newsletter->unsubscribed_at->diffForHumans() }}
                            @else
                                Not subscribed
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resubscribe Section -->
            <div class="resubscribe-section">
                <h3 class="resubscribe-title">
                    <i class="fas fa-heart"></i>
                    Miss Our Updates?
                </h3>
                <p style="color: #6c757d; margin-bottom: 2rem; font-size: 1rem;">
                    Want to stay updated with our latest products, deals, and exclusive offers?
                    You can resubscribe anytime and start receiving amazing benefits again!
                </p>

                <button onclick="resubscribe()" class="btn-modern btn-success-modern">
                    <i class="fas fa-plus"></i>
                    Resubscribe Now
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{ route('home') }}" class="btn-modern btn-primary-modern">
                    <i class="fas fa-home"></i>
                    Back to Store
                </a>

                <a href="{{ route('products.index') }}" class="btn-modern btn-success-modern">
                    <i class="fas fa-shopping-bag"></i>
                    Browse Products
                </a>
            </div>

            <!-- Contact Section -->
            <div class="contact-section">
                <p class="contact-text">
                    Questions about your subscription? We're here to help!
                    <br>
                    Contact us at
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <EMAIL>
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
<script>
// Resubscribe function
function resubscribe() {
    const confirmation = confirm('🎉 Welcome back! Would you like to resubscribe to our newsletter and start receiving exclusive deals again?');

    if (confirmation) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resubscribing...';
        button.disabled = true;

        // Send resubscribe request
        fetch('{{ route("newsletter.subscribe") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                email: '{{ $newsletter->email }}',
                name: '{{ $newsletter->name }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('🎉 Welcome back! You have been resubscribed to our newsletter.', 'success');
                setTimeout(() => {
                    window.location.href = '{{ route("home") }}';
                }, 2000);
            } else {
                showNotification('❌ ' + (data.message || 'Something went wrong. Please try again.'), 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('❌ Something went wrong. Please try again.', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' :
                     type === 'error' ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' :
                     'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'};
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-weight: 600;
        max-width: 400px;
        animation: slideInRight 0.5s ease-out;
    `;

    notification.innerHTML = message;
    document.body.appendChild(notification);

    // Add slide in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);

    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideInRight 0.5s ease-out reverse';
        setTimeout(() => {
            notification.remove();
            style.remove();
        }, 500);
    }, 5000);
}

// Add floating particles animation
document.addEventListener('DOMContentLoaded', function() {
    createFloatingParticles();
});

function createFloatingParticles() {
    const container = document.querySelector('.already-unsubscribed-container');
    const particles = ['💌', '📧', '✨', '💫', '🌟', 'ℹ️'];

    for (let i = 0; i < 12; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.innerHTML = particles[Math.floor(Math.random() * particles.length)];
            particle.style.cssText = `
                position: absolute;
                font-size: ${Math.random() * 20 + 15}px;
                left: ${Math.random() * 100}%;
                top: 100%;
                pointer-events: none;
                z-index: 0;
                opacity: 0.6;
                animation: floatUp ${Math.random() * 3 + 4}s linear forwards;
            `;

            container.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 7000);
        }, i * 600);
    }

    // Add CSS for floating animation
    if (!document.querySelector('#floating-animation')) {
        const style = document.createElement('style');
        style.id = 'floating-animation';
        style.textContent = `
            @keyframes floatUp {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}
</script>
@endsection
