<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class ApiPartner extends Model
{
    protected $fillable = [
        'company_name',
        'contact_name',
        'email',
        'phone',
        'website',
        'business_description',
        'api_key',
        'api_secret',
        'status',
        'tier',
        'commission_rate',
        'minimum_order',
        'allowed_categories',
        'settings',
        'approved_at',
        'last_api_call',
        'total_orders',
        'total_sales',
        'total_commission',
    ];

    protected $casts = [
        'allowed_categories' => 'array',
        'settings' => 'array',
        'approved_at' => 'datetime',
        'last_api_call' => 'datetime',
        'commission_rate' => 'decimal:2',
        'minimum_order' => 'decimal:2',
        'total_sales' => 'decimal:2',
        'total_commission' => 'decimal:2',
    ];

    protected $hidden = [
        'api_secret',
    ];

    /**
     * Get the orders for this partner
     */
    public function orders(): HasMany
    {
        return $this->hasMany(ApiOrder::class, 'partner_id');
    }

    /**
     * Get the user associated with this partner
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }

    /**
     * Generate API credentials
     */
    public static function generateCredentials(): array
    {
        return [
            'api_key' => 'bec_' . Str::random(32),
            'api_secret' => Hash::make(Str::random(64)),
        ];
    }

    /**
     * Check if partner is active
     */
    public function isActive(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if partner can access a category
     */
    public function canAccessCategory(int $categoryId): bool
    {
        if (empty($this->allowed_categories)) {
            return true; // No restrictions
        }

        return in_array($categoryId, $this->allowed_categories);
    }

    /**
     * Calculate commission for an amount
     */
    public function calculateCommission(float $baseAmount): float
    {
        return $baseAmount * ($this->commission_rate / 100);
    }

    /**
     * Update API usage stats
     */
    public function updateApiUsage(): void
    {
        $this->update(['last_api_call' => now()]);
    }

    /**
     * Get rate limit settings
     */
    public function getRateLimit(): array
    {
        $defaults = [
            'basic' => ['requests_per_minute' => 60, 'requests_per_hour' => 1000],
            'premium' => ['requests_per_minute' => 120, 'requests_per_hour' => 5000],
            'enterprise' => ['requests_per_minute' => 300, 'requests_per_hour' => 20000],
        ];

        return $this->settings['rate_limit'] ?? $defaults[$this->tier] ?? $defaults['basic'];
    }

    /**
     * Scope for active partners
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for specific tier
     */
    public function scopeTier($query, string $tier)
    {
        return $query->where('tier', $tier);
    }
}
