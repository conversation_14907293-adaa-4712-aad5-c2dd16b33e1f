s<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>{{ config('app.name', 'BelteiEcom') }} - Admin Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- SB Admin 2 CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/startbootstrap-sb-admin-2/4.1.4/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Enhanced Admin Styles -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        /* Modern Sidebar Design */
        .sidebar {
            background: var(--primary-gradient) !important;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            border-right: none;
            width: 280px !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            height: 100vh !important;
            min-height: 100vh !important;
            z-index: 1000;
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* Ensure full height background coverage */
        #accordionSidebar {
            background: var(--primary-gradient) !important;
            height: 100vh !important;
            min-height: 100vh !important;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            pointer-events: none;
            z-index: 1;
        }

        /* Content wrapper positioning */
        #content-wrapper {
            margin-left: 280px !important;
            min-height: 100vh;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                margin-left: -280px;
            }

            #content-wrapper {
                margin-left: 0 !important;
            }
        }

        .sidebar .sidebar-brand {
            background: rgba(255,255,255,0.05);
            border-radius: 20px;
            margin: 1.5rem 1rem;
            padding: 1.5rem;
            transition: all 0.4s ease;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar .sidebar-brand:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .sidebar .sidebar-brand-icon {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);
            width: 55px;
            height: 55px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .sidebar .sidebar-brand:hover .sidebar-brand-icon {
            transform: rotate(10deg) scale(1.1);
        }

        .sidebar .sidebar-brand-text {
            transition: all 0.4s ease;
            position: relative;
            z-index: 2;
        }

        .sidebar .nav-item {
            margin: 0.3rem 1rem;
            position: relative;
        }

        .sidebar .nav-link {
            border-radius: 15px;
            padding: 1rem 1.2rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            color: rgba(255,255,255,0.8) !important;
            border: 1px solid transparent;
            backdrop-filter: blur(5px);
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
            transition: all 0.4s ease;
            z-index: 0;
        }

        .sidebar .nav-link:hover::before {
            left: 0;
        }

        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(8px);
            border-color: rgba(255,255,255,0.2);
            color: white !important;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .sidebar .nav-item.active .nav-link {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(147, 51, 234, 0.3) 100%);
            border-color: rgba(59, 130, 246, 0.5);
            color: white !important;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .sidebar .nav-item.active .nav-link::before {
            left: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        }

        .sidebar .nav-link i {
            font-size: 1.3rem;
            margin-right: 1rem;
            width: 24px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .sidebar .nav-link:hover i {
            transform: scale(1.1);
        }

        .sidebar .nav-link span {
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
            font-weight: 500;
        }

        .sidebar .nav-link .badge {
            background: linear-gradient(135deg, #ef4444 0%, #f97316 100%) !important;
            color: white !important;
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
            border-radius: 12px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
            transition: all 0.4s ease;
            position: relative;
            z-index: 2;
        }

        .sidebar-heading {
            color: rgba(255,255,255,0.6) !important;
            font-weight: 700;
            font-size: 0.75rem;
            letter-spacing: 2px;
            margin: 2rem 1.5rem 1rem 1.5rem;
            text-transform: uppercase;
            position: relative;
            transition: all 0.4s ease;
            z-index: 2;
        }

        .sidebar-heading::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .sidebar-divider {
            border-color: rgba(255,255,255,0.15) !important;
            margin: 1.5rem 1.5rem;
            transition: all 0.4s ease;
        }



        /* Enhanced Topbar */
        .topbar {
            background: white !important;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1) !important;
            border-bottom: none;
        }

        .topbar .nav-link {
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .topbar .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        /* Enhanced Content Area */
        .container-fluid {
            background: #f8f9fc;
            min-height: calc(100vh - 120px);
        }

        /* Enhanced Cards */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 1.5rem;
        }

        .card-header h6 {
            margin: 0;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* Enhanced Buttons */
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: var(--success-gradient);
            border: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(67, 233, 123, 0.4);
        }

        .btn-info {
            background: var(--info-gradient);
            border: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-warning {
            background: var(--warning-gradient);
            border: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(250, 112, 154, 0.4);
        }

        /* Enhanced Tables */
        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: var(--primary-gradient);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }

        .table tbody td {
            padding: 1rem;
            border-color: #e3e6f0;
            vertical-align: middle;
        }

        /* Enhanced Forms */
        .form-control {
            border-radius: 10px;
            border: 2px solid #e3e6f0;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* Enhanced Alerts */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
            color: #155724;
            border-left: 4px solid #43e97b;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(245, 87, 108, 0.1) 0%, rgba(240, 147, 251, 0.1) 100%);
            color: #721c24;
            border-left: 4px solid #f5576c;
        }

        /* Enhanced Badges */
        .badge {
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-weight: 600;
        }

        /* Page Animations */
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
            opacity: 0;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            animation: slideUp 0.8s ease-out forwards;
            transform: translateY(30px);
            opacity: 0;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Enhanced Footer */
        .sticky-footer {
            background: var(--primary-gradient) !important;
            color: white;
        }

        /* Beautiful Global Loading Screen for Admin Panel */
        .admin-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 1;
            visibility: visible;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .admin-loading.hide {
            opacity: 0;
            visibility: hidden;
        }

        .admin-loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: adminSpin 1s linear infinite;
            margin-bottom: 20px;
        }

        .admin-loading-text {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            animation: adminPulse 2s ease-in-out infinite;
        }

        .admin-loading-subtext {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            text-align: center;
            max-width: 300px;
            animation: adminFadeInOut 3s ease-in-out infinite;
        }

        @keyframes adminSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes adminPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes adminFadeInOut {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        /* Page transition effects - start hidden */
        .admin-content {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .admin-content.loaded {
            opacity: 1;
            transform: translateY(0);
        }



        /* Notification Dropdown Styles */
        .dropdown-list {
            min-width: 350px;
        }

        .icon-circle {
            height: 2.5rem;
            width: 2.5rem;
            border-radius: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-scroll {
            scrollbar-width: thin;
            scrollbar-color: #d1d3e2 transparent;
        }

        .notification-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .notification-scroll::-webkit-scrollbar-track {
            background: #f8f9fc;
        }

        .notification-scroll::-webkit-scrollbar-thumb {
            background: #d1d3e2;
            border-radius: 3px;
        }

        .notification-scroll::-webkit-scrollbar-thumb:hover {
            background: #adb5bd;
        }

        .dropdown-item:hover {
            background-color: #f8f9fc;
            transition: all 0.3s ease;
        }

        .badge-counter {
            position: absolute;
            right: 0.1rem;
            top: 0.1rem;
            font-size: 0.75rem;
            min-width: 1.3rem;
            height: 1.3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
            font-weight: 600;
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .sidebar {
                width: 100% !important;
            }

            .card {
                margin-bottom: 1rem;
            }

            .dropdown-list {
                min-width: 300px;
            }
        }
    </style>

    @yield('styles')
</head>

<body id="page-top">
    <!-- Global Admin Loading Screen -->
    <div class="admin-loading" id="adminLoading">
        <div class="admin-loading-spinner"></div>
        <div class="admin-loading-text" id="loadingText">Loading Dashboard...</div>
        <div class="admin-loading-subtext" id="loadingSubtext">Preparing your admin panel with the latest data</div>
    </div>

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="{{ route('admin.dashboard') }}">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="sidebar-brand-text mx-3">
                    <div style="font-weight: 700; font-size: 1.2rem;">BelteiEcom</div>
                    <div style="font-size: 0.8rem; opacity: 0.8;">Admin Panel</div>
                </div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.dashboard') }}" data-tooltip="Dashboard">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-chart-pie"></i>
                        <span>Dashboard</span>
                    </div>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                📦 Catalog Management
            </div>

            <!-- Nav Item - Products -->
            <li class="nav-item {{ request()->routeIs('admin.products.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.products.index') }}" data-tooltip="Products">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-cubes"></i>
                        <span>Products</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\Product::count() }}</span>
                </a>
            </li>

            <!-- Nav Item - Categories -->
            <li class="nav-item {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.categories.index') }}" data-tooltip="Categories">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tags"></i>
                        <span>Categories</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\Category::count() }}</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                💰 Sales & Orders
            </div>

            <!-- Nav Item - Orders -->
            <li class="nav-item {{ request()->routeIs('admin.orders.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.orders.index') }}" data-tooltip="Orders">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Orders</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\Order::count() }}</span>
                </a>
            </li>

            <!-- Nav Item - Customers -->
            <li class="nav-item {{ request()->routeIs('admin.customers.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.customers.index') }}" data-tooltip="Customers">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-users"></i>
                        <span>Customers</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\User::where('is_admin', false)->count() }}</span>
                </a>
            </li>

            <!-- Nav Item - Reviews -->
            <li class="nav-item {{ request()->routeIs('admin.reviews.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.reviews.index') }}" data-tooltip="Reviews">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-star"></i>
                        <span>Reviews</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\Review::count() }}</span>
                </a>
            </li>

            <!-- Nav Item - Inventory Management -->
            <li class="nav-item {{ request()->routeIs('admin.inventory.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.inventory.index') }}" data-tooltip="Advanced Inventory Management">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-warehouse"></i>
                        <span>Inventory</span>
                    </div>
                    <span class="badge badge-warning">{{ \App\Models\WarehouseInventory::lowStock()->count() }}</span>
                </a>
            </li>

            <!-- Nav Item - Live Chat Support -->
            <li class="nav-item {{ request()->routeIs('admin.chat.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.chat.index') }}" data-tooltip="Live Chat Support">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-comments"></i>
                        <span>Live Chat</span>
                    </div>
                    <span class="badge badge-info" id="waiting-chats-badge">{{ \App\Models\ChatConversation::waiting()->count() }}</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                📧 Marketing
            </div>

            <!-- Nav Item - Newsletter -->
            <li class="nav-item {{ request()->routeIs('admin.newsletter.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.newsletter.index') }}" data-tooltip="Newsletter">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-envelope"></i>
                        <span>Newsletter</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\Newsletter::active()->count() }}</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                ⚙️ Settings
            </div>

            <!-- Nav Item - Admin Management -->
            <li class="nav-item {{ request()->routeIs('admin.admins.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.admins.index') }}" data-tooltip="Admin Management">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user-shield"></i>
                        <span>Admin Management</span>
                    </div>
                    <span class="badge badge-light">{{ \App\Models\User::where('is_admin', true)->count() }}</span>
                </a>
            </li>

            <!-- Nav Item - Settings -->
            <li class="nav-item {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                <a class="nav-link" href="{{ route('admin.settings.index') }}" data-tooltip="Settings">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </div>
                </a>
            </li>

            <!-- Nav Item - Reports -->
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="alert('Coming Soon!')" data-tooltip="Reports">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </div>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">



        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <!-- Nav Item - Notifications -->
                        <li class="nav-item dropdown no-arrow mx-1">
                            <a class="nav-link dropdown-toggle" href="#" id="alertsDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-bell fa-fw"></i>
                                <!-- Counter - Notifications -->
                                @php
                                    $newOrders = \App\Models\Order::where('status', 'pending')->count();
                                    $lowStockProducts = \App\Models\Product::where('stock', '<=', 5)->count();
                                    $totalNotifications = $newOrders + $lowStockProducts;
                                @endphp
                                @if($totalNotifications > 0)
                                    <span class="badge badge-danger badge-counter">{{ $totalNotifications > 99 ? '99+' : $totalNotifications }}</span>
                                @endif
                            </a>
                            <!-- Dropdown - Notifications -->
                            <div class="dropdown-list dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="alertsDropdown">
                                <h6 class="dropdown-header">
                                    <i class="fas fa-bell mr-2"></i>
                                    Notifications Center
                                </h6>

                                <div class="notification-scroll" style="max-height: 300px; overflow-y: auto;">
                                    @if($newOrders > 0)
                                        <a class="dropdown-item d-flex align-items-center" href="{{ route('admin.orders.index') }}">
                                            <div class="mr-3">
                                                <div class="icon-circle bg-primary">
                                                    <i class="fas fa-shopping-cart text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="small text-gray-500">{{ now()->format('M d, Y') }}</div>
                                                <span class="font-weight-bold">{{ $newOrders }} New Order{{ $newOrders > 1 ? 's' : '' }}</span>
                                                <div class="small text-gray-500">Click to view pending orders</div>
                                            </div>
                                        </a>
                                    @endif

                                    @if($lowStockProducts > 0)
                                        <a class="dropdown-item d-flex align-items-center" href="{{ route('admin.products.index') }}?filter=low_stock">
                                            <div class="mr-3">
                                                <div class="icon-circle bg-warning">
                                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="small text-gray-500">{{ now()->format('M d, Y') }}</div>
                                                <span class="font-weight-bold">{{ $lowStockProducts }} Low Stock Alert{{ $lowStockProducts > 1 ? 's' : '' }}</span>
                                                <div class="small text-gray-500">Products with 5 or fewer items</div>
                                            </div>
                                        </a>
                                    @endif

                                    @php
                                        $recentLowStockProducts = \App\Models\Product::where('stock', '<=', 5)->take(5)->get();
                                    @endphp
                                    @foreach($recentLowStockProducts as $product)
                                        <a class="dropdown-item d-flex align-items-center" href="{{ route('admin.products.edit', $product->id) }}">
                                            <div class="mr-3">
                                                <div class="icon-circle bg-danger">
                                                    <i class="fas fa-box text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="small text-gray-500">Low Stock</div>
                                                <span class="font-weight-bold">{{ Str::limit($product->name, 25) }}</span>
                                                <div class="small text-gray-500">Only {{ $product->stock }} left in stock</div>
                                            </div>
                                        </a>
                                    @endforeach

                                    @if($totalNotifications == 0)
                                        <div class="dropdown-item text-center text-gray-500 py-4">
                                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                                            <div>No new notifications</div>
                                            <div class="small">All caught up!</div>
                                        </div>
                                    @endif
                                </div>

                                @if($totalNotifications > 0)
                                    <div class="dropdown-divider"></div>
                                    <div class="dropdown-item text-center small text-gray-500">
                                        <i class="fas fa-bell mr-1"></i>
                                        {{ $totalNotifications }} Total Notification{{ $totalNotifications > 1 ? 's' : '' }}
                                    </div>
                                @endif
                            </div>
                        </li>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">{{ Auth::user()->name }}</span>
                                @if(Auth::user()->profile_picture)
                                    <img class="img-profile rounded-circle" src="{{ asset('storage/' . Auth::user()->profile_picture) }}"
                                         style="width: 32px; height: 32px; object-fit: cover;">
                                @else
                                    <div class="img-profile rounded-circle d-flex align-items-center justify-content-center text-white"
                                         style="width: 32px; height: 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-weight: 700; font-size: 0.8rem;">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </div>
                                @endif
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="{{ route('admin.settings.profile') }}">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile Settings
                                </a>
                                <a class="dropdown-item" href="{{ route('admin.settings.password') }}">
                                    <i class="fas fa-key fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Change Password
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ route('home') }}">
                                    <i class="fas fa-store fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Go to Store
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid admin-content" id="adminContent">

                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> Please fix the following errors:
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @yield('content')

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; {{ config('app.name', 'BelteiEcom') }} {{ date('Y') }}</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="{{ route('logout') }}"
                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>
                    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                        @csrf
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.4.1/jquery.easing.min.js"></script>

    <!-- SB Admin 2 JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/startbootstrap-sb-admin-2/4.1.4/js/sb-admin-2.min.js"></script>

    @yield('scripts')

    <!-- Enhanced Admin JavaScript -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add smooth animations to page elements
        $(document).ready(function() {
            // Animate cards on page load
            $('.card').each(function(index) {
                $(this).delay(100 * index).animate({
                    opacity: 1
                }, 500);
            });

            // Add hover effects to buttons
            $('.btn').hover(
                function() {
                    $(this).addClass('shadow-lg');
                },
                function() {
                    $(this).removeClass('shadow-lg');
                }
            );

            // Smooth scroll for sidebar links
            $('.sidebar .nav-link').click(function(e) {
                if ($(this).attr('href').startsWith('#')) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: $($(this).attr('href')).offset().top - 100
                    }, 500);
                }
            });



            // Enhanced nav link hover effects
            $('.sidebar .nav-link').hover(
                function() {
                    $(this).find('i').addClass('animated-icon');
                },
                function() {
                    $(this).find('i').removeClass('animated-icon');
                }
            );

            // Add page load animations
            $('.card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
                $(this).addClass('fade-in');
            });
        });

        // Add CSS for additional animations
        const additionalStyles = `
            .animated-icon {
                animation: iconBounce 0.6s ease;
            }

            @keyframes iconBounce {
                0%, 20%, 60%, 100% {
                    transform: translateY(0) scale(1);
                }
                40% {
                    transform: translateY(-3px) scale(1.1);
                }
                80% {
                    transform: translateY(-1px) scale(1.05);
                }
            }

            .fade-in {
                animation: fadeInUp 0.8s ease-out forwards;
                opacity: 0;
                transform: translateY(30px);
            }

            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;

        // Inject additional styles
        $('<style>').text(additionalStyles).appendTo('head');

        // Beautiful Global Admin Loading System
        let loadingStartTime = null;
        let isCurrentlyLoading = false;
        let loadingTriggeredByClick = false;

        const loadingMessages = {
            dashboard: {
                text: 'Loading Dashboard...',
                subtext: 'Preparing your admin dashboard with the latest data'
            },
            products: {
                text: 'Loading Products...',
                subtext: 'Fetching your product catalog and inventory'
            },
            orders: {
                text: 'Loading Orders...',
                subtext: 'Retrieving customer orders and transactions'
            },
            customers: {
                text: 'Loading Customers...',
                subtext: 'Gathering customer information and profiles'
            },
            categories: {
                text: 'Loading Categories...',
                subtext: 'Organizing your product categories'
            },
            settings: {
                text: 'Loading Settings...',
                subtext: 'Preparing configuration options'
            },
            default: {
                text: 'Loading...',
                subtext: 'Preparing your admin panel'
            }
        };

        function getLoadingMessage(url) {
            console.log('Loading URL:', url); // Debug log
            if (url.includes('categories') || url.includes('category')) return loadingMessages.categories;
            if (url.includes('products') || url.includes('product')) return loadingMessages.products;
            if (url.includes('orders') || url.includes('order')) return loadingMessages.orders;
            if (url.includes('customers') || url.includes('customer')) return loadingMessages.customers;
            if (url.includes('settings') || url.includes('setting')) return loadingMessages.settings;
            if (url.includes('dashboard')) return loadingMessages.dashboard;
            return loadingMessages.default;
        }

        function showAdminLoading(url = '') {
            // If already loading, don't change the message
            if (isCurrentlyLoading) {
                return;
            }

            const loadingScreen = document.getElementById('adminLoading');
            const adminContent = document.getElementById('adminContent');
            const loadingText = document.getElementById('loadingText');
            const loadingSubtext = document.getElementById('loadingSubtext');

            // Mark as currently loading
            isCurrentlyLoading = true;

            // Record when loading started
            loadingStartTime = Date.now();

            const message = getLoadingMessage(url);
            loadingText.textContent = message.text;
            loadingSubtext.textContent = message.subtext;

            // Show loading screen and hide content
            loadingScreen.classList.remove('hide');
            adminContent.classList.remove('loaded');
        }

        function hideAdminLoading() {
            const loadingScreen = document.getElementById('adminLoading');
            const adminContent = document.getElementById('adminContent');

            // Force exactly 1.5 seconds loading time - no matter what
            setTimeout(() => {
                // Hide loading and show content at EXACTLY the same time
                loadingScreen.classList.add('hide');
                adminContent.classList.add('loaded');

                // Reset loading state
                isCurrentlyLoading = false;
                loadingTriggeredByClick = false;
            }, 1500); // Always wait exactly 1.5 seconds
        }

        // Show loading on page navigation
        $(document).on('click', 'a[href]:not([href^="#"]):not([href^="javascript:"]):not([target="_blank"]):not(.no-loading)', function(e) {
            const href = $(this).attr('href');
            const currentUrl = window.location.href;

            // Only show loading for admin panel links and if it's a different page
            if (href && (href.includes('/admin') || href.startsWith('/admin')) && href !== currentUrl) {
                loadingTriggeredByClick = true;
                showAdminLoading(href);
            }
        });

        // Show loading on form submissions
        $(document).on('submit', 'form:not(.no-loading)', function(e) {
            showAdminLoading('form submission');
        });

        // Set initial loading start time when page begins loading (only if not triggered by click)
        if (!loadingTriggeredByClick) {
            loadingStartTime = Date.now();
            showAdminLoading(window.location.href);
        }

        // Perfect timing for page load - always 1.5 seconds
        $(window).on('load', function() {
            // Only start loading if not already triggered by click
            if (!loadingTriggeredByClick && !isCurrentlyLoading) {
                showAdminLoading(window.location.href);
            }
            hideAdminLoading();
        });

        // Perfect timing on page ready - always 1.5 seconds
        $(document).ready(function() {
            // Only start loading if not already triggered by click
            if (!loadingTriggeredByClick && !isCurrentlyLoading) {
                showAdminLoading(window.location.href);
            }
            hideAdminLoading();
        });

        // Handle browser back/forward buttons
        $(window).on('pageshow', function(event) {
            if (event.originalEvent.persisted) {
                hideAdminLoading();
            }
        });
    </script>

</body>
</html>
