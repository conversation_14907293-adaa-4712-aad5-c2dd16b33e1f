<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verified Successfully - {{ config('app.name') }}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow: hidden;
        }

        .success-container {
            background: white;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            text-align: center;
            position: relative;
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-header {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 50px 30px;
            position: relative;
        }

        .success-icon {
            font-size: 5rem;
            margin-bottom: 20px;
            animation: bounceIn 1s ease-out 0.3s both;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .success-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .success-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
            animation: fadeInUp 0.8s ease-out 0.7s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-body {
            padding: 50px 40px;
        }

        .success-message {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
            animation: fadeInUp 0.8s ease-out 0.9s both;
        }

        .user-email {
            background: #e8f5e8;
            border: 2px solid #00b894;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            font-weight: 600;
            color: #00a085;
            animation: fadeInUp 0.8s ease-out 1.1s both;
        }

        .benefits {
            text-align: left;
            margin: 30px 0;
            animation: fadeInUp 0.8s ease-out 1.3s both;
        }

        .benefit {
            display: flex;
            align-items: center;
            margin: 15px 0;
            color: #495057;
            font-weight: 500;
        }

        .benefit-icon {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .action-buttons {
            margin-top: 40px;
            animation: fadeInUp 0.8s ease-out 1.5s both;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            text-decoration: none;
            color: #495057;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ffd700;
            animation: confetti-fall 3s linear infinite;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }

        .confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #ff6b6b; }
        .confetti:nth-child(2) { left: 20%; animation-delay: 0.5s; background: #4ecdc4; }
        .confetti:nth-child(3) { left: 30%; animation-delay: 1s; background: #45b7d1; }
        .confetti:nth-child(4) { left: 40%; animation-delay: 1.5s; background: #96ceb4; }
        .confetti:nth-child(5) { left: 50%; animation-delay: 2s; background: #ffeaa7; }
        .confetti:nth-child(6) { left: 60%; animation-delay: 2.5s; background: #dda0dd; }
        .confetti:nth-child(7) { left: 70%; animation-delay: 3s; background: #98d8c8; }
        .confetti:nth-child(8) { left: 80%; animation-delay: 3.5s; background: #f7dc6f; }
        .confetti:nth-child(9) { left: 90%; animation-delay: 4s; background: #bb8fce; }

        @media (max-width: 480px) {
            .success-container {
                margin: 10px;
                border-radius: 20px;
            }
            
            .success-header {
                padding: 40px 20px;
            }
            
            .success-body {
                padding: 40px 20px;
            }
            
            .success-title {
                font-size: 1.7rem;
            }
            
            .success-icon {
                font-size: 4rem;
            }
            
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Confetti Animation -->
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>
    <div class="confetti"></div>

    <div class="success-container">
        <div class="success-header">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1 class="success-title">Email Verified!</h1>
            <p class="success-subtitle">Your account is now fully activated</p>
        </div>

        <div class="success-body">
            <p class="success-message">
                🎉 Congratulations! Your email has been successfully verified.
            </p>
            
            <div class="user-email">
                <i class="fas fa-envelope-circle-check"></i> {{ Auth::user()->email ?? 'Your email' }}
            </div>

            <div class="benefits">
                <div class="benefit">
                    <div class="benefit-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <span>Start shopping and add items to your cart</span>
                </div>
                <div class="benefit">
                    <div class="benefit-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <span>Secure checkout with multiple payment options</span>
                </div>
                <div class="benefit">
                    <div class="benefit-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <span>Track your orders and delivery status</span>
                </div>
                <div class="benefit">
                    <div class="benefit-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <span>Access exclusive deals and promotions</span>
                </div>
            </div>

            <div class="action-buttons">
                <a href="{{ route('products.index') }}" class="btn btn-primary">
                    <i class="fas fa-shopping-bag"></i> Start Shopping
                </a>
                <a href="{{ route('home') }}" class="btn btn-secondary">
                    <i class="fas fa-home"></i> Go to Home
                </a>
            </div>
        </div>
    </div>

    <script>
        // Auto redirect after 5 seconds
        setTimeout(function() {
            window.location.href = '{{ route("home") }}';
        }, 5000);

        // Show success notification
        if (typeof window !== 'undefined') {
            // Create a success toast notification
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0, 184, 148, 0.3);
                z-index: 10000;
                font-weight: 600;
                animation: slideInRight 0.5s ease-out;
            `;
            toast.innerHTML = '<i class="fas fa-check-circle"></i> Account verified successfully!';
            document.body.appendChild(toast);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
