<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e(config('app.name', 'Laravel')); ?> - Login</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/sass/app.scss', 'resources/js/app.js']); ?>

<style>
    /* Override main layout for login page */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        min-height: 100vh;
    }

    main {
        display: none !important;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    /* Login Page Specific Styles */
    .login-page {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        z-index: 998;
    }

    /* Header Override - Match main site exactly */
    .login-page .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        color: #2d3436;
        padding: 0.75rem 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1000;
    }

    .login-page .header-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .login-page .logo {
        font-size: 1.5rem;
        font-weight: bold;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .login-page .logo i {
        font-size: 1.2rem;
        color: #ffd700;
        -webkit-text-fill-color: #ffd700;
        background: none;
    }

    .login-page .nav-wrapper {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .login-page nav ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 1.5rem;
    }

    .login-page nav a {
        color: #333;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .login-page nav a:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
    }

    .login-page .auth-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .login-page .auth-buttons .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        font-size: 0.9rem;
    }

    .login-page .btn-login {
        color: #667eea;
        border-color: #667eea;
        background: transparent;
    }

    .login-page .btn-login:hover {
        background: #667eea;
        color: white;
    }

    .login-page .btn-register {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: transparent;
    }

    .login-page .btn-register:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }



        /* Login Container */
        .login-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
        }

        .login-card {
            background: #ffffff;
            border-radius: 15px;
            padding: 2.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            position: relative;
        }

        /* Login Header */
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .logo-icon {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .login-header h2 {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .login-header h3 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
        }

        /* Social Login */
        .social-login {
            margin-bottom: 2rem;
        }

        .social-btn {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            color: #333;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
            text-decoration: none;
            min-height: 48px;
        }

        .social-btn i {
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .google-btn:hover {
            border-color: #db4437;
            color: #db4437;
        }

        .facebook-btn:hover {
            border-color: #4267B2;
            color: #4267B2;
        }

        /* Divider */
        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 1rem;
            color: #666;
            font-size: 0.9rem;
        }

        /* Form Styles */
        .login-form {
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
            transform: translateY(-1px);
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 0.25rem;
            display: block;
        }

        /* Form Options */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            color: #666;
        }

        .remember-me input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .forgot-password {
            color: #ff6b6b;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #e55555;
        }

        /* Submit Button */
        .login-submit-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .login-submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        /* Register Link */
        .register-link {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }

        .register-link a {
            color: #ff6b6b;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .register-link a:hover {
            color: #e55555;
        }

        /* Login Tabs */
        .login-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-radius: 15px;
            background: #f8f9fa;
            padding: 0.25rem;
        }

        .tab-btn {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            background: transparent;
            color: #666;
            font-weight: 600;
            cursor: pointer;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-btn.active {
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tab-btn:hover:not(.active) {
            color: #333;
        }

        .login-tab-content {
            display: none;
        }

        .login-tab-content.active {
            display: block;
        }

        /* QR Code Login Styles */
        .qr-login-container {
            text-align: center;
        }

        .qr-code-section {
            margin-bottom: 2rem;
            position: relative;
        }

        .qr-code-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #e0e0e0;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #999;
            background: #f8f9fa;
        }

        .qr-code-placeholder i {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .qr-code-display {
            position: relative;
            display: inline-block;
        }

        .qr-code-display img {
            width: 200px;
            height: 200px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .qr-code-timer {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .qr-instructions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .qr-instructions h4 {
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .qr-instructions ol {
            margin: 0;
            padding-left: 1.5rem;
            color: #666;
        }

        .qr-instructions li {
            margin-bottom: 0.5rem;
        }

        .qr-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .qr-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }

        .qr-btn-primary {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .qr-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }

        .qr-btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e0e0e0;
        }

        .qr-btn-secondary:hover {
            border-color: #4ecdc4;
            color: #4ecdc4;
        }

        .qr-status {
            margin-bottom: 2rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 15px;
            font-weight: 600;
        }

        .status-indicator.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-indicator.scanning {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-indicator.success {
            background: #d4edda;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
        }



        /* OTP Login Styles */
        .otp-login-container {
            text-align: center;
        }

        .otp-instructions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .otp-instructions h4 {
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .otp-instructions p {
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        .otp-step {
            margin-bottom: 2rem;
        }

        .otp-sent-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .otp-sent-info i {
            color: #28a745;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .otp-sent-info p {
            color: #155724;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .otp-timer {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #856404;
            font-weight: 600;
            background: #fff3cd;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-flex;
        }

        .otp-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .otp-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            text-decoration: none;
        }

        .otp-btn-primary {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .otp-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }

        .otp-btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e0e0e0;
        }

        .otp-btn-secondary:hover {
            border-color: #4ecdc4;
            color: #4ecdc4;
        }

        .otp-btn-link {
            background: transparent;
            color: #666;
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }

        .otp-btn-link:hover {
            color: #4ecdc4;
        }

        .otp-status {
            margin-bottom: 2rem;
        }

        #otp-code {
            text-align: center;
            font-size: 1.5rem;
            letter-spacing: 0.3rem;
            font-weight: 600;
            font-family: 'Courier New', monospace;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
            }

            .auth-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-content {
                padding: 1rem;
            }

            .login-card {
                padding: 2rem;
            }

            .form-options {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .qr-actions {
                flex-direction: column;
            }

            .qr-code-placeholder,
            .qr-code-display img {
                width: 150px;
                height: 150px;
            }
        }
    </style>
</head>
<body>
<div class="login-page">
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <a href="<?php echo e(route('home')); ?>">
                    <i class="fas fa-store"></i>
                    <?php echo e(config('app.name', 'BelteiEcom')); ?>

                </a>
            </div>

            <div class="nav-wrapper">
                <nav>
                    <ul>
                        <li><a href="<?php echo e(route('home')); ?>"><i class="fas fa-home"></i> Home</a></li>
                        <li><a href="<?php echo e(route('products.index')); ?>"><i class="fas fa-box"></i> Products</a></li>
                    </ul>
                </nav>

                <div class="auth-buttons">
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-login">Login</a>
                    <a href="<?php echo e(route('register')); ?>" class="btn btn-register">Register</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="logo-container">
                        <i class="fas fa-shopping-bag logo-icon"></i>
                        <h2>BelteiEcom</h2>
                    </div>
                    <h3>Welcome Back!</h3>
                    <p>Sign in to your account to continue shopping</p>
                </div>

                <!-- Login Method Tabs -->
                <div class="login-tabs">
                    <button type="button" class="tab-btn active" onclick="switchTab('email')">
                        <i class="fas fa-envelope"></i> Email Login
                    </button>
                    <button type="button" class="tab-btn" onclick="switchTab('otp')">
                        <i class="fas fa-key"></i> One-Time Code
                    </button>
                    <button type="button" class="tab-btn" onclick="switchTab('qr')">
                        <i class="fas fa-qrcode"></i> QR Code Login
                    </button>
                </div>

                <!-- Email Login Tab -->
                <div id="email-login-tab" class="login-tab-content active">
                    <div class="social-login">
                        <a href="<?php echo e(route('auth.google')); ?>" class="social-btn google-btn">
                            <i class="fab fa-google"></i>
                            Continue with Google
                        </a>
                        <button class="social-btn facebook-btn" onclick="alert('Facebook login coming soon!')">
                            <i class="fab fa-facebook-f"></i>
                            Continue with Facebook
                        </button>
                    </div>

                    <div class="divider">
                        <span>or sign in with email</span>
                    </div>

                    <form method="POST" action="<?php echo e(route('login')); ?>" class="login-form">
                        <?php echo csrf_field(); ?>
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input id="email" type="email" name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus placeholder="Enter your email address">
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="error-message"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <input id="password" type="password" name="password" required autocomplete="current-password" placeholder="Enter your password">
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="error-message"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-options">
                            <label class="remember-me">
                                <input type="checkbox" name="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                Remember Me
                            </label>
                            <?php if(Route::has('password.request')): ?>
                                <a href="<?php echo e(route('password.request')); ?>" class="forgot-password">
                                    Forgot Password?
                                </a>
                            <?php endif; ?>
                        </div>

                        <button type="submit" class="login-submit-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                    </form>

                    <div class="register-link">
                        Don't have an account? <a href="<?php echo e(route('register')); ?>">Create one here</a>
                    </div>
                </div>

                <!-- QR Code Login Tab -->
                <div id="qr-login-tab" class="login-tab-content">
                    <div class="qr-login-container">
                        <div class="qr-code-section">
                            <div class="qr-code-placeholder" id="qr-code-placeholder">
                                <i class="fas fa-qrcode"></i>
                                <p>Click "Generate QR Code" to start</p>
                            </div>
                            <div class="qr-code-display" id="qr-code-display" style="display: none;">
                                <img id="qr-code-image" src="" alt="QR Code">
                                <div class="qr-code-timer">
                                    <i class="fas fa-clock"></i>
                                    <span id="qr-timer">5:00</span>
                                </div>
                            </div>
                        </div>

                        <div class="qr-instructions">
                            <h4><i class="fas fa-mobile-alt"></i> How to use QR Login:</h4>
                            <ol>
                                <li>Click "Generate QR Code" below</li>
                                <li>Open your mobile device and log in to your account</li>
                                <li>Go to Profile → Scan QR Code</li>
                                <li>Scan the QR code displayed here</li>
                                <li>Confirm the login on your mobile device</li>
                            </ol>
                        </div>

                        <div class="qr-actions">
                            <button type="button" class="qr-btn qr-btn-primary" id="generate-qr-btn" onclick="generateQrCode()">
                                <i class="fas fa-qrcode"></i> Generate QR Code
                            </button>
                            <button type="button" class="qr-btn qr-btn-secondary" id="refresh-qr-btn" onclick="refreshQrCode()" style="display: none;">
                                <i class="fas fa-sync-alt"></i> Refresh Code
                            </button>
                        </div>

                        <div class="qr-status" id="qr-status">
                            <div class="status-indicator pending" id="status-indicator">
                                <i class="fas fa-clock"></i>
                                <span id="status-text">Ready to generate QR code</span>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- OTP Login Tab -->
                <div id="otp-login-tab" class="login-tab-content">
                    <div class="otp-login-container">
                        <div class="otp-instructions">
                            <h4><i class="fas fa-key"></i> One-Time Code Login:</h4>
                            <p>Enter your email address to receive a secure 6-digit code. The code will be sent only if your email is registered in our system.</p>
                        </div>

                        <!-- Email Input Step -->
                        <div id="otp-email-step" class="otp-step">
                            <div class="form-group">
                                <label for="otp-email">Email Address</label>
                                <input type="email" id="otp-email" placeholder="Enter your email address" required>
                            </div>
                            <button type="button" class="otp-btn otp-btn-primary" id="send-otp-btn" onclick="sendOtp()">
                                <i class="fas fa-paper-plane"></i> Send Code
                            </button>
                        </div>

                        <!-- Code Verification Step -->
                        <div id="otp-verify-step" class="otp-step" style="display: none;">
                            <div class="otp-sent-info">
                                <i class="fas fa-check-circle"></i>
                                <p>Code has been sent to <strong id="otp-sent-email"></strong></p>
                                <div class="otp-timer">
                                    <i class="fas fa-clock"></i>
                                    <span>Expires in <span id="otp-countdown">1:00</span></span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="otp-code">6-Digit Code</label>
                                <input type="text" id="otp-code" placeholder="Enter 6-digit code" maxlength="6" pattern="[0-9]{6}">
                            </div>

                            <div class="otp-actions">
                                <button type="button" class="otp-btn otp-btn-primary" id="verify-otp-btn" onclick="verifyOtp()">
                                    <i class="fas fa-check"></i> Verify Code
                                </button>
                                <button type="button" class="otp-btn otp-btn-secondary" id="resend-otp-btn" onclick="resendOtp()">
                                    <i class="fas fa-sync-alt"></i> Resend Code
                                </button>
                            </div>

                            <button type="button" class="otp-btn otp-btn-link" onclick="resetOtpForm()">
                                <i class="fas fa-arrow-left"></i> Use Different Email
                            </button>
                        </div>

                        <div class="otp-status" id="otp-status">
                            <div class="status-indicator ready" id="otp-status-indicator">
                                <i class="fas fa-info-circle"></i>
                                <span id="otp-status-text">Enter your email to get started</span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
let qrSession = null;
let qrTimer = null;
let statusPoller = null;

// Tab switching functionality
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.login-tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-login-tab`).classList.add('active');

    // Clean up QR session if switching away from QR tab
    if (tabName !== 'qr' && qrSession) {
        cancelQrSession();
    }
}

// Generate QR code
async function generateQrCode() {
    try {
        updateStatus('pending', 'Generating QR code...');

        const response = await fetch('<?php echo e(route("qr-login.generate")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            }
        });

        const data = await response.json();

        if (data.success) {
            qrSession = data;
            displayQrCode(data.qr_image);
            startTimer(300); // 5 minutes
            startStatusPolling(data.token);
            updateStatus('scanning', 'Scan the QR code with your mobile device');

            // Update buttons
            document.getElementById('generate-qr-btn').style.display = 'none';
            document.getElementById('refresh-qr-btn').style.display = 'inline-flex';
        } else {
            updateStatus('error', 'Failed to generate QR code');
        }
    } catch (error) {
        updateStatus('error', 'Network error occurred');
    }
}

// Display QR code
function displayQrCode(imageUrl) {
    document.getElementById('qr-code-placeholder').style.display = 'none';
    document.getElementById('qr-code-display').style.display = 'block';
    document.getElementById('qr-code-image').src = imageUrl;
}

// Start countdown timer
function startTimer(seconds) {
    let timeLeft = seconds;
    const timerElement = document.getElementById('qr-timer');

    qrTimer = setInterval(() => {
        const minutes = Math.floor(timeLeft / 60);
        const secs = timeLeft % 60;
        timerElement.textContent = `${minutes}:${secs.toString().padStart(2, '0')}`;

        if (timeLeft <= 0) {
            clearInterval(qrTimer);
            expireQrCode();
        }

        timeLeft--;
    }, 1000);
}

// Start polling for status updates
function startStatusPolling(token) {
    statusPoller = setInterval(async () => {
        try {
            const response = await fetch('<?php echo e(route("qr-login.status")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({ token })
            });

            const data = await response.json();

            if (data.success) {
                if (data.status === 'scanned') {
                    updateStatus('scanning', `Scanned by ${data.user.name}. Waiting for confirmation...`);
                } else if (data.status === 'authenticated') {
                    updateStatus('success', 'Authentication successful! Logging you in...');
                    clearInterval(statusPoller);
                    clearInterval(qrTimer);

                    // Authenticate the user
                    authenticateUser(token);
                } else if (data.status === 'expired' || data.status === 'cancelled') {
                    expireQrCode();
                }
            } else if (data.status === 'expired') {
                expireQrCode();
            }
        } catch (error) {
            console.error('Status polling error:', error);
        }
    }, 2000); // Poll every 2 seconds
}

// Authenticate user
async function authenticateUser(token) {
    try {
        const response = await fetch('<?php echo e(route("qr-login.authenticate")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ token })
        });

        const data = await response.json();

        if (data.success) {
            window.location.href = data.redirect_url;
        } else {
            updateStatus('error', data.message || 'Authentication failed');
        }
    } catch (error) {
        updateStatus('error', 'Authentication error occurred');
    }
}

// Expire QR code
function expireQrCode() {
    clearInterval(qrTimer);
    clearInterval(statusPoller);
    updateStatus('error', 'QR code has expired');

    // Reset UI
    document.getElementById('qr-code-display').style.display = 'none';
    document.getElementById('qr-code-placeholder').style.display = 'flex';
    document.getElementById('generate-qr-btn').style.display = 'inline-flex';
    document.getElementById('refresh-qr-btn').style.display = 'none';

    qrSession = null;
}

// Refresh QR code
function refreshQrCode() {
    cancelQrSession();
    generateQrCode();
}

// Cancel QR session
function cancelQrSession() {
    if (qrSession) {
        fetch('<?php echo e(route("qr-login.cancel")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ token: qrSession.token })
        });
    }

    clearInterval(qrTimer);
    clearInterval(statusPoller);
    qrSession = null;
}

// Update status display
function updateStatus(type, message) {
    const indicator = document.getElementById('status-indicator');
    const text = document.getElementById('status-text');

    indicator.className = `status-indicator ${type}`;
    text.textContent = message;
}



// Clean up on page unload
window.addEventListener('beforeunload', () => {
    cancelQrSession();
    clearOtpTimer();
});

// OTP Login Functions
let otpTimer = null;
let otpCountdown = 60;

// Send OTP code
async function sendOtp() {
    const email = document.getElementById('otp-email').value.trim();

    if (!email) {
        updateOtpStatus('error', 'Please enter your email address');
        return;
    }

    if (!isValidEmail(email)) {
        updateOtpStatus('error', 'Please enter a valid email address');
        return;
    }

    try {
        updateOtpStatus('pending', 'Sending code...');
        document.getElementById('send-otp-btn').disabled = true;

        const response = await fetch('<?php echo e(route("otp.send")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ email })
        });

        const data = await response.json();

        if (data.success) {
            // Show verification step
            document.getElementById('otp-email-step').style.display = 'none';
            document.getElementById('otp-verify-step').style.display = 'block';
            document.getElementById('otp-sent-email').textContent = email;

            // Start countdown timer
            startOtpTimer();

            updateOtpStatus('success', 'Code sent successfully!');
        } else {
            updateOtpStatus('error', data.message || 'Failed to send code');
            document.getElementById('send-otp-btn').disabled = false;
        }
    } catch (error) {
        updateOtpStatus('error', 'Network error occurred');
        document.getElementById('send-otp-btn').disabled = false;
    }
}

// Verify OTP code
async function verifyOtp() {
    const email = document.getElementById('otp-sent-email').textContent;
    const code = document.getElementById('otp-code').value.trim();

    if (!code || code.length !== 6) {
        updateOtpStatus('error', 'Please enter a valid 6-digit code');
        return;
    }

    try {
        updateOtpStatus('pending', 'Verifying code...');
        document.getElementById('verify-otp-btn').disabled = true;

        const response = await fetch('<?php echo e(route("otp.verify")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ email, code })
        });

        const data = await response.json();

        if (data.success) {
            updateOtpStatus('success', 'Login successful! Redirecting...');
            clearOtpTimer();

            setTimeout(() => {
                window.location.href = data.redirect_url;
            }, 1500);
        } else {
            updateOtpStatus('error', data.message || 'Invalid code');
            document.getElementById('verify-otp-btn').disabled = false;
        }
    } catch (error) {
        updateOtpStatus('error', 'Network error occurred');
        document.getElementById('verify-otp-btn').disabled = false;
    }
}

// Resend OTP code
async function resendOtp() {
    const email = document.getElementById('otp-sent-email').textContent;

    try {
        updateOtpStatus('pending', 'Resending code...');
        document.getElementById('resend-otp-btn').disabled = true;

        const response = await fetch('<?php echo e(route("otp.resend")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ email })
        });

        const data = await response.json();

        if (data.success) {
            // Reset countdown timer
            clearOtpTimer();
            startOtpTimer();

            updateOtpStatus('success', 'Code resent successfully!');
        } else {
            updateOtpStatus('error', data.message || 'Failed to resend code');
        }

        document.getElementById('resend-otp-btn').disabled = false;
    } catch (error) {
        updateOtpStatus('error', 'Network error occurred');
        document.getElementById('resend-otp-btn').disabled = false;
    }
}

// Reset OTP form
function resetOtpForm() {
    clearOtpTimer();
    document.getElementById('otp-email-step').style.display = 'block';
    document.getElementById('otp-verify-step').style.display = 'none';
    document.getElementById('otp-email').value = '';
    document.getElementById('otp-code').value = '';
    document.getElementById('send-otp-btn').disabled = false;
    document.getElementById('verify-otp-btn').disabled = false;
    updateOtpStatus('ready', 'Enter your email to get started');
}

// Start OTP countdown timer
function startOtpTimer() {
    otpCountdown = 60;
    updateCountdown();

    otpTimer = setInterval(() => {
        otpCountdown--;
        updateCountdown();

        if (otpCountdown <= 0) {
            clearOtpTimer();
            updateOtpStatus('error', 'Code has expired. Please request a new one.');
        }
    }, 1000);
}

// Clear OTP timer
function clearOtpTimer() {
    if (otpTimer) {
        clearInterval(otpTimer);
        otpTimer = null;
    }
}

// Update countdown display
function updateCountdown() {
    const minutes = Math.floor(otpCountdown / 60);
    const seconds = otpCountdown % 60;
    const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    const countdownElement = document.getElementById('otp-countdown');
    if (countdownElement) {
        countdownElement.textContent = display;
    }
}

// Update OTP status
function updateOtpStatus(type, message) {
    const indicator = document.getElementById('otp-status-indicator');
    const text = document.getElementById('otp-status-text');

    indicator.className = `status-indicator ${type}`;
    text.textContent = message;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Format OTP code input
document.getElementById('otp-code').addEventListener('input', function(e) {
    // Only allow digits
    e.target.value = e.target.value.replace(/\D/g, '');
});

// Auto-submit when 6 digits entered
document.getElementById('otp-code').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        setTimeout(() => {
            verifyOtp();
        }, 500);
    }
});
</script>

</body>
</html>
<?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/auth/login.blade.php ENDPATH**/ ?>