# 🛒 Shopping Cart Flow - BelteiEcom

## 📋 Shopping Cart System Overview

The BelteiEcom shopping cart provides a seamless, real-time shopping experience with AJAX-powered updates, persistent storage, and intelligent inventory management.

## 🛍️ Product Browsing & Selection

### 1. Product Discovery Flow

```mermaid
graph TD
    A[User visits homepage] --> B[Browse categories]
    A --> C[Use search function]
    B --> D[View category products]
    C --> D
    D --> E[Click product card]
    E --> F[View product details]
    F --> G[Select quantity]
    G --> H[Click 'Add to Cart']
    H --> I[AJAX add to cart]
    I --> J[Update cart counter]
    J --> K[Show success notification]
```

### Product Display Features
- **Product Grid** - Responsive grid layout with product cards
- **Product Images** - High-quality images with hover effects
- **Price Display** - Clear pricing with currency formatting
- **Stock Status** - Real-time stock availability indicators
- **Quick Actions** - Add to cart directly from product grid

### Search & Filtering
- **Live Search** - Auto-complete search with product suggestions
- **Category Filtering** - Filter products by category
- **Price Range** - Filter by minimum and maximum price
- **Stock Filter** - Show only in-stock products
- **Sort Options** - Sort by price, name, or popularity

## 🛒 Add to Cart Process

### 1. AJAX Add to Cart Flow

```mermaid
graph TD
    A[User clicks 'Add to Cart'] --> B[Validate product availability]
    B --> C{Product in stock?}
    C -->|Yes| D[Check existing cart item]
    C -->|No| E[Show out of stock message]
    D --> F{Item already in cart?}
    F -->|Yes| G[Update quantity]
    F -->|No| H[Create new cart item]
    G --> I[Validate total quantity vs stock]
    H --> I
    I --> J{Quantity available?}
    J -->|Yes| K[Save to database]
    J -->|No| L[Show quantity limit message]
    K --> M[Update cart counter]
    M --> N[Show success notification]
    N --> O[Update cart dropdown]
```

### Add to Cart Features
- **Real-time Updates** - Instant cart updates without page reload
- **Stock Validation** - Prevent adding out-of-stock items
- **Quantity Limits** - Respect available inventory
- **Visual Feedback** - Loading states and success animations
- **Error Handling** - Clear error messages for failed additions

### Cart Item Management
```php
// Cart item structure
{
    'id': 'cart_item_id',
    'user_id': 'authenticated_user_id',
    'product_id': 'product_id',
    'quantity': 'selected_quantity',
    'created_at': 'timestamp',
    'updated_at': 'timestamp'
}
```

## 🛒 Cart Page Experience

### 1. Cart Management Flow

```mermaid
graph TD
    A[User visits cart page] --> B[Load cart items]
    B --> C[Display cart contents]
    C --> D[User modifies quantity]
    D --> E[AJAX update quantity]
    E --> F[Validate new quantity]
    F --> G{Quantity valid?}
    G -->|Yes| H[Update database]
    G -->|No| I[Show error message]
    H --> J[Recalculate totals]
    J --> K[Update cart display]
    I --> L[Revert to previous quantity]
    K --> M[Show updated totals]
```

### Cart Page Features
- **Item List** - Detailed view of all cart items
- **Product Information** - Name, image, price, and description
- **Quantity Controls** - Increase/decrease quantity buttons
- **Remove Items** - Delete items from cart
- **Price Calculations** - Subtotal, tax, and total calculations

### Quantity Management
- **Inline Editing** - Direct quantity input fields
- **Increment/Decrement** - Plus/minus buttons
- **Stock Validation** - Real-time stock checking
- **Auto-Save** - Automatic saving of quantity changes
- **Undo Functionality** - Revert accidental changes

## 💰 Price Calculations

### 1. Cart Totals Calculation

```mermaid
graph TD
    A[Cart Items] --> B[Calculate Item Subtotals]
    B --> C[Sum All Subtotals]
    C --> D[Apply Discounts]
    D --> E[Calculate Tax]
    E --> F[Add Shipping Costs]
    F --> G[Calculate Final Total]
    G --> H[Display Breakdown]
```

### Price Components
- **Item Price** - Individual product price
- **Quantity** - Number of items
- **Subtotal** - Price × Quantity per item
- **Cart Subtotal** - Sum of all item subtotals
- **Discounts** - Applied coupon or promotion discounts
- **Tax** - Calculated tax amount
- **Shipping** - Delivery charges
- **Grand Total** - Final amount to pay

### Dynamic Price Updates
```javascript
// Real-time price calculation
function updateCartTotals() {
    let subtotal = 0;
    cartItems.forEach(item => {
        subtotal += item.price * item.quantity;
    });
    
    let tax = subtotal * TAX_RATE;
    let shipping = calculateShipping(subtotal);
    let total = subtotal + tax + shipping;
    
    updateDisplay(subtotal, tax, shipping, total);
}
```

## 🔄 Cart Persistence

### 1. Cart Storage Strategy

```mermaid
graph TD
    A[User Action] --> B{User Authenticated?}
    B -->|Yes| C[Save to Database]
    B -->|No| D[Save to Session]
    C --> E[Associate with User ID]
    D --> F[Store in Session Data]
    E --> G[Persist Across Sessions]
    F --> H[Temporary Storage]
    H --> I{User Logs In?}
    I -->|Yes| J[Migrate Session Cart to Database]
    I -->|No| K[Cart Expires with Session]
```

### Storage Methods
- **Authenticated Users** - Database storage with user association
- **Guest Users** - Session-based storage
- **Cart Migration** - Transfer guest cart to user account on login
- **Expiration** - Automatic cleanup of old cart items

### Database Schema
```sql
CREATE TABLE cart_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NULL,
    session_id VARCHAR(255) NULL,
    product_id BIGINT NOT NULL,
    quantity INT NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

## 🛒 Cart Dropdown/Mini Cart

### 1. Mini Cart Features

```mermaid
graph TD
    A[User hovers cart icon] --> B[Show mini cart dropdown]
    B --> C[Display recent items]
    C --> D[Show cart summary]
    D --> E[Quick actions available]
    E --> F[View Cart button]
    E --> G[Checkout button]
    E --> H[Remove item buttons]
```

### Mini Cart Components
- **Cart Counter** - Number of items in cart
- **Recent Items** - Last few items added
- **Quick Remove** - Remove items without leaving page
- **Subtotal** - Current cart value
- **Action Buttons** - View cart and checkout links

### Real-time Updates
- **Add to Cart** - Instantly updates mini cart
- **Quantity Changes** - Reflects quantity modifications
- **Item Removal** - Removes items from dropdown
- **Price Updates** - Shows current totals

## 🚫 Cart Validation & Error Handling

### 1. Validation Rules

```mermaid
graph TD
    A[Cart Action] --> B[Validate Product Exists]
    B --> C[Check Product Active]
    C --> D[Verify Stock Availability]
    D --> E[Validate Quantity Limits]
    E --> F[Check User Permissions]
    F --> G{All Validations Pass?}
    G -->|Yes| H[Execute Action]
    G -->|No| I[Show Error Message]
    I --> J[Suggest Alternative]
```

### Error Scenarios
- **Product Not Found** - Product deleted or deactivated
- **Out of Stock** - Insufficient inventory
- **Quantity Limits** - Exceeds maximum allowed quantity
- **Price Changes** - Product price updated since adding to cart
- **Session Expired** - Guest cart session timeout

### Error Messages
- **User-Friendly** - Clear, actionable error messages
- **Contextual** - Specific to the error type
- **Suggestions** - Alternative actions or products
- **Recovery Options** - Ways to resolve the issue

## 📱 Mobile Cart Experience

### Mobile Optimizations
- **Touch-Friendly** - Large buttons and touch targets
- **Swipe Actions** - Swipe to remove items
- **Responsive Design** - Optimized for small screens
- **Fast Loading** - Minimal data usage
- **Offline Support** - Basic functionality without internet

### Mobile-Specific Features
- **Sticky Cart Bar** - Always visible cart access
- **Quick Add** - One-tap add to cart
- **Image Zoom** - Pinch to zoom product images
- **Voice Search** - Voice-activated product search

## 🔒 Cart Security

### Security Measures
- **CSRF Protection** - All cart actions protected
- **User Validation** - Verify cart ownership
- **Input Sanitization** - Clean all user inputs
- **Rate Limiting** - Prevent cart spam
- **Session Security** - Secure session handling

### Data Protection
- **Encryption** - Sensitive cart data encrypted
- **Access Control** - User-specific cart access
- **Audit Logging** - Track cart modifications
- **Privacy Compliance** - GDPR-compliant data handling

## 📊 Cart Analytics

### Tracking Metrics
- **Cart Abandonment Rate** - Percentage of abandoned carts
- **Average Cart Value** - Mean cart total amount
- **Items per Cart** - Average number of items
- **Conversion Rate** - Cart to order conversion
- **Popular Products** - Most added items

### Behavioral Analysis
- **Add to Cart Events** - Track product additions
- **Quantity Changes** - Monitor quantity modifications
- **Removal Patterns** - Analyze item removals
- **Time in Cart** - How long items stay in cart
- **Checkout Progression** - Cart to checkout flow
