<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some products and users
        $products = Product::take(5)->get();
        $users = User::where('is_admin', false)->take(10)->get();

        // If no regular users exist, create some
        if ($users->isEmpty()) {
            $users = collect([
                User::create([
                    'name' => 'John Customer',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'badge' => 'verified',
                ]),
                User::create([
                    'name' => 'Sarah Premium',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'badge' => 'premium',
                ]),
                User::create([
                    'name' => 'Mike VIP',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'badge' => 'vip',
                ]),
                User::create([
                    'name' => 'Lisa Regular',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]),
            ]);
        }

        $reviewData = [
            [
                'rating' => 5,
                'title' => 'Absolutely amazing product!',
                'comment' => 'I\'ve been using this product for a few weeks now and I\'m completely satisfied. The quality is outstanding and it exceeded my expectations. Highly recommended!',
                'is_featured' => true,
            ],
            [
                'rating' => 4,
                'title' => 'Great value for money',
                'comment' => 'Really good product overall. The build quality is solid and it works exactly as described. Only minor complaint is the packaging could be better.',
                'is_featured' => false,
            ],
            [
                'rating' => 5,
                'title' => 'Perfect!',
                'comment' => 'Everything I was looking for and more. Fast shipping, great customer service, and the product itself is top-notch. Will definitely buy again!',
                'is_featured' => true,
            ],
            [
                'rating' => 3,
                'title' => 'It\'s okay',
                'comment' => 'The product is decent but nothing special. It does what it\'s supposed to do but I expected a bit more for the price. Not bad, not great.',
                'is_featured' => false,
            ],
            [
                'rating' => 4,
                'title' => 'Good purchase',
                'comment' => 'Happy with this purchase. The product arrived quickly and was well-packaged. Quality is good and it\'s been working perfectly so far.',
                'is_featured' => false,
            ],
            [
                'rating' => 5,
                'title' => 'Exceeded expectations!',
                'comment' => 'Wow! This product is even better than I thought it would be. The attention to detail is incredible and you can tell it\'s made with quality materials.',
                'is_featured' => true,
            ],
            [
                'rating' => 2,
                'title' => 'Not what I expected',
                'comment' => 'Unfortunately, this product didn\'t meet my expectations. The quality feels cheap and it doesn\'t work as well as advertised. Considering returning it.',
                'is_featured' => false,
            ],
            [
                'rating' => 4,
                'title' => 'Solid choice',
                'comment' => 'This is a reliable product that does exactly what it promises. No complaints here. Good build quality and reasonable price point.',
                'is_featured' => false,
            ],
        ];

        foreach ($products as $product) {
            // Add 2-4 reviews per product
            $numReviews = rand(2, 4);
            $usedUsers = [];

            for ($i = 0; $i < $numReviews; $i++) {
                // Get a random user that hasn't reviewed this product yet
                do {
                    $user = $users->random();
                } while (in_array($user->id, $usedUsers));

                $usedUsers[] = $user->id;
                $reviewInfo = $reviewData[array_rand($reviewData)];

                Review::create([
                    'user_id' => $user->id,
                    'product_id' => $product->id,
                    'rating' => $reviewInfo['rating'],
                    'title' => $reviewInfo['title'],
                    'comment' => $reviewInfo['comment'],
                    'is_approved' => true,
                    'is_featured' => $reviewInfo['is_featured'],
                    'approved_at' => now(),
                    'approved_by' => User::where('is_admin', true)->first()->id ?? null,
                ]);
            }
        }

        $this->command->info('Created reviews for ' . $products->count() . ' products');
    }
}
