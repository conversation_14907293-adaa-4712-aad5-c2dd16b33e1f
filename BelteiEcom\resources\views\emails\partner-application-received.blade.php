<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner Application Received</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 2rem;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .welcome-section h2 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .status-badge {
            display: inline-block;
            background: #ffc107;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            margin: 1rem 0;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 4px solid #667eea;
        }

        .info-card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        .info-label {
            font-weight: 600;
            color: #666;
        }

        .info-value {
            color: #333;
        }

        .next-steps {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .next-steps h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }

        .next-steps ul {
            list-style: none;
            padding: 0;
        }

        .next-steps li {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .next-steps li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #1976d2;
            font-weight: bold;
        }

        .timeline {
            background: #fff3cd;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }

        .timeline h3 {
            color: #856404;
            margin-bottom: 1rem;
        }

        .timeline p {
            color: #856404;
            font-weight: 500;
        }

        .contact-section {
            background: #d4edda;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }

        .contact-section h3 {
            color: #155724;
            margin-bottom: 1rem;
        }

        .contact-section p {
            color: #155724;
        }

        .contact-section a {
            color: #155724;
            font-weight: 600;
            text-decoration: none;
        }

        .footer {
            background: #f8f9fa;
            padding: 2rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .social-links {
            margin-top: 1rem;
        }

        .social-links a {
            display: inline-block;
            margin: 0 0.5rem;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .content {
                padding: 1rem;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .info-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🤝 BelteiEcom Partners</h1>
            <p>Your Gateway to Success</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Welcome Section -->
            <div class="welcome-section">
                <h2>Thank You for Your Application!</h2>
                <p>Dear {{ $partner->contact_name }},</p>
                <div class="status-badge">
                    📋 Application Received
                </div>
                <p>We've successfully received your partner application for <strong>{{ $partner->company_name }}</strong>.</p>
            </div>

            <!-- Application Details -->
            <div class="info-card">
                <h3>📋 Application Details</h3>
                <div class="info-row">
                    <span class="info-label">Company:</span>
                    <span class="info-value">{{ $partner->company_name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Contact:</span>
                    <span class="info-value">{{ $partner->contact_name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ $partner->email }}</span>
                </div>
                @if($partner->website)
                <div class="info-row">
                    <span class="info-label">Website:</span>
                    <span class="info-value">{{ $partner->website }}</span>
                </div>
                @endif
                <div class="info-row">
                    <span class="info-label">Application Date:</span>
                    <span class="info-value">{{ $partner->created_at->format('F j, Y') }}</span>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h3>🚀 What Happens Next?</h3>
                <ul>
                    <li>Our team will review your application within 1-2 business days</li>
                    <li>We'll verify your business information and website</li>
                    <li>You'll receive an email with our decision</li>
                    <li>If approved, you'll get your API credentials and documentation</li>
                    <li>Our integration team will help you get started</li>
                </ul>
            </div>

            <!-- Timeline -->
            <div class="timeline">
                <h3>⏰ Expected Timeline</h3>
                <p><strong>1-2 Business Days</strong> for application review</p>
            </div>

            <!-- Contact Section -->
            <div class="contact-section">
                <h3>💬 Questions?</h3>
                <p>Our partner success team is here to help!</p>
                <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>Phone: <a href="tel:+1234567890">+****************</a></p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>BelteiEcom Partner Program</strong></p>
            <p>Building successful partnerships, one integration at a time.</p>
            
            <div class="social-links">
                <a href="#">Documentation</a>
                <a href="#">Support</a>
                <a href="#">Community</a>
            </div>
            
            <p style="margin-top: 1rem; font-size: 0.8rem;">
                This email was sent to {{ $partner->email }} regarding your partner application.
            </p>
        </div>
    </div>
</body>
</html>
