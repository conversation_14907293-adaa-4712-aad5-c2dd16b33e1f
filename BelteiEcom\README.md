# 🛒 BelteiEcom - Laravel E-commerce Platform

A comprehensive e-commerce platform built with Laravel 12, featuring modern payment integration, admin management, and advanced location services.

## 🌟 Features

### 🛍️ Customer Features
- **Product Browsing**: Browse products by categories with search functionality
- **Shopping Cart**: Add, update, and remove items from cart
- **Secure Checkout**: Multiple payment methods including Bakong KHQR
- **Order Tracking**: Track order status and history
- **User Authentication**: Registration, login, email verification
- **Profile Management**: Update personal information and addresses
- **Advanced Location Services**: GPS location, map pin selection, address search

### 👨‍💼 Admin Features
- **Dashboard Analytics**: Sales metrics, order statistics, customer insights
- **Product Management**: CRUD operations for products and categories
- **Order Management**: View, update, and track all orders
- **Customer Management**: Manage customer accounts and verification
- **Inventory Tracking**: Monitor stock levels and low stock alerts
- **Real-time Notifications**: Telegram integration for order alerts

### 💳 Payment Integration
- **Bakong KHQR**: Cambodia's national payment system
- **QR Code Generation**: Dynamic QR codes for payments
- **Transaction Verification**: Real-time payment status checking
- **Multiple Payment Methods**: Support for various payment options

### 📍 Location Services
- **Address Search**: Autocomplete with Mapbox/OpenStreetMap
- **GPS Location**: Current location detection
- **Interactive Maps**: Pin location on map for precise addresses
- **Manual Entry**: Traditional text input as fallback

## 🚀 Quick Start

### Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js & NPM
- MySQL/SQLite database
- Web server (Apache/Nginx) or use Laravel's built-in server

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd BelteiEcom
```

2. **Install PHP dependencies**
```bash
composer install
```

3. **Install Node.js dependencies**
```bash
npm install
```

4. **Environment setup**
```bash
cp .env.example .env
php artisan key:generate
```

5. **Configure your `.env` file**
```env
# Database Configuration
DB_CONNECTION=sqlite
# OR for MySQL:
# DB_CONNECTION=mysql
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=belteiecom
# DB_USERNAME=root
# DB_PASSWORD=

# Application Settings
APP_NAME="BelteiEcom"
APP_URL=http://localhost:8000

# Telegram Notifications (Optional)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Bakong Payment (Optional)
BAKONG_API_TOKEN=your_bakong_api_token_here
BAKONG_API_URL=https://api-bakong.nbc.gov.kh/v1
```

6. **Database setup**
```bash
# Create SQLite database (if using SQLite)
touch database/database.sqlite

# Run migrations and seeders
php artisan migrate --seed
```

7. **Build frontend assets**
```bash
npm run build
# OR for development with hot reload:
npm run dev
```

8. **Start the development server**
```bash
php artisan serve
```

Visit `http://localhost:8000` to access the application.

## 🔧 Development Setup

### Running in Development Mode
```bash
# Start all development services (recommended)
composer run dev
# This starts: server, queue worker, logs, and vite in parallel

# OR start services individually:
php artisan serve              # Web server
php artisan queue:work         # Queue worker
npm run dev                    # Frontend build with hot reload
php artisan pail               # Real-time logs
```

### Testing
```bash
# Run all tests
composer run test
# OR
php artisan test

# Run specific test suites
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit
```

## 📊 Default Accounts

After running seeders, you'll have these default accounts:

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Full admin dashboard and management features

### Customer Account
- **Email**: <EMAIL>
- **Password**: customer123
- **Access**: Shopping and order management

## 🗂️ Project Structure

```
BelteiEcom/
├── app/
│   ├── Http/Controllers/
│   │   ├── Admin/              # Admin panel controllers
│   │   ├── Api/                # API controllers
│   │   ├── CartController.php  # Shopping cart logic
│   │   ├── OrderController.php # Order processing
│   │   └── BakongController.php # Payment integration
│   ├── Models/                 # Eloquent models
│   ├── Services/               # Business logic services
│   └── Notifications/          # Custom notifications
├── database/
│   ├── migrations/             # Database schema
│   └── seeders/                # Sample data
├── resources/
│   ├── views/                  # Blade templates
│   ├── js/                     # Frontend JavaScript
│   └── sass/                   # Styling
├── routes/
│   ├── web.php                 # Web routes
│   └── api.php                 # API routes
└── public/                     # Public assets
```

## 🌐 User Flow

### Customer Journey
1. **Browse Products** → View homepage with featured products
2. **Product Discovery** → Search or browse by categories
3. **Add to Cart** → Select products and quantities
4. **Checkout Process** → Enter shipping details with location services
5. **Payment** → Choose payment method (Bakong KHQR, etc.)
6. **Order Confirmation** → Receive order confirmation and tracking
7. **Order Management** → Track order status in user dashboard

### Admin Workflow
1. **Dashboard Overview** → Monitor sales, orders, and analytics
2. **Product Management** → Add, edit, delete products and categories
3. **Order Processing** → Review and update order statuses
4. **Customer Support** → Manage customer accounts and inquiries
5. **Inventory Control** → Monitor stock levels and reorder alerts
6. **Analytics** → Review sales reports and performance metrics

## 🔌 API Endpoints

### Public APIs
- `GET /api/products` - List all products
- `GET /api/products/search` - Search products
- `GET /api/products/category/{id}` - Products by category
- `GET /api/products/{id}` - Product details

### Payment APIs
- `POST /api/bakong/check-transaction` - Verify payment status
- `POST /api/bakong/check-order-transaction` - Check order payment

## 🛡️ Security Features

- **CSRF Protection**: All forms protected against CSRF attacks
- **Email Verification**: Required for account activation
- **Admin Authentication**: Role-based access control
- **Secure Payments**: Encrypted payment processing
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Eloquent ORM prevents SQL injection

## 📱 Mobile Support

- **Responsive Design**: Works on all device sizes
- **Touch-friendly Interface**: Optimized for mobile interaction
- **GPS Integration**: Native mobile location services
- **Progressive Web App**: App-like experience on mobile

## 🔧 Configuration

### Location Services Setup
1. Get a free Mapbox API key from [mapbox.com](https://www.mapbox.com/)
2. Update the token in `/resources/views/orders/checkout.blade.php`
3. See `LOCATION_SETUP.md` for detailed configuration

### Telegram Notifications
1. Create a Telegram bot via @BotFather
2. Get your chat ID
3. Add credentials to `.env` file

### Bakong Payment
1. Register with National Bank of Cambodia
2. Obtain API credentials
3. Configure in `.env` file

## 🚨 Troubleshooting

### Common Issues

**Database Connection Error**
- Check database credentials in `.env`
- Ensure database server is running
- Verify database exists

**Permission Denied**
- Set proper file permissions: `chmod -R 755 storage bootstrap/cache`
- Ensure web server has write access

**Assets Not Loading**
- Run `npm run build` to compile assets
- Check if Vite is running for development

**Email Verification Not Working**
- Configure mail settings in `.env`
- Check spam folder for verification emails

## 📈 Performance Optimization

- **Database Indexing**: Optimized queries with proper indexes
- **Caching**: Redis/Memcached support for session and cache
- **Asset Optimization**: Vite for efficient asset bundling
- **Image Optimization**: Automatic image resizing and compression
- **Queue Processing**: Background job processing for heavy tasks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests to ensure everything works
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## �‍💻 Project Owner

**Tey Takakvitya**
📱 Telegram: [@T_vits](https://t.me/T_vits)

## �📞 Support

For support and questions:
- Check the documentation
- Review troubleshooting section
- Create an issue on GitHub
- Contact the project owner: [@T_vits](https://t.me/T_vits)

---

**Built with ❤️ by [Tey Takakvitya](https://t.me/T_vits) using Laravel 12, Bootstrap 5, and modern web technologies.**
