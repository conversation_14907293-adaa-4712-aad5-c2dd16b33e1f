@extends('layouts.app')

@section('title', isset($category) ? $category->name : 'All Products')

@section('styles')
<style>
    /* Override main container for products page */
    main .container {
        max-width: 1400px;
        padding: 0 2rem;
    }

    /* Products Page Header */
    .products-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 3rem 0 2rem;
        margin-bottom: 3rem;
        border-radius: 0 0 20px 20px;
    }

    .products-header-content {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .products-title {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-align: center;
    }

    .products-subtitle {
        text-align: center;
        color: #636e72;
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }

    /* Search Bar */
    .search-container {
        max-width: 600px;
        margin: 0 auto;
        position: relative;
    }

    /* Live Search Dropdown */
    .search-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        z-index: 1000;
        max-height: 400px;
        overflow-y: auto;
        display: none;
        border: 2px solid #667eea;
        border-top: none;
    }

    .search-dropdown.show {
        display: block;
    }

    .search-result-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
    }

    .search-result-item:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        text-decoration: none;
        color: inherit;
    }

    .search-result-item:last-child {
        border-bottom: none;
        border-radius: 0 0 18px 18px;
    }

    .search-result-image {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        object-fit: cover;
        margin-right: 1rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .search-result-info {
        flex: 1;
    }

    .search-result-name {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .search-result-price {
        color: #667eea;
        font-weight: 700;
        font-size: 0.9rem;
    }

    .search-no-results {
        padding: 2rem 1.5rem;
        text-align: center;
        color: #636e72;
        font-style: italic;
    }

    .search-loading {
        padding: 1rem 1.5rem;
        text-align: center;
        color: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .search-loading i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .search-form {
        display: flex;
        background: white;
        border-radius: 50px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .search-form:focus-within {
        border-color: #667eea;
        box-shadow: 0 10px 40px rgba(102, 126, 234, 0.2);
    }

    .search-input {
        flex: 1;
        padding: 1rem 1.5rem;
        border: none;
        outline: none;
        font-size: 1rem;
        background: transparent;
    }

    .search-btn {
        padding: 1rem 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .search-btn:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateX(-2px);
    }

    /* Main Content Layout */
    .products-layout {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: 3rem;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Sidebar Styles */
    .filters-sidebar {
        position: sticky;
        top: 2rem;
        height: fit-content;
    }

    .filter-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .filter-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .filter-title {
        font-size: 1.2rem;
        font-weight: 700;
        margin: 0;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-body {
        padding: 1.5rem;
    }

    .filter-group {
        margin-bottom: 1.5rem;
    }

    .filter-group:last-child {
        margin-bottom: 0;
    }

    .filter-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #2d3436;
        font-size: 0.9rem;
    }

    .filter-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .filter-input:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .filter-btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    /* Categories List */
    .categories-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .category-item {
        margin-bottom: 0.75rem;
    }

    .category-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: #636e72;
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .category-link:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(5px);
        text-decoration: none;
    }

    .category-link i {
        margin-right: 0.75rem;
        width: 16px;
        text-align: center;
    }

    .category-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(5px);
    }

    .category-link.active:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        color: white;
    }

    /* Hierarchical Categories */
    .parent-category {
        margin-bottom: 0.5rem;
    }

    .category-parent {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .category-parent .category-link {
        flex: 1;
        margin-bottom: 0;
    }

    .category-toggle {
        background: none;
        border: none;
        color: #636e72;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 5px;
        transition: all 0.3s ease;
        margin-left: 0.5rem;
    }

    .category-toggle:hover {
        background: #f8f9fa;
        color: #667eea;
    }

    .category-toggle i {
        transition: transform 0.3s ease;
    }

    .category-toggle.expanded i {
        transform: rotate(180deg);
    }

    .subcategories-list {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0 0 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .subcategories-list.expanded {
        max-height: 500px;
    }

    .subcategory-item {
        margin-bottom: 0.25rem;
    }

    .subcategory-link {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem 0.5rem 2rem;
        color: #636e72;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-weight: 400;
        font-size: 0.9rem;
        border-left: 3px solid transparent;
    }

    .subcategory-link:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        color: #667eea;
        transform: translateX(5px);
        text-decoration: none;
        border-left-color: #667eea;
    }

    .subcategory-link i {
        margin-right: 0.5rem;
        width: 12px;
        text-align: center;
        font-size: 0.8rem;
    }

    .subcategory-link.active {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
        color: #667eea;
        transform: translateX(5px);
        border-left-color: #667eea;
        font-weight: 600;
    }

    .subcategory-link.active:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
        color: #667eea;
    }

    /* Products Grid */
    .products-content {
        min-height: 500px;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .product-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.12);
        border-color: rgba(102, 126, 234, 0.1);
    }

    .product-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover::before {
        opacity: 1;
    }

    .product-image {
        position: relative;
        height: 220px;
        overflow: hidden;
        background: #f8f9fa;
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: all 0.3s ease;
        display: block;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .product-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 2;
    }

    .product-info {
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 160px;
    }

    .product-title {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 0.75rem;
        color: #2d3436;
        line-height: 1.4;
        flex-grow: 1;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .product-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .product-title a:hover {
        color: #667eea;
    }

    .product-price {
        font-size: 1.3rem;
        font-weight: 800;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .product-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
        margin-top: auto;
    }

    .product-btn {
        flex: 1;
        padding: 0.75rem 0.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.4rem;
        font-size: 0.85rem;
        min-height: 44px;
        text-align: center;
        white-space: nowrap;
    }

    .product-btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .product-btn-outline:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .product-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 2px solid transparent;
    }

    .product-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .product-btn-disabled {
        background: #e74c3c;
        color: white;
        cursor: not-allowed;
        border: 2px solid transparent;
    }

    .product-btn-disabled:hover {
        transform: none;
        box-shadow: none;
    }

    .product-btn-login {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: 2px solid transparent;
    }

    .product-btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .empty-icon {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1.5rem;
    }

    .empty-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 0.5rem;
    }

    .empty-description {
        color: #636e72;
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    /* Pagination */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: 0.5rem;
    }

    .pagination li a,
    .pagination li span {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        color: #636e72;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        min-width: 44px;
        height: 44px;
    }

    .pagination li.active span {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: transparent;
    }

    .pagination li a:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        transform: translateY(-2px);
        text-decoration: none;
    }

    .pagination li.disabled span {
        color: #adb5bd;
        cursor: not-allowed;
        opacity: 0.5;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .products-layout {
            grid-template-columns: 280px 1fr;
            gap: 2rem;
        }
    }

    @media (max-width: 992px) {
        .products-layout {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .filters-sidebar {
            position: static;
            order: 2;
        }

        .products-content {
            order: 1;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .products-title {
            font-size: 2.5rem;
        }

        .products-header {
            padding: 2rem 0 1.5rem;
        }

        .search-form {
            flex-direction: column;
            border-radius: 15px;
        }

        .search-btn {
            border-radius: 0 0 15px 15px;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .product-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .product-btn {
            padding: 0.75rem 1rem;
            font-size: 0.85rem;
        }
    }

    @media (max-width: 480px) {
        .products-title {
            font-size: 2rem;
        }

        .products-layout {
            padding: 0 1rem;
        }

        .products-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Add to Cart Animation */
    .add-to-cart-btn.loading {
        position: relative;
        color: transparent !important;
    }

    .add-to-cart-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .add-to-cart-btn.success {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Toast Notification */
    .toast-notification {
        position: fixed;
        top: 100px;
        right: 20px;
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        max-width: 350px;
        backdrop-filter: blur(10px);
    }

    .toast-notification.show {
        transform: translateX(0);
    }

    .toast-notification.error {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    }

    .toast-notification .toast-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .toast-notification .toast-icon {
        font-size: 1.2rem;
    }

    .toast-notification .toast-message {
        font-weight: 600;
        font-size: 0.9rem;
    }
</style>
@endsection

@section('content')
<!-- Products Header -->
<div class="products-header">
    <div class="products-header-content">
        <h1 class="products-title">{{ isset($category) ? $category->name : 'All Products' }}</h1>
        <p class="products-subtitle">
            @if(isset($category))
                @if($category->children->count() > 0)
                    Discover amazing products in {{ $category->name }} and all its subcategories
                @else
                    Discover amazing products in {{ $category->name }}
                @endif
            @else
                Discover our complete collection of amazing products
            @endif
        </p>

        <!-- Search Bar -->
        <div class="search-container">
            <form action="{{ route('products.index') }}" method="GET" class="search-form">
                <input type="text" name="search" id="live-search-input" value="{{ request('search') }}" placeholder="Search for products..." class="search-input" autocomplete="off">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i> Search
                </button>
            </form>

            <!-- Live Search Dropdown -->
            <div id="search-dropdown" class="search-dropdown">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="products-layout">
    <!-- Filters Sidebar -->
    <div class="filters-sidebar">
        <!-- Filters Card -->
        <div class="filter-card">
            <div class="filter-header">
                <h3 class="filter-title">
                    <i class="fas fa-filter"></i> Filters
                </h3>
            </div>
            <div class="filter-body">
                <form action="{{ route('products.index') }}" method="GET">
                    @if(request('search'))
                        <input type="hidden" name="search" value="{{ request('search') }}">
                    @endif

                    <div class="filter-group">
                        <label for="category" class="filter-label">Category</label>
                        <select name="category" id="category" class="filter-input">
                            <option value="">All Categories</option>
                            @foreach($categories as $parentCat)
                                <option value="{{ $parentCat->id }}" {{ request('category') == (string)$parentCat->id ? 'selected' : '' }}>
                                    {{ $parentCat->name }}
                                </option>
                                @foreach($parentCat->children as $childCat)
                                    <option value="{{ $childCat->id }}" {{ request('category') == (string)$childCat->id ? 'selected' : '' }}>
                                        &nbsp;&nbsp;&nbsp;&nbsp;{{ $childCat->name }}
                                    </option>
                                @endforeach
                            @endforeach
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="min_price" class="filter-label">Min Price ($)</label>
                        <input type="number" name="min_price" id="min_price" class="filter-input"
                               value="{{ request('min_price') }}" min="0" step="0.01" placeholder="0.00">
                    </div>

                    <div class="filter-group">
                        <label for="max_price" class="filter-label">Max Price ($)</label>
                        <input type="number" name="max_price" id="max_price" class="filter-input"
                               value="{{ request('max_price') }}" min="0" step="0.01" placeholder="1000.00">
                    </div>

                    <button type="submit" class="filter-btn">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                </form>
            </div>
        </div>

        <!-- Categories Card -->
        <div class="filter-card">
            <div class="filter-header">
                <h3 class="filter-title">
                    <i class="fas fa-list"></i> Categories
                </h3>
            </div>
            <div class="filter-body">
                <ul class="categories-list">
                    @foreach($categories as $parentCat)
                        <li class="category-item parent-category">
                            <div class="category-parent">
                                <a href="{{ route('products.category', $parentCat->slug) }}" class="category-link {{ isset($category) && $category->slug == $parentCat->slug ? 'active' : '' }}">
                                    <i class="fas fa-folder"></i> {{ $parentCat->name }}
                                </a>
                                @if($parentCat->children->count() > 0)
                                    <button class="category-toggle" onclick="toggleCategory('{{ $parentCat->slug }}')">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                @endif
                            </div>
                            @if($parentCat->children->count() > 0)
                                <ul class="subcategories-list" id="subcategories-{{ $parentCat->slug }}">
                                    @foreach($parentCat->children as $childCat)
                                        <li class="subcategory-item">
                                            <a href="{{ route('products.category', $childCat->slug) }}" class="subcategory-link {{ isset($category) && $category->slug == $childCat->slug ? 'active' : '' }}">
                                                <i class="fas fa-tag"></i> {{ $childCat->name }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>

    <!-- Products Content -->
    <div class="products-content">
        @if($products->isEmpty())
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="empty-title">No products found</h3>
                <p class="empty-description">
                    We couldn't find any products matching your criteria. Try adjusting your search or filter settings.
                </p>
            </div>
        @else
            <div class="products-grid">
                @foreach($products as $product)
                    <div class="product-card">
                        <div class="product-image">
                            @if($product->image)
                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                            @else
                                <img src="https://via.placeholder.com/300x220?text=No+Image&bg=f8f9fa&color=636e72" alt="No Image">
                            @endif
                            @if($product->stock < 10 && $product->stock > 0)
                                <div class="product-badge">Low Stock</div>
                            @elseif($product->stock == 0)
                                <div class="product-badge" style="background: #e74c3c;">Out of Stock</div>
                            @endif
                        </div>
                        <div class="product-info">
                            <h3 class="product-title">
                                <a href="{{ route('products.show', $product->slug) }}">{{ $product->name }}</a>
                            </h3>
                            <div class="product-price">${{ number_format($product->price, 2) }}</div>
                            <div class="product-actions">
                                <a href="{{ route('products.show', $product->slug) }}" class="product-btn product-btn-outline">
                                    <i class="fas fa-eye"></i> View Details
                                </a>

                                @auth
                                    @if($product->stock > 0)
                                        <form action="{{ route('cart.add') }}" method="POST" class="add-to-cart-form" style="flex: 1;" data-product-id="{{ $product->id }}" data-product-name="{{ $product->name }}">
                                            @csrf
                                            <input type="hidden" name="product_id" value="{{ $product->id }}">
                                            <input type="hidden" name="quantity" value="1">
                                            <button type="submit" class="product-btn product-btn-primary add-to-cart-btn" style="width: 100%;">
                                                <i class="fas fa-cart-plus"></i> Add Cart
                                            </button>
                                        </form>
                                    @else
                                        <button class="product-btn product-btn-disabled" disabled>
                                            <i class="fas fa-times"></i> Out of Stock
                                        </button>
                                    @endif
                                @else
                                    <a href="{{ route('login') }}" class="product-btn product-btn-login">
                                        <i class="fas fa-sign-in-alt"></i> Login to Buy
                                    </a>
                                @endauth
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                {{ $products->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle add to cart forms
    document.querySelectorAll('.add-to-cart-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const button = this.querySelector('.add-to-cart-btn');
            const originalText = button.innerHTML;
            const productName = this.dataset.productName;

            // Show loading state
            button.classList.add('loading');
            button.disabled = true;

            // Prepare form data
            const formData = new FormData(this);

            // Send AJAX request
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading state
                button.classList.remove('loading');
                button.disabled = false;

                if (data.success) {
                    // Show success state
                    button.classList.add('success');
                    button.innerHTML = '<i class="fas fa-check"></i> Added!';

                    // Update cart count in header if exists
                    updateCartCount(data.cart_count);

                    // Show success toast
                    showToast(data.message, 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.classList.remove('success');
                        button.innerHTML = originalText;
                    }, 2000);
                } else {
                    // Show error toast
                    showToast(data.message, 'error');
                    button.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Remove loading state
                button.classList.remove('loading');
                button.disabled = false;
                button.innerHTML = originalText;

                // Show error toast
                showToast('Something went wrong. Please try again.', 'error');
            });
        });
    });

    const searchInput = document.getElementById('live-search-input');
    const searchDropdown = document.getElementById('search-dropdown');
    let searchTimeout;

    // Live search functionality
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        // Clear previous timeout
        clearTimeout(searchTimeout);

        // Hide dropdown if query is too short
        if (query.length < 2) {
            hideDropdown();
            return;
        }

        // Show loading state
        showLoading();

        // Debounce the search request
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchDropdown.contains(e.target)) {
            hideDropdown();
        }
    });

    // Hide dropdown when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideDropdown();
        }
    });

    function performSearch(query) {
        fetch(`{{ route('products.live-search') }}?query=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displayResults(data);
            })
            .catch(error => {
                console.error('Search error:', error);
                hideDropdown();
            });
    }

    function displayResults(results) {
        if (results.length === 0) {
            searchDropdown.innerHTML = '<div class="search-no-results">No products found</div>';
        } else {
            const resultsHtml = results.map(product => `
                <a href="${product.url}" class="search-result-item">
                    <img src="${product.image}" alt="${product.name}" class="search-result-image">
                    <div class="search-result-info">
                        <div class="search-result-name">${product.name}</div>
                        <div class="search-result-price">$${product.price}</div>
                    </div>
                </a>
            `).join('');

            searchDropdown.innerHTML = resultsHtml;
        }

        showDropdown();
    }

    function showLoading() {
        searchDropdown.innerHTML = `
            <div class="search-loading">
                <i class="fas fa-spinner"></i>
                Searching...
            </div>
        `;
        showDropdown();
    }

    function showDropdown() {
        searchDropdown.classList.add('show');
    }

    function hideDropdown() {
        searchDropdown.classList.remove('show');
    }
});

// Category toggle functionality
function toggleCategory(categorySlug) {
    const subcategoriesList = document.getElementById('subcategories-' + categorySlug);
    const toggleButton = event.target.closest('.category-toggle');

    if (subcategoriesList.classList.contains('expanded')) {
        // Collapse
        subcategoriesList.classList.remove('expanded');
        toggleButton.classList.remove('expanded');
    } else {
        // Expand
        subcategoriesList.classList.add('expanded');
        toggleButton.classList.add('expanded');
    }
}

// Auto-expand category if we're viewing a subcategory
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a category page
    const currentUrl = window.location.pathname;
    const categoryMatch = currentUrl.match(/\/categories\/([^\/]+)/);

    if (categoryMatch) {
        const currentCategorySlug = categoryMatch[1];

        // Find if this category is a subcategory and expand its parent
        const subcategoryLink = document.querySelector(`a[href*="/categories/${currentCategorySlug}"]`);
        if (subcategoryLink && subcategoryLink.classList.contains('subcategory-link')) {
            const parentList = subcategoryLink.closest('.subcategories-list');
            if (parentList) {
                const parentSlug = parentList.id.replace('subcategories-', '');
                const toggleButton = document.querySelector(`button[onclick="toggleCategory('${parentSlug}')"]`);
                if (toggleButton) {
                    parentList.classList.add('expanded');
                    toggleButton.classList.add('expanded');
                }
            }
        }
    }
});

function updateCartCount(count) {
    // Update cart count in header if cart link exists
    const cartLink = document.querySelector('.cart-link');
    if (cartLink) {
        // Look for existing count badge or create one
        let countBadge = cartLink.querySelector('.cart-count');
        if (!countBadge) {
            countBadge = document.createElement('span');
            countBadge.className = 'cart-count';
            countBadge.style.cssText = `
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ff6b6b;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 0.7rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
            `;
            cartLink.style.position = 'relative';
            cartLink.appendChild(countBadge);
        }
        countBadge.textContent = count;

        // Animate the badge
        countBadge.style.transform = 'scale(1.3)';
        setTimeout(() => {
            countBadge.style.transform = 'scale(1)';
        }, 200);
    }
}

function showToast(message, type = 'success') {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => {
        toast.remove();
    });

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <div class="toast-icon">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            </div>
            <div class="toast-message">${message}</div>
        </div>
    `;

    // Add to page
    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Hide toast after 4 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 4000);
}
</script>
@endsection

@endsection
