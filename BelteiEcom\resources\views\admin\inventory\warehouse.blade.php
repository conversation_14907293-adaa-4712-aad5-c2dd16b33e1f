@extends('admin.layouts.app')

@section('title', 'Warehouse Inventory - ' . $warehouse->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.inventory.index') }}">Inventory</a>
                    </li>
                    <li class="breadcrumb-item active">{{ $warehouse->name }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-warehouse text-primary"></i>
                {{ $warehouse->name }}
                <span class="badge badge-{{ $warehouse->is_active ? 'success' : 'secondary' }}">
                    {{ $warehouse->is_active ? 'Active' : 'Inactive' }}
                </span>
                @if($warehouse->is_default)
                    <span class="badge badge-primary">Default</span>
                @endif
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Overview
            </a>
            <a href="{{ route('admin.inventory.export', ['warehouse_id' => $warehouse->id]) }}" class="btn btn-success btn-sm">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
    </div>

    <!-- Warehouse Info -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Warehouse Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Code:</strong> {{ $warehouse->code }}</p>
                            <p><strong>Address:</strong> {{ $warehouse->address }}</p>
                            <p><strong>City:</strong> {{ $warehouse->city }}, {{ $warehouse->state }}</p>
                            <p><strong>Country:</strong> {{ $warehouse->country }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Manager:</strong> {{ $warehouse->manager_name }}</p>
                            <p><strong>Phone:</strong> {{ $warehouse->phone }}</p>
                            <p><strong>Email:</strong> {{ $warehouse->email }}</p>
                            <p><strong>Capacity:</strong> {{ number_format($warehouse->capacity) }} units</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">{{ $warehouse->getUniqueProductsCount() }}</h4>
                            <small class="text-muted">Unique Products</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-success">{{ number_format($warehouse->getTotalProductsCount()) }}</h4>
                            <small class="text-muted">Total Units</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-info">${{ number_format($warehouse->getTotalInventoryValue(), 2) }}</h4>
                            <small class="text-muted">Inventory Value</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.inventory.warehouse', $warehouse->id) }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="search">Search Products</label>
                                    <input type="text" name="search" id="search" class="form-control" 
                                           placeholder="Product name or SKU..." value="{{ request('search') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status">Stock Status</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="in_stock" {{ request('status') == 'in_stock' ? 'selected' : '' }}>In Stock</option>
                                        <option value="low_stock" {{ request('status') == 'low_stock' ? 'selected' : '' }}>Low Stock</option>
                                        <option value="out_of_stock" {{ request('status') == 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Filter
                                        </button>
                                        <a href="{{ route('admin.inventory.warehouse', $warehouse->id) }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes"></i>
                        Inventory Items ({{ $inventory->total() }})
                    </h6>
                </div>
                <div class="card-body">
                    @if($inventory->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Quantity</th>
                                        <th>Reserved</th>
                                        <th>Available</th>
                                        <th>Reorder Level</th>
                                        <th>Status</th>
                                        <th>Last Restocked</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($inventory as $item)
                                        <tr class="{{ $item->isOutOfStock() ? 'table-danger' : ($item->isLowStock() ? 'table-warning' : '') }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($item->product->image)
                                                        <img src="{{ asset('storage/' . $item->product->image) }}" 
                                                             alt="{{ $item->product->name }}" 
                                                             class="rounded mr-2" 
                                                             style="width: 40px; height: 40px; object-fit: cover;">
                                                    @endif
                                                    <div>
                                                        <strong>{{ $item->product->name }}</strong><br>
                                                        <small class="text-muted">{{ $item->product->sku }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">
                                                    {{ $item->product->category->name ?? 'N/A' }}
                                                </span>
                                            </td>
                                            <td>
                                                <code>{{ $item->location_code ?? 'N/A' }}</code>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $item->quantity > 0 ? 'primary' : 'danger' }} badge-lg">
                                                    {{ $item->quantity }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($item->reserved_quantity > 0)
                                                    <span class="badge badge-warning">{{ $item->reserved_quantity }}</span>
                                                @else
                                                    <span class="text-muted">0</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $item->available_quantity > 0 ? 'success' : 'danger' }}">
                                                    {{ $item->available_quantity }}
                                                </span>
                                            </td>
                                            <td>{{ $item->reorder_level }}</td>
                                            <td>
                                                <span class="badge badge-{{ $item->getStockStatusColor() }}">
                                                    {{ ucfirst(str_replace('_', ' ', $item->getStockStatus())) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($item->last_restocked_at)
                                                    <small>{{ $item->last_restocked_at->format('M j, Y') }}</small>
                                                @else
                                                    <small class="text-muted">Never</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.inventory.product', $item->product_id) }}" 
                                                       class="btn btn-info btn-sm" title="View Product Inventory">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.inventory.add-stock-form', $item->product_id) }}" 
                                                       class="btn btn-success btn-sm" title="Add Stock">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-warning btn-sm" 
                                                            title="Adjust Stock" 
                                                            onclick="adjustStock({{ $item->product_id }}, {{ $warehouse->id }}, {{ $item->quantity }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $inventory->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No inventory items found</h5>
                            <p class="text-muted">
                                @if(request()->filled('search') || request()->filled('status'))
                                    Try adjusting your filters or 
                                    <a href="{{ route('admin.inventory.warehouse', $warehouse->id) }}">clear all filters</a>.
                                @else
                                    This warehouse doesn't have any inventory items yet.
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Adjust Stock Modal -->
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adjust Stock Quantity</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="adjustStockForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newQuantity">New Quantity</label>
                        <input type="number" class="form-control" id="newQuantity" min="0" required>
                        <small class="form-text text-muted">Current quantity: <span id="currentQuantity"></span></small>
                    </div>
                    <div class="form-group">
                        <label for="adjustNotes">Reason for Adjustment *</label>
                        <textarea class="form-control" id="adjustNotes" rows="3" required 
                                  placeholder="Explain why you're adjusting the stock quantity..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Adjust Stock</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}

.table th {
    position: sticky;
    top: 0;
    background: #f8f9fc;
    z-index: 10;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>

<script>
let adjustProductId, adjustWarehouseId;

function adjustStock(productId, warehouseId, currentQty) {
    adjustProductId = productId;
    adjustWarehouseId = warehouseId;
    
    document.getElementById('currentQuantity').textContent = currentQty;
    document.getElementById('newQuantity').value = currentQty;
    document.getElementById('adjustNotes').value = '';
    
    $('#adjustStockModal').modal('show');
}

document.getElementById('adjustStockForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const newQuantity = document.getElementById('newQuantity').value;
    const notes = document.getElementById('adjustNotes').value;
    
    fetch(`{{ route('admin.inventory.adjust', ['product' => ':productId', 'warehouse' => ':warehouseId']) }}`
          .replace(':productId', adjustProductId)
          .replace(':warehouseId', adjustWarehouseId), {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            new_quantity: parseInt(newQuantity),
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#adjustStockModal').modal('hide');
            location.reload(); // Refresh the page to show updated data
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adjusting stock.');
    });
});
</script>
@endsection
