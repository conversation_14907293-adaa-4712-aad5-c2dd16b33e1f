<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'BelteiEcom') }} - @yield('title', 'E-commerce Store')</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #ff6b6b;
            --primary-dark: #ee5a52;
            --secondary-color: #4ecdc4;
            --secondary-dark: #45b7aa;
            --accent-color: #ffe66d;
            --accent-dark: #ffd93d;
            --success-color: #51cf66;
            --warning-color: #ffd43b;
            --danger-color: #ff6b6b;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --white: #ffffff;
            --text-primary: #2d3436;
            --text-secondary: #636e72;
            --border-color: #e9ecef;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
            --gradient-primary: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            --gradient-secondary: linear-gradient(135deg, #4ecdc4 0%, #ffe66d 100%);
            --gradient-accent: linear-gradient(135deg, #ffe66d 0%, #ff6b6b 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        a {
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        a:hover {
            color: var(--primary-dark);
        }

        ul {
            list-style: none;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header - Match login page exactly */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            padding: 0.75rem 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo a {
            color: inherit;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: inherit;
            font-weight: inherit;
            background: inherit;
            -webkit-background-clip: inherit;
            -webkit-text-fill-color: inherit;
            background-clip: inherit;
            text-shadow: inherit;
        }

        .logo i {
            font-size: 1.2rem;
            color: #ffd700;
            -webkit-text-fill-color: #ffd700;
            background: none;
        }

        .nav-wrapper {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 1.5rem;
        }

        nav ul li a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav ul li a:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        /* Wishlist Navigation Styling */
        .wishlist-nav-link {
            position: relative;
        }

        .wishlist-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .user-actions {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .cart-link {
            position: relative;
            background: var(--gradient-secondary);
            color: var(--white) !important;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .cart-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(78, 205, 196, 0.5);
            color: var(--white) !important;
        }

        .auth-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .auth-buttons .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            font-size: 0.9rem;
        }

        .auth-buttons .btn-login {
            color: #667eea;
            border-color: #667eea;
            background: transparent;
        }

        .auth-buttons .btn-login:hover {
            background: #667eea;
            color: white;
            text-decoration: none;
        }

        .auth-buttons .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }

        .auth-buttons .btn-register:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Main content */
        main {
            min-height: calc(100vh - 200px);
            position: relative;
        }

        /* Add padding for non-home pages */
        main.with-padding {
            padding: 3rem 0;
        }

        /* Footer */
        footer {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        footer:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--gradient-primary);
            color: var(--white);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover:before {
            left: 100%;
        }

        .btn:hover {
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-secondary {
            background: var(--gradient-secondary);
        }

        .btn-accent {
            background: var(--gradient-accent);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #27ae60 100%);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: var(--white);
        }

        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
            transform: translateY(-1px);
        }

        /* Alerts */
        .alert {
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            border-radius: 15px;
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 1001;
            box-shadow: var(--shadow-medium);
            max-width: 400px;
            backdrop-filter: blur(10px);
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(81, 207, 102, 0.9) 0%, rgba(39, 174, 96, 0.9) 100%);
            color: var(--white);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.9) 0%, rgba(231, 76, 60, 0.9) 100%);
            color: var(--white);
            border-left: 4px solid var(--danger-color);
        }

        /* Cards */
        .card {
            background: var(--white);
            border: none;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .card-header {
            padding: 1.5rem;
            background: var(--gradient-secondary);
            color: var(--white);
            border-bottom: none;
            font-weight: 600;
        }

        .card-body {
            padding: 2rem;
        }

        /* Product grid */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .product-card {
            background: var(--white);
            border: none;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s ease;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .product-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .product-card:hover:before {
            transform: scaleX(1);
        }

        .product-image {
            height: 220px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            line-height: 1.4;
        }

        .product-title a {
            color: inherit;
            transition: color 0.3s ease;
        }

        .product-title a:hover {
            color: var(--primary-color);
        }

        .product-price {
            font-weight: 700;
            font-size: 1.3rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .product-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            justify-content: space-between;
        }

        .product-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        /* Cart badge */
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--gradient-accent);
            color: var(--white);
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.7rem;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Profile Dropdown Styles */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-trigger {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .profile-trigger:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: var(--text-primary);
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .profile-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-name {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .profile-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
            min-width: 220px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            margin-top: 0.5rem;
        }

        .profile-dropdown.active .profile-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px 12px 0 0;
        }

        .dropdown-user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .dropdown-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            overflow: hidden;
        }

        .dropdown-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .dropdown-user-details h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .dropdown-user-details p {
            margin: 0;
            color: #666;
            font-size: 0.8rem;
        }

        .dropdown-menu-items {
            padding: 0.5rem 0;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: #333;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #667eea;
            text-decoration: none;
        }

        .dropdown-item i {
            width: 16px;
            color: #666;
            transition: color 0.2s ease;
        }

        .dropdown-item:hover i {
            color: #667eea;
        }

        .dropdown-divider {
            height: 1px;
            background: rgba(0, 0, 0, 0.1);
            margin: 0.5rem 0;
        }

        .dropdown-item.logout {
            color: #dc3545;
        }

        .dropdown-item.logout:hover {
            background: #fff5f5;
            color: #dc3545;
        }

        .dropdown-item.logout i {
            color: #dc3545;
        }

        /* Hero section */
        .hero {
            text-align: center;
            padding: 4rem 0;
            background: var(--white);
            border-radius: 25px;
            margin-bottom: 3rem;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .hero:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-secondary);
            opacity: 0.05;
            z-index: 1;
        }

        .hero > * {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.3rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        /* Section headings */
        .section-heading {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-heading h2 {
            font-size: 2.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .section-heading p {
            font-size: 1.1rem;
            color: var(--text-secondary);
        }

        /* Category cards */
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .category-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .category-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-secondary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .category-card:hover:before {
            opacity: 0.1;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-medium);
        }

        .category-card > * {
            position: relative;
            z-index: 2;
        }

        .category-card i {
            font-size: 3rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            display: block;
        }

        .category-card h3 {
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            header {
                padding: 1rem 0;
            }

            .header-container {
                flex-direction: column;
                gap: 1.5rem;
            }

            .logo {
                font-size: 1.8rem;
            }

            .nav-wrapper {
                flex-direction: column;
                gap: 1rem;
                width: 100%;
            }

            nav ul {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.3rem;
                padding: 0.3rem;
                width: 100%;
            }

            nav ul li a {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .user-actions {
                justify-content: center;
                width: 100%;
            }

            .auth-buttons {
                flex-direction: column;
                width: 100%;
                max-width: 300px;
            }

            .auth-buttons a {
                text-align: center;
                width: 100%;
            }

            .cart-link {
                padding: 0.6rem 1.2rem;
            }

            /* Profile dropdown mobile styles */
            .profile-name {
                display: none;
            }

            .profile-dropdown-menu {
                right: -1rem;
                min-width: 200px;
            }

            .profile-trigger {
                padding: 0.5rem;
            }

            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .section-heading h2 {
                font-size: 2rem;
            }

            .container {
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .logo {
                font-size: 1.5rem;
            }

            .nav-wrapper {
                gap: 0.8rem;
            }

            nav ul li a {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }

            .hero {
                padding: 2rem 0;
            }

            .hero h1 {
                font-size: 1.6rem;
            }
        }

        /* Professional Footer Styles */
        .main-footer {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%) !important;
            color: white !important;
            margin-top: 4rem;
            position: relative;
            z-index: 1;
        }

        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Footer Top Section */
        .footer-top {
            padding: 4rem 2rem 3rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 3rem;
        }

        .footer-section {
            display: flex;
            flex-direction: column;
        }

        /* Footer Logo */
        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-logo i {
            font-size: 1.5rem;
            color: #ffd700;
            -webkit-text-fill-color: #ffd700;
            background: none;
        }

        .footer-description {
            color: #b2bec3;
            line-height: 1.6;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        /* Social Links */
        .social-links {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .social-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Footer Titles */
        .footer-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: white;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        /* Footer Links */
        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: #b2bec3;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0;
        }

        .footer-links a:hover {
            color: #ffd700;
            transform: translateX(5px);
            text-decoration: none;
        }

        .footer-links a i {
            width: 16px;
            text-align: center;
        }

        /* Contact Info */
        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            color: #b2bec3;
        }

        .contact-item i {
            color: #ffd700;
            width: 20px;
            text-align: center;
            margin-top: 0.25rem;
            flex-shrink: 0;
        }

        /* Newsletter Section */
        .newsletter-section {
            background: transparent;
            padding: 3rem 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .newsletter-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 3rem;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            padding: 0 2rem;
        }

        .newsletter-text {
            flex: 1;
            max-width: 500px;
            min-width: 300px;
        }

        .newsletter-text h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: white;
        }

        .newsletter-text p {
            color: #b2bec3;
            margin: 0;
            line-height: 1.5;
        }

        .newsletter-form {
            display: flex;
            gap: 1rem;
            flex: 1;
            max-width: 500px;
            min-width: 350px;
        }

        .newsletter-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .newsletter-input::placeholder {
            color: #b2bec3;
        }

        .newsletter-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .newsletter-btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            white-space: nowrap;
        }

        .newsletter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Footer Bottom */
        .footer-bottom {
            background: rgba(0, 0, 0, 0.3);
            padding: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-bottom-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .copyright p {
            margin: 0;
            color: #b2bec3;
            font-size: 0.9rem;
        }

        .payment-methods {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #b2bec3;
            font-size: 0.9rem;
        }

        .payment-icons {
            display: flex;
            gap: 0.75rem;
        }

        .payment-icons i {
            font-size: 1.5rem;
            color: #b2bec3;
            transition: all 0.3s ease;
        }

        .payment-icons i:hover {
            color: #ffd700;
            transform: translateY(-2px);
        }

        /* Footer Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-wrapper {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                gap: 1rem;
                justify-content: center;
            }

            .auth-buttons {
                justify-content: center;
            }
        }

        @media (max-width: 992px) {
            .footer-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 2rem;
            }

            .newsletter-content {
                flex-direction: column;
                text-align: center;
                gap: 2rem;
            }

            .newsletter-text {
                max-width: 100%;
                min-width: auto;
                text-align: center;
                margin-bottom: 1rem;
            }

            .newsletter-form {
                max-width: 100%;
                min-width: auto;
            }

            .footer-bottom-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .footer-top {
                padding: 3rem 1rem 2rem;
            }

            .newsletter-section {
                padding: 2rem 1rem;
            }

            .newsletter-form {
                flex-direction: column;
                gap: 1rem;
            }

            .footer-bottom {
                padding: 1.5rem 1rem;
            }

            .social-links {
                justify-content: center;
            }

            .payment-methods {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .footer-logo {
                justify-content: center;
                text-align: center;
            }

            .footer-description {
                text-align: center;
            }

            .contact-info {
                align-items: center;
            }
        }

        /* Newsletter Subscribed State */
        .newsletter-subscribed {
            text-align: center;
        }

        .newsletter-subscribed .newsletter-text h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .newsletter-subscribed .newsletter-text h3 i {
            color: #43e97b;
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .newsletter-subscribed .newsletter-text p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .newsletter-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .newsletter-btn-outline {
            background: transparent !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            color: white !important;
        }

        .newsletter-btn-outline:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
        }

        .newsletter-btn-secondary {
            background: rgba(239, 68, 68, 0.2) !important;
            border: 2px solid rgba(239, 68, 68, 0.3) !important;
            color: #fca5a5 !important;
        }

        .newsletter-btn-secondary:hover {
            background: rgba(239, 68, 68, 0.3) !important;
            border-color: rgba(239, 68, 68, 0.5) !important;
            color: white !important;
        }

        @media (max-width: 768px) {
            .newsletter-actions {
                flex-direction: column;
                align-items: center;
            }

            .newsletter-btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>

    @yield('styles')
</head>
<body>
    <header>
        <div class="container header-container">
            <div class="logo">
                <a href="{{ route('home') }}">
                    <i class="fas fa-store"></i>
                    {{ config('app.name', 'BelteiEcom') }}
                </a>
            </div>

            <div class="nav-wrapper">
                <nav>
                    <ul>
                        <li><a href="{{ route('home') }}"><i class="fas fa-home"></i> Home</a></li>
                        <li><a href="{{ route('products.index') }}"><i class="fas fa-box"></i> Products</a></li>
                        @auth
                            <li><a href="{{ route('wishlist.index') }}" class="wishlist-nav-link">
                                <i class="fas fa-heart"></i> Wishlist
                                <span class="wishlist-count" style="display: none;">0</span>
                            </a></li>
                            <li><a href="{{ route('orders.history') }}"><i class="fas fa-history"></i> Orders</a></li>
                            @if(Auth::user()->is_admin)
                                <li><a href="{{ route('admin.dashboard') }}"><i class="fas fa-tachometer-alt"></i> Admin</a></li>
                            @endif
                        @endauth
                    </ul>
                </nav>

                <div class="user-actions">
                    @guest
                        <div class="auth-buttons">
                            <a href="{{ route('login') }}" class="btn btn-login">Login</a>
                            <a href="{{ route('register') }}" class="btn btn-register">Register</a>
                        </div>
                    @else
                        <a href="{{ route('cart.index') }}" class="cart-link">
                            <i class="fas fa-shopping-cart"></i> Cart
                            @php
                                $cartCount = Auth::user()->cartItems()->sum('quantity');
                            @endphp
                            @if($cartCount > 0)
                                <span class="cart-badge">
                                    {{ $cartCount }}
                                </span>
                            @endif
                        </a>

                        <!-- Profile Dropdown -->
                        <div class="profile-dropdown">
                            <div class="profile-trigger" onclick="toggleProfileDropdown()">
                                <div class="profile-avatar">
                                    @php
                                        $profilePictureUrl = App\Http\Controllers\UserProfileController::getProfilePictureUrl(Auth::user());
                                    @endphp
                                    @if($profilePictureUrl)
                                        <img src="{{ $profilePictureUrl }}" alt="{{ Auth::user()->name }}">
                                    @else
                                        {{ App\Http\Controllers\UserProfileController::getUserInitials(Auth::user()) }}
                                    @endif
                                </div>
                                <span class="profile-name">{{ Auth::user()->name }}</span>
                                <i class="fas fa-chevron-down" style="font-size: 0.7rem; transition: transform 0.3s ease;"></i>
                            </div>

                            <div class="profile-dropdown-menu">
                                <div class="dropdown-header">
                                    <div class="dropdown-user-info">
                                        <div class="dropdown-avatar">
                                            @if($profilePictureUrl)
                                                <img src="{{ $profilePictureUrl }}" alt="{{ Auth::user()->name }}">
                                            @else
                                                {{ App\Http\Controllers\UserProfileController::getUserInitials(Auth::user()) }}
                                            @endif
                                        </div>
                                        <div class="dropdown-user-details">
                                            <h6>{{ Auth::user()->name }}</h6>
                                            <p>{{ Auth::user()->email }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="dropdown-menu-items">
                                    <a href="{{ route('profile.index') }}" class="dropdown-item">
                                        <i class="fas fa-user"></i>
                                        <span>My Profile</span>
                                    </a>
                                    <a href="{{ route('orders.history') }}" class="dropdown-item">
                                        <i class="fas fa-shopping-bag"></i>
                                        <span>My Orders</span>
                                    </a>
                                    @if(Auth::user()->is_admin)
                                        <div class="dropdown-divider"></div>
                                        <a href="{{ route('admin.dashboard') }}" class="dropdown-item">
                                            <i class="fas fa-tachometer-alt"></i>
                                            <span>Admin Dashboard</span>
                                        </a>
                                    @endif
                                    <div class="dropdown-divider"></div>
                                    <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="dropdown-item logout">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Logout</span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                            @csrf
                        </form>
                    @endguest
                </div>
            </div>
        </div>
    </header>

    <main>
        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif

        @yield('content')
    </main>

    <!-- Professional Footer -->
    <footer class="main-footer">
        <div class="footer-content">
            <!-- Footer Top Section -->
            <div class="footer-top">
                <div class="footer-grid">
                    <!-- Company Info -->
                    <div class="footer-section">
                        <div class="footer-logo">
                            <i class="fas fa-store"></i>
                            <span>{{ config('app.name', 'BelteiEcom') }}</span>
                        </div>
                        <p class="footer-description">
                            Your trusted online marketplace for quality products at unbeatable prices.
                            Discover amazing deals and exceptional customer service.
                        </p>
                        <div class="social-links">
                            <a href="#" class="social-link" aria-label="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="footer-section">
                        <h3 class="footer-title">Quick Links</h3>
                        <ul class="footer-links">
                            <li><a href="{{ route('home') }}"><i class="fas fa-home"></i> Home</a></li>
                            <li><a href="{{ route('products.index') }}"><i class="fas fa-box"></i> All Products</a></li>
                            <li><a href="#"><i class="fas fa-tags"></i> Categories</a></li>
                            <li><a href="#"><i class="fas fa-percent"></i> Special Offers</a></li>
                            <li><a href="#"><i class="fas fa-star"></i> Best Sellers</a></li>
                        </ul>
                    </div>

                    <!-- Customer Service -->
                    <div class="footer-section">
                        <h3 class="footer-title">Customer Service</h3>
                        <ul class="footer-links">
                            <li><a href="#"><i class="fas fa-question-circle"></i> Help Center</a></li>
                            <li><a href="#"><i class="fas fa-shipping-fast"></i> Shipping Info</a></li>
                            <li><a href="#"><i class="fas fa-undo"></i> Returns & Exchanges</a></li>
                            <li><a href="#"><i class="fas fa-shield-alt"></i> Privacy Policy</a></li>
                            <li><a href="#"><i class="fas fa-file-contract"></i> Terms of Service</a></li>
                        </ul>
                    </div>

                    <!-- Contact Info -->
                    <div class="footer-section">
                        <h3 class="footer-title">Contact Us</h3>
                        <div class="contact-info">
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>123 Business Street<br>Phnom Penh, Cambodia</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+855 12 345 678</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-clock"></i>
                                <span>Mon - Fri: 9:00 AM - 6:00 PM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="newsletter-section">
                <div class="newsletter-content">
                    @php
                        $isSubscribed = false;
                        if (auth()->check()) {
                            $isSubscribed = \App\Models\Newsletter::where('email', auth()->user()->email)
                                                                 ->where('is_active', true)
                                                                 ->where('email_confirmed', true)
                                                                 ->exists();
                        }
                    @endphp

                    @if($isSubscribed)
                        <!-- Already Subscribed State -->
                        <div class="newsletter-subscribed">
                            <div class="newsletter-text">
                                <h3>
                                    <i class="fas fa-check-circle text-success"></i>
                                    Already Subscribed!
                                </h3>
                                <p>Your account <strong>{{ auth()->user()->email }}</strong> is already subscribed to our newsletter. You can disconnect or manage your subscription below.</p>
                            </div>
                            <div class="newsletter-actions">
                                @if(auth()->check())
                                    @php
                                        $newsletter = \App\Models\Newsletter::where('email', auth()->user()->email)->first();
                                    @endphp
                                    @if($newsletter)
                                        <a href="{{ route('newsletter.unsubscribe', $newsletter->subscription_token) }}" class="newsletter-btn newsletter-btn-secondary">
                                            <i class="fas fa-unlink"></i> Disconnect
                                        </a>
                                    @endif
                                @endif
                                <button onclick="toggleNewsletterInfo()" class="newsletter-btn newsletter-btn-outline">
                                    <i class="fas fa-cog"></i> Manage
                                </button>
                            </div>
                        </div>
                    @else
                        <!-- Subscribe Form -->
                        <div class="newsletter-text">
                            <h3>Stay Updated</h3>
                            <p>Subscribe to our newsletter for exclusive deals and latest updates</p>
                        </div>
                        <form class="newsletter-form" id="newsletterForm">
                            @csrf
                            <input type="email" name="email" placeholder="Enter your email address" class="newsletter-input" required>
                            <button type="submit" class="newsletter-btn">
                                <i class="fas fa-paper-plane"></i> Subscribe
                            </button>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="copyright">
                        <p>&copy; {{ date('Y') }} {{ config('app.name', 'BelteiEcom') }}. All rights reserved.</p>
                    </div>
                    <div class="payment-methods">
                        <span>We Accept:</span>
                        <div class="payment-icons">
                            <i class="fab fa-cc-visa"></i>
                            <i class="fab fa-cc-mastercard"></i>
                            <i class="fab fa-cc-paypal"></i>
                            <i class="fab fa-cc-stripe"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    @yield('scripts')

    <script>
        // Auto-hide alerts after 3 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.opacity = '0';
                    alert.style.transition = 'opacity 0.5s';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                }, 3000);
            });

            // Add padding to main for non-home pages
            const currentPath = window.location.pathname;
            const main = document.querySelector('main');
            if (currentPath !== '/' && main) {
                main.classList.add('with-padding');
            }

            // Newsletter Subscription Handler
            const newsletterForm = document.getElementById('newsletterForm');
            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const form = this;
                    const email = form.querySelector('input[name="email"]').value;
                    const button = form.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;

                    // Show loading state
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
                    button.disabled = true;

                    // Send subscription request
                    fetch('{{ route("newsletter.subscribe") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': form.querySelector('input[name="_token"]').value
                        },
                        body: JSON.stringify({
                            email: email
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            showNotification(data.message, 'success');
                            form.reset();

                            // Refresh page if needed to update footer
                            if (data.refresh_page) {
                                setTimeout(() => {
                                    window.location.reload();
                                }, 2000); // Wait 2 seconds to show the success message
                            }
                        } else {
                            // Show error message
                            showNotification(data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Something went wrong. Please try again.', 'error');
                    })
                    .finally(() => {
                        // Reset button
                        button.innerHTML = originalText;
                        button.disabled = false;
                    });
                });
            }
        });

        // Notification function
        function showNotification(message, type) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.newsletter-notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification
            const notification = document.createElement('div');
            notification.className = `newsletter-notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                max-width: 400px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                animation: slideInRight 0.5s ease-out;
                ${type === 'success' ?
                    'background: linear-gradient(135deg, #00b894 0%, #00a085 100%);' :
                    'background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);'
                }
            `;

            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            `;

            // Add to page
            document.body.appendChild(notification);

            // Remove after 5 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.5s ease-out';
                setTimeout(() => notification.remove(), 500);
            }, 5000);
        }

        // Newsletter info toggle function
        function toggleNewsletterInfo() {
            @if(auth()->check() && \App\Models\Newsletter::where('email', auth()->user()->email)->where('is_active', true)->where('email_confirmed', true)->exists())
                const infoText = `
                    ✅ Your Newsletter Subscription:

                    📧 Email: {{ auth()->user()->email }}
                    📅 Status: Active & Confirmed

                    🎁 Your Benefits:
                    • Exclusive deals and discounts up to 50% off
                    • Early access to new products and collections
                    • Weekly shopping tips and trends
                    • Special member-only promotions
                    • Free shipping offers and flash sales

                    📝 Manage Your Subscription:
                    • You can disconnect anytime using the button below
                    • Or click unsubscribe in any newsletter email
                    • Your data is secure and never shared
                `;
            @else
                const infoText = `
                    📧 Newsletter Benefits:
                    • Exclusive deals and discounts up to 50% off
                    • Early access to new products and collections
                    • Weekly shopping tips and trends
                    • Special member-only promotions
                    • Free shipping offers and flash sales

                    🔒 Privacy & Security:
                    • Your email is secure and never shared
                    • You can unsubscribe anytime
                    • Email confirmation required for security
                `;
            @endif

            alert(infoText);
        }
    </script>

    <!-- Newsletter Animation Styles -->
    <style>
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>

    <!-- Profile Dropdown JavaScript -->
    <script>
        function toggleProfileDropdown() {
            const dropdown = document.querySelector('.profile-dropdown');
            const chevron = dropdown.querySelector('.fa-chevron-down');

            dropdown.classList.toggle('active');

            // Rotate chevron
            if (dropdown.classList.contains('active')) {
                chevron.style.transform = 'rotate(180deg)';
            } else {
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.querySelector('.profile-dropdown');
            const chevron = dropdown?.querySelector('.fa-chevron-down');

            if (dropdown && !dropdown.contains(event.target)) {
                dropdown.classList.remove('active');
                if (chevron) {
                    chevron.style.transform = 'rotate(0deg)';
                }
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const dropdown = document.querySelector('.profile-dropdown');
                const chevron = dropdown?.querySelector('.fa-chevron-down');

                if (dropdown) {
                    dropdown.classList.remove('active');
                    if (chevron) {
                        chevron.style.transform = 'rotate(0deg)';
                    }
                }
            }
        });

        // Load wishlist count on page load
        @auth
        document.addEventListener('DOMContentLoaded', function() {
            loadWishlistCount();
        });

        function loadWishlistCount() {
            fetch('{{ route("wishlist.count") }}')
                .then(response => response.json())
                .then(data => {
                    updateWishlistCount(data.count);
                })
                .catch(error => {
                    console.error('Error loading wishlist count:', error);
                });
        }

        function updateWishlistCount(count) {
            const wishlistCounters = document.querySelectorAll('.wishlist-count');
            wishlistCounters.forEach(counter => {
                counter.textContent = count;
                if (count > 0) {
                    counter.style.display = 'flex';
                } else {
                    counter.style.display = 'none';
                }
            });
        }
        @endauth
    </script>

    <!-- Live Chat Widget -->
    @include('chat.widget')
</body>
</html>
