<?php

namespace App\Console\Commands;

use App\Models\Category;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateCategorySlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categories:generate-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate unique slugs for all categories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating slugs for categories...');

        $categories = Category::whereNull('slug')->orWhere('slug', '')->get();

        if ($categories->isEmpty()) {
            $this->info('All categories already have slugs!');
            return;
        }

        $bar = $this->output->createProgressBar($categories->count());
        $bar->start();

        foreach ($categories as $category) {
            do {
                $slug = Str::random(12);
            } while (Category::where('slug', $slug)->exists());

            $category->slug = $slug;
            $category->save();

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Generated slugs for {$categories->count()} categories!");
    }
}
