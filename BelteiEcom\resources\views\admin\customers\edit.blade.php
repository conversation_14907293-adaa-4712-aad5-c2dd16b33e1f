@extends('admin.layouts.app')

@section('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .page-header .d-flex {
        position: relative;
        z-index: 10;
    }

    .page-header .btn {
        position: relative;
        z-index: 11;
        pointer-events: auto;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 2px solid rgba(255,255,255,0.3);
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 8px;
    }

    .page-header .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        border-color: rgba(255,255,255,0.5);
    }

    .page-header .btn-light {
        background: rgba(255,255,255,0.9);
        color: #667eea;
    }

    .page-header .btn-light:hover {
        background: white;
        color: #5a6fd8;
    }

    .page-header .btn-outline-light {
        background: transparent;
        color: white;
        border-color: rgba(255,255,255,0.5);
    }

    .page-header .btn-outline-light:hover {
        background: rgba(255,255,255,0.1);
        color: white;
        border-color: white;
    }
    
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    
    .form-header {
        background: var(--primary-gradient);
        color: white;
        padding: 1.5rem;
        margin: 0;
    }
    
    .form-body {
        padding: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(67, 233, 123, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
    }
    
    .customer-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin: 0 auto 1rem;
    }
    
    .customer-info-card {
        background: #f8f9fc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .required {
        color: #dc3545;
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">✏️ Edit Customer</h1>
                <p class="mb-0 opacity-75">Update customer information and details</p>
            </div>
            <div>
                <a href="{{ route('admin.customers.show', $customer->id) }}" class="btn btn-light">
                    <i class="fas fa-eye"></i> View Customer
                </a>
                <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-light ml-2">
                    <i class="fas fa-arrow-left"></i> Back to Customers
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Info Card -->
        <div class="col-md-4">
            <div class="customer-info-card slide-up" style="animation-delay: 0.2s;">
                <div class="customer-avatar">
                    {{ substr($customer->name, 0, 1) }}
                </div>
                <h5>{{ $customer->name }}</h5>
                <p class="text-muted">{{ $customer->email }}</p>
                <div class="mt-3">
                    <div class="small text-muted">Member since</div>
                    <div class="font-weight-bold">{{ $customer->created_at->format('F j, Y') }}</div>
                </div>
                <div class="mt-2">
                    <div class="small text-muted">Total Orders</div>
                    <div class="font-weight-bold">{{ $customer->orders()->count() }}</div>
                </div>
                <div class="mt-2">
                    <div class="small text-muted">Total Spent</div>
                    <div class="font-weight-bold">${{ number_format($customer->orders()->sum('total_amount'), 2) }}</div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="col-md-8">
            <div class="form-card slide-up" style="animation-delay: 0.4s;">
                <div class="form-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-edit"></i> Customer Information
                    </h5>
                </div>
                <div class="form-body">
                    <form action="{{ route('admin.customers.update', $customer->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">
                                        Full Name <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $customer->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">
                                        Email Address <span class="required">*</span>
                                    </label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $customer->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $customer->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address" class="form-label">Address</label>
                                    <input type="text" class="form-control @error('address') is-invalid @enderror" 
                                           id="address" name="address" value="{{ old('address', $customer->address) }}">
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <h6 class="mb-3">
                            <i class="fas fa-lock"></i> Change Password (Optional)
                        </h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password">
                                    <small class="form-text text-muted">Leave blank to keep current password</small>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" 
                                           id="password_confirmation" name="password_confirmation">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save"></i> Update Customer
                            </button>
                            <a href="{{ route('admin.customers.show', $customer->id) }}" class="btn btn-cancel ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
