@extends('layouts.app')

@section('title', 'Confirm QR Login')

@section('styles')
<style>
    .qr-confirm-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .qr-confirm-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 500px;
        width: 100%;
        text-align: center;
    }

    .qr-confirm-header {
        margin-bottom: 2rem;
    }

    .qr-confirm-icon {
        font-size: 4rem;
        color: #4ecdc4;
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .qr-confirm-title {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .qr-confirm-subtitle {
        color: #666;
        font-size: 1.1rem;
    }

    .device-info {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: left;
    }

    .device-info h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }

    .device-detail {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .device-detail:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .device-label {
        font-weight: 600;
        color: #555;
    }

    .device-value {
        color: #777;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-confirm {
        flex: 1;
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-confirm:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
    }

    .btn-deny {
        flex: 1;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-deny:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    }

    .loading {
        display: none;
        margin-top: 1rem;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #4ecdc4;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .alert {
        padding: 1rem;
        border-radius: 10px;
        margin-top: 1rem;
        display: none;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>
@endsection

@section('content')
<div class="qr-confirm-container">
    <div class="qr-confirm-card">
        <div class="qr-confirm-header">
            <div class="qr-confirm-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h1 class="qr-confirm-title">Confirm Login</h1>
            <p class="qr-confirm-subtitle">
                Do you want to allow this device to log in to your account?
            </p>
        </div>

        <div class="device-info">
            <h4><i class="fas fa-desktop"></i> Device Information</h4>
            <div class="device-detail">
                <span class="device-label">IP Address:</span>
                <span class="device-value">{{ $session->ip_address }}</span>
            </div>
            <div class="device-detail">
                <span class="device-label">Browser:</span>
                <span class="device-value">{{ json_decode($session->device_info)->browser ?? 'Unknown' }}</span>
            </div>
            <div class="device-detail">
                <span class="device-label">Platform:</span>
                <span class="device-value">{{ json_decode($session->device_info)->platform ?? 'Unknown' }}</span>
            </div>
            <div class="device-detail">
                <span class="device-label">Requested:</span>
                <span class="device-value">{{ $session->created_at->diffForHumans() }}</span>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn-deny" onclick="denyLogin()">
                <i class="fas fa-times"></i> Deny
            </button>
            <button type="button" class="btn-confirm" onclick="confirmLogin()">
                <i class="fas fa-check"></i> Allow Login
            </button>
        </div>

        <div class="loading">
            <div class="spinner"></div>
            <p>Processing...</p>
        </div>

        <div class="alert alert-success" id="success-alert"></div>
        <div class="alert alert-error" id="error-alert"></div>
    </div>
</div>

<script>
const sessionToken = '{{ $session->token }}';

function showLoading() {
    document.querySelector('.action-buttons').style.display = 'none';
    document.querySelector('.loading').style.display = 'block';
}

function hideLoading() {
    document.querySelector('.action-buttons').style.display = 'flex';
    document.querySelector('.loading').style.display = 'none';
}

function showAlert(type, message) {
    const alert = document.getElementById(type + '-alert');
    alert.textContent = message;
    alert.style.display = 'block';
    
    // Hide other alerts
    const otherType = type === 'success' ? 'error' : 'success';
    document.getElementById(otherType + '-alert').style.display = 'none';
}

async function confirmLogin() {
    showLoading();
    
    try {
        const response = await fetch('{{ route("qr-login.confirm") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                token: sessionToken
            })
        });

        const data = await response.json();
        
        if (data.success) {
            showAlert('success', 'Login confirmed successfully! You can close this page.');
            setTimeout(() => {
                window.close();
            }, 2000);
        } else {
            showAlert('error', data.message || 'Failed to confirm login');
            hideLoading();
        }
    } catch (error) {
        showAlert('error', 'Network error occurred');
        hideLoading();
    }
}

async function denyLogin() {
    showLoading();
    
    try {
        const response = await fetch('{{ route("qr-login.cancel") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                token: sessionToken
            })
        });

        const data = await response.json();
        
        if (data.success) {
            showAlert('success', 'Login request denied. You can close this page.');
            setTimeout(() => {
                window.close();
            }, 2000);
        } else {
            showAlert('error', data.message || 'Failed to deny login');
            hideLoading();
        }
    } catch (error) {
        showAlert('error', 'Network error occurred');
        hideLoading();
    }
}
</script>
@endsection
