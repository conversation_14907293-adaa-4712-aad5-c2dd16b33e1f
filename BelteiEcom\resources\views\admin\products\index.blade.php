@extends('admin.layouts.app')

@section('styles')
<style>
    .product-image {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        object-fit: cover;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .product-image:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .product-image-error {
        border: 2px solid #dc3545 !important;
        background: #f8d7da !important;
    }

    .stock-badge {
        border-radius: 20px;
        padding: 0.4rem 0.8rem;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .stock-low {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: #721c24;
    }

    .stock-medium {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: #856404;
    }

    .stock-high {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: #155724;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .btn-edit {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-delete {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        color: white;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .page-header .d-flex {
        position: relative;
        z-index: 2;
    }

    .btn {
        position: relative;
        z-index: 10;
        pointer-events: auto;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">📦 Products Management</h1>
                <p class="mb-0 opacity-75">Manage your product catalog with ease</p>
            </div>
            <div>
                <a href="{{ route('admin.products.create') }}" class="btn btn-light btn-lg" style="text-decoration: none; position: relative; z-index: 10; display: inline-block;" onclick="console.log('Button clicked!'); return true;">
                    <i class="fas fa-plus"></i> Add New Product
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-cards slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-number">{{ $products->total() }}</div>
            <div class="stat-label">Total Products</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Category::count() }}</div>
            <div class="stat-label">Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${{ number_format(\App\Models\Product::sum('price'), 2) }}</div>
            <div class="stat-label">Total Value</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Product::where('stock', '<=', 10)->count() }}</div>
            <div class="stat-label">Low Stock Items</div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card slide-up" style="animation-delay: 0.4s;">
        <div class="card-header">
            <h6>🛍️ All Products</h6>
        </div>
        <div class="card-body">
            @if($products->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Image</th>
                                <th>Product Details</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($products as $product)
                                <tr>
                                    <td>
                                        <span class="badge badge-primary">#{{ $product->id }}</span>
                                    </td>
                                    <td style="padding: 15px; text-align: center; vertical-align: middle;">
                                        @if($product->image)
                                            <div style="position: relative; display: inline-block;">
                                                <img src="{{ asset('storage/' . $product->image) }}?bust={{ rand(1000,9999) }}"
                                                     alt="{{ $product->name }}"
                                                     style="
                                                        width: 100px;
                                                        height: 100px;
                                                        object-fit: cover;
                                                        border-radius: 12px;
                                                        border: 3px solid #007bff;
                                                        background: white;
                                                        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
                                                        position: relative;
                                                        z-index: 10;
                                                     ">
                                                <div style="
                                                    position: absolute;
                                                    bottom: -8px;
                                                    left: 50%;
                                                    transform: translateX(-50%);
                                                    background: #007bff;
                                                    color: white;
                                                    padding: 2px 8px;
                                                    border-radius: 10px;
                                                    font-size: 10px;
                                                    white-space: nowrap;
                                                    z-index: 11;
                                                ">✅ Image</div>
                                            </div>
                                        @else
                                            <div style="
                                                width: 100px;
                                                height: 100px;
                                                background: #f8f9fa;
                                                border: 3px dashed #dc3545;
                                                border-radius: 12px;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                margin: 0 auto;
                                            ">
                                                <i class="fas fa-image" style="color: #dc3545; font-size: 30px;"></i>
                                            </div>
                                            <div style="margin-top: 5px; font-size: 10px; color: #dc3545;">No Image</div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold text-primary">{{ $product->name }}</div>
                                            <div class="text-muted small">{{ Str::limit($product->description, 50) }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ $product->category->name }}</span>
                                    </td>
                                    <td>
                                        <div class="font-weight-bold text-success">${{ number_format($product->price, 2) }}</div>
                                    </td>
                                    <td>
                                        @if($product->stock <= 10)
                                            <span class="stock-badge stock-low">
                                                <i class="fas fa-exclamation-triangle"></i> {{ $product->stock }} Low
                                            </span>
                                        @elseif($product->stock <= 50)
                                            <span class="stock-badge stock-medium">
                                                <i class="fas fa-minus-circle"></i> {{ $product->stock }} Medium
                                            </span>
                                        @else
                                            <span class="stock-badge stock-high">
                                                <i class="fas fa-check-circle"></i> {{ $product->stock }} High
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-edit btn-sm" title="Edit Product">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.products.destroy', $product->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-delete btn-sm" onclick="return confirm('Are you sure you want to delete this product?')" title="Delete Product">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 d-flex justify-content-center">
                    {{ $products->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">No products found</h5>
                    <p class="text-gray-400">Start by adding your first product to the catalog.</p>
                    <a href="{{ route('admin.products.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Product
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
