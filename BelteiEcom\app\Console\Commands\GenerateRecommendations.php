<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Services\RecommendationService;
use Illuminate\Console\Command;

class GenerateRecommendations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recommendations:generate {--product_id= : Generate for specific product} {--all : Generate for all products}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate AI recommendations for products';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $recommendationService = app(\App\Services\RecommendationService::class);

        if ($this->option('product_id')) {
            $productId = $this->option('product_id');
            $this->info("Generating recommendations for product ID: {$productId}");

            $result = $recommendationService->generateRecommendations($productId);

            if ($result) {
                $this->info("✅ Recommendations generated successfully for product {$productId}");
            } else {
                $this->error("❌ Failed to generate recommendations for product {$productId}");
            }

        } elseif ($this->option('all')) {
            $this->info("Generating recommendations for all products...");

            $products = \App\Models\Product::where('stock', '>', 0)->get();
            $bar = $this->output->createProgressBar($products->count());
            $bar->start();

            $successful = 0;
            $failed = 0;

            foreach ($products as $product) {
                $result = $recommendationService->generateRecommendations($product->id);

                if ($result) {
                    $successful++;
                } else {
                    $failed++;
                }

                $bar->advance();
                usleep(100000); // Small delay to prevent overwhelming the system
            }

            $bar->finish();
            $this->newLine();
            $this->info("✅ Completed! Generated recommendations for {$successful} products");

            if ($failed > 0) {
                $this->warn("⚠️  Failed to generate recommendations for {$failed} products");
            }

        } else {
            $this->error("Please specify either --product_id=ID or --all");
            return 1;
        }

        return 0;
    }
}
