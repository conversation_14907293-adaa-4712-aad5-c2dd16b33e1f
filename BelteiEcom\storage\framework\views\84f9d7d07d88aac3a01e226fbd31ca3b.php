<?php $__env->startSection('title', 'QR Code Scanner'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    body {
        background: #000;
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    .qr-scanner-page {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: #000;
        display: flex;
        flex-direction: column;
        z-index: 1000;
    }

    .qr-scanner-header {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 1rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 1001;
    }

    .qr-scanner-title {
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .qr-scanner-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .qr-control-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .qr-control-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    .qr-control-btn.primary {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        border-color: transparent;
    }

    .qr-control-btn.danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border-color: transparent;
    }

    .qr-scanner-main {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .qr-video-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #qr-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .qr-scanner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
    }

    .qr-scanner-frame {
        width: 280px;
        height: 280px;
        border: 3px solid #4ecdc4;
        border-radius: 20px;
        position: relative;
        animation: pulse 2s infinite;
        box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.2);
    }

    .qr-scanner-frame::before,
    .qr-scanner-frame::after {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        border: 4px solid #4ecdc4;
    }

    .qr-scanner-frame::before {
        top: -4px;
        left: -4px;
        border-right: none;
        border-bottom: none;
        border-top-left-radius: 20px;
    }

    .qr-scanner-frame::after {
        bottom: -4px;
        right: -4px;
        border-left: none;
        border-top: none;
        border-bottom-right-radius: 20px;
    }

    .qr-scanner-corners {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 30px;
        height: 30px;
        border: 4px solid #4ecdc4;
        border-left: none;
        border-bottom: none;
        border-top-right-radius: 20px;
    }

    .qr-scanner-corners:nth-child(2) {
        top: auto;
        right: auto;
        bottom: -4px;
        left: -4px;
        border-right: none;
        border-top: none;
        border-bottom-left-radius: 20px;
        border-top-right-radius: 0;
    }

    @keyframes pulse {
        0%, 100% { 
            opacity: 1; 
            transform: scale(1);
        }
        50% { 
            opacity: 0.8; 
            transform: scale(1.02);
        }
    }

    .qr-scanner-placeholder {
        text-align: center;
        color: white;
        padding: 3rem;
    }

    .qr-scanner-placeholder i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.7;
    }

    .qr-scanner-placeholder h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .qr-scanner-placeholder p {
        font-size: 1rem;
        opacity: 0.8;
        line-height: 1.6;
    }

    .qr-scanner-footer {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 1.5rem 2rem;
        text-align: center;
        position: relative;
        z-index: 1001;
    }

    .qr-status-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 1rem;
        border-radius: 25px;
        font-weight: 600;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .qr-status-indicator.ready {
        background: rgba(78, 205, 196, 0.2);
        color: #4ecdc4;
        border: 1px solid rgba(78, 205, 196, 0.3);
    }

    .qr-status-indicator.scanning {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .qr-status-indicator.success {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .qr-status-indicator.error {
        background: rgba(220, 53, 69, 0.2);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .qr-instructions {
        font-size: 0.9rem;
        opacity: 0.8;
        line-height: 1.5;
    }

    .qr-instructions strong {
        color: #4ecdc4;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .qr-scanner-header {
            padding: 1rem;
        }
        
        .qr-scanner-title {
            font-size: 1rem;
        }
        
        .qr-control-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .qr-scanner-frame {
            width: 220px;
            height: 220px;
        }
        
        .qr-scanner-footer {
            padding: 1rem;
        }
        
        .qr-scanner-placeholder {
            padding: 2rem 1rem;
        }
        
        .qr-scanner-placeholder i {
            font-size: 3rem;
        }
        
        .qr-scanner-placeholder h3 {
            font-size: 1.2rem;
        }
    }

    /* Hide main layout */
    .navbar, .footer, main {
        display: none !important;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="qr-scanner-page">
    <!-- Header -->
    <div class="qr-scanner-header">
        <div class="qr-scanner-title">
            <i class="fas fa-qrcode"></i>
            QR Code Scanner
        </div>
        <div class="qr-scanner-controls">
            <button type="button" id="start-camera-btn" class="qr-control-btn primary" onclick="startCamera()">
                <i class="fas fa-camera"></i> Start Camera
            </button>
            <button type="button" id="stop-camera-btn" class="qr-control-btn danger" onclick="stopCamera()" style="display: none;">
                <i class="fas fa-stop"></i> Stop Camera
            </button>
            <a href="<?php echo e(route('profile.index')); ?>" class="qr-control-btn">
                <i class="fas fa-times"></i> Close
            </a>
        </div>
    </div>

    <!-- Main Scanner Area -->
    <div class="qr-scanner-main">
        <div class="qr-video-container">
            <video id="qr-video" autoplay muted playsinline style="display: none;"></video>
            
            <div class="qr-scanner-overlay" id="qr-overlay" style="display: none;">
                <div class="qr-scanner-frame">
                    <div class="qr-scanner-corners"></div>
                    <div class="qr-scanner-corners"></div>
                </div>
            </div>
            
            <div class="qr-scanner-placeholder" id="qr-placeholder">
                <i class="fas fa-camera"></i>
                <h3>Ready to Scan</h3>
                <p>Click <strong>"Start Camera"</strong> to begin scanning QR codes.<br>
                Position the QR code within the frame when the camera starts.</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="qr-scanner-footer">
        <div class="qr-status-indicator ready" id="qr-status">
            <i class="fas fa-info-circle"></i>
            <span id="status-text">Ready to start scanning</span>
        </div>
        
        <div class="qr-instructions">
            <strong>How to use:</strong> Point your camera at a QR code from the login page to authenticate the device.
        </div>
    </div>
</div>

<?php $__env->startSection('scripts'); ?>
<script>
let videoStream = null;
let qrScanner = null;
let isScanning = false;

// Start camera for QR scanning
async function startCamera() {
    try {
        updateStatus('scanning', 'Starting camera...');

        // Request camera permission with back camera preference
        const constraints = {
            video: {
                facingMode: { ideal: 'environment' }, // Prefer back camera
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        };

        videoStream = await navigator.mediaDevices.getUserMedia(constraints);

        const video = document.getElementById('qr-video');
        video.srcObject = videoStream;

        // Show video and overlay, hide placeholder
        video.style.display = 'block';
        document.getElementById('qr-overlay').style.display = 'flex';
        document.getElementById('qr-placeholder').style.display = 'none';

        // Update buttons
        document.getElementById('start-camera-btn').style.display = 'none';
        document.getElementById('stop-camera-btn').style.display = 'inline-flex';

        updateStatus('scanning', 'Camera active - Position QR code in the frame');

        // Start QR code detection
        startQrDetection();

    } catch (error) {
        console.error('Camera access error:', error);
        let errorMessage = 'Camera access denied or not available';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'Camera permission denied. Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'No camera found on this device.';
        } else if (error.name === 'NotSupportedError') {
            errorMessage = 'Camera not supported in this browser.';
        }

        updateStatus('error', errorMessage);
    }
}

// Stop camera
function stopCamera() {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }

    if (qrScanner) {
        clearInterval(qrScanner);
        qrScanner = null;
    }

    isScanning = false;

    // Hide video and overlay, show placeholder
    document.getElementById('qr-video').style.display = 'none';
    document.getElementById('qr-overlay').style.display = 'none';
    document.getElementById('qr-placeholder').style.display = 'block';

    // Update buttons
    document.getElementById('start-camera-btn').style.display = 'inline-flex';
    document.getElementById('stop-camera-btn').style.display = 'none';

    updateStatus('ready', 'Ready to start scanning');
}

// Start QR code detection
function startQrDetection() {
    const video = document.getElementById('qr-video');
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    isScanning = true;

    qrScanner = setInterval(() => {
        if (video.readyState === video.HAVE_ENOUGH_DATA && isScanning) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            // For production, you would use a QR code library like jsQR here
            // const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            // const code = jsQR(imageData.data, imageData.width, imageData.height);
            // if (code) { handleQrCodeDetected(code.data); }

            // For now, we'll add a manual test function
            // In a real implementation, this would be automatic QR detection
        }
    }, 500);
}

// Handle QR code detection
async function handleQrCodeDetected(qrData) {
    if (!isScanning) return; // Prevent multiple detections

    isScanning = false;
    clearInterval(qrScanner);

    updateStatus('scanning', 'QR code detected! Processing...');

    // Extract token from QR data URL
    const urlMatch = qrData.match(/\/qr-login\/scan\/([a-zA-Z0-9]+)/);
    if (!urlMatch) {
        updateStatus('error', 'Invalid QR code format');
        setTimeout(() => {
            isScanning = true;
            startQrDetection();
        }, 2000);
        return;
    }

    const token = urlMatch[1];

    try {
        const response = await fetch('<?php echo e(route("qr-login.confirm")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ token })
        });

        const data = await response.json();

        if (data.success) {
            updateStatus('success', 'Login confirmed successfully!');

            // Show success animation
            const frame = document.querySelector('.qr-scanner-frame');
            frame.style.borderColor = '#28a745';
            frame.style.animation = 'none';

            setTimeout(() => {
                // Redirect back to profile or close scanner
                window.location.href = '<?php echo e(route("profile.index")); ?>';
            }, 2000);
        } else {
            updateStatus('error', data.message || 'Failed to confirm login');
            setTimeout(() => {
                isScanning = true;
                startQrDetection();
            }, 3000);
        }
    } catch (error) {
        updateStatus('error', 'Network error occurred');
        setTimeout(() => {
            isScanning = true;
            startQrDetection();
        }, 3000);
    }
}

// Update status display
function updateStatus(type, message) {
    const statusElement = document.getElementById('qr-status');
    const textElement = document.getElementById('status-text');

    statusElement.className = `qr-status-indicator ${type}`;
    textElement.textContent = message;

    // Update icon based on type
    const icon = statusElement.querySelector('i');
    switch(type) {
        case 'ready':
            icon.className = 'fas fa-info-circle';
            break;
        case 'scanning':
            icon.className = 'fas fa-camera';
            break;
        case 'success':
            icon.className = 'fas fa-check-circle';
            break;
        case 'error':
            icon.className = 'fas fa-exclamation-triangle';
            break;
    }
}

// Manual QR code input for testing
function testQrInput() {
    const qrUrl = prompt('Enter QR code URL for testing:');
    if (qrUrl) {
        handleQrCodeDetected(qrUrl);
    }
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        stopCamera();
    } else if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        if (videoStream) {
            stopCamera();
        } else {
            startCamera();
        }
    } else if (e.key === 't' && e.ctrlKey) {
        e.preventDefault();
        testQrInput(); // For testing purposes
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    stopCamera();
});

// Auto-start camera on mobile devices
document.addEventListener('DOMContentLoaded', function() {
    // Check if this is a mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // Add a note for mobile users
        const instructions = document.querySelector('.qr-instructions');
        instructions.innerHTML += '<br><br><strong>Mobile tip:</strong> Tap "Start Camera" and hold your device steady when scanning.';
    }

    // Add test button for development (remove in production)
    const controls = document.querySelector('.qr-scanner-controls');
    const testBtn = document.createElement('button');
    testBtn.type = 'button';
    testBtn.className = 'qr-control-btn';
    testBtn.innerHTML = '<i class="fas fa-keyboard"></i> Test';
    testBtn.onclick = testQrInput;
    testBtn.style.fontSize = '0.8rem';
    testBtn.title = 'Test QR input (Ctrl+T)';
    controls.insertBefore(testBtn, controls.lastElementChild);
});
</script>
<?php $__env->stopSection(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/profile/qr-scanner.blade.php ENDPATH**/ ?>