<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Review;
use App\Models\ReviewImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ReviewController extends Controller
{
    /**
     * Store a newly created review in storage.
     */
    public function store(Request $request, Product $product)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to leave a review.');
        }

        $user = Auth::user();

        // Check if user is banned
        if ($user->isBanned()) {
            return back()->with('error', 'You are banned from posting reviews. Reason: ' . $user->ban_reason);
        }

        // Check if user already reviewed this product
        $existingReview = Review::where('user_id', $user->id)
                               ->where('product_id', $product->id)
                               ->first();

        if ($existingReview) {
            return back()->with('error', 'You have already reviewed this product.');
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'required|string|min:10|max:1000',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // 2MB max per image
        ]);

        // Create the review
        $review = Review::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => $request->rating,
            'title' => $request->title,
            'comment' => $request->comment,
            'is_approved' => true, // Auto-approve for now, can be changed to false for moderation
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            $this->handleImageUploads($request->file('images'), $review);
        }

        return back()->with('success', 'Thank you for your review! It has been posted successfully.');
    }

    /**
     * Handle multiple image uploads for a review.
     */
    private function handleImageUploads($images, Review $review)
    {
        foreach ($images as $index => $image) {
            // Generate unique filename
            $filename = 'review-' . $review->id . '-' . time() . '-' . ($index + 1) . '.' . $image->getClientOriginalExtension();

            // Store the image
            $imagePath = $image->storeAs('reviews', $filename, 'public');

            // Ensure file is accessible via web (Windows XAMPP fix)
            $sourcePath = storage_path('app/public/' . $imagePath);
            $publicPath = public_path('storage/' . $imagePath);

            if (file_exists($sourcePath) && !file_exists($publicPath)) {
                // Create directory if it doesn't exist
                $publicDir = dirname($publicPath);
                if (!is_dir($publicDir)) {
                    mkdir($publicDir, 0755, true);
                }
                // Copy file to public storage
                copy($sourcePath, $publicPath);
            }

            // Save image record
            ReviewImage::create([
                'review_id' => $review->id,
                'image_path' => $imagePath,
                'original_name' => $image->getClientOriginalName(),
                'file_size' => $image->getSize(),
                'mime_type' => $image->getMimeType(),
                'sort_order' => $index,
            ]);
        }
    }

    /**
     * Remove the specified review from storage.
     */
    public function destroy(Review $review)
    {
        $user = Auth::user();

        // Check if user can delete this review
        if (!$user->is_admin && $review->user_id !== $user->id) {
            return back()->with('error', 'You can only delete your own reviews.');
        }

        // Delete associated images
        foreach ($review->images as $image) {
            // Delete from storage
            Storage::disk('public')->delete($image->image_path);

            // Delete from public storage (Windows XAMPP fix)
            $publicPath = public_path('storage/' . $image->image_path);
            if (file_exists($publicPath)) {
                unlink($publicPath);
            }

            // Delete record
            $image->delete();
        }

        // Delete the review
        $review->delete();

        return back()->with('success', 'Review deleted successfully.');
    }
}
