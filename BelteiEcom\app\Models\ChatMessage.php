<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'conversation_id',
        'user_id',
        'sender_type',
        'message',
        'attachments',
        'message_type',
        'is_read',
        'read_at',
        'is_edited',
        'edited_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'attachments' => 'array',
        'metadata' => 'array',
        'is_read' => 'boolean',
        'is_edited' => 'boolean',
        'read_at' => 'datetime',
        'edited_at' => 'datetime',
    ];

    /**
     * Get the conversation this message belongs to.
     */
    public function conversation()
    {
        return $this->belongsTo(ChatConversation::class, 'conversation_id');
    }

    /**
     * Get the user who sent this message.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if message is from customer.
     */
    public function isFromCustomer()
    {
        return $this->sender_type === 'customer';
    }

    /**
     * Check if message is from agent.
     */
    public function isFromAgent()
    {
        return $this->sender_type === 'agent';
    }

    /**
     * Check if message is system message.
     */
    public function isSystemMessage()
    {
        return $this->sender_type === 'system';
    }

    /**
     * Get sender name.
     */
    public function getSenderNameAttribute()
    {
        if ($this->isSystemMessage()) {
            return 'System';
        }

        if ($this->user) {
            return $this->user->name;
        }

        // For guest users, get name from conversation
        if ($this->conversation) {
            return $this->conversation->customer_name;
        }

        return 'Unknown User';
    }

    /**
     * Get sender avatar.
     */
    public function getSenderAvatarAttribute()
    {
        if ($this->isSystemMessage()) {
            return asset('images/system-avatar.png');
        }

        if ($this->user && $this->user->avatar) {
            return asset('storage/' . $this->user->avatar);
        }

        // Default avatar based on sender type
        if ($this->isFromAgent()) {
            return asset('images/agent-avatar.png');
        }

        return asset('images/customer-avatar.png');
    }

    /**
     * Mark message as read.
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    /**
     * Edit message content.
     */
    public function editMessage($newMessage)
    {
        $this->update([
            'message' => $newMessage,
            'is_edited' => true,
            'edited_at' => now(),
        ]);
    }

    /**
     * Get formatted timestamp.
     */
    public function getFormattedTimeAttribute()
    {
        return $this->created_at->format('H:i');
    }

    /**
     * Get formatted date.
     */
    public function getFormattedDateAttribute()
    {
        if ($this->created_at->isToday()) {
            return 'Today';
        } elseif ($this->created_at->isYesterday()) {
            return 'Yesterday';
        } else {
            return $this->created_at->format('M j, Y');
        }
    }

    /**
     * Check if message has attachments.
     */
    public function hasAttachments()
    {
        return !empty($this->attachments);
    }

    /**
     * Get attachment URLs.
     */
    public function getAttachmentUrlsAttribute()
    {
        if (!$this->hasAttachments()) {
            return [];
        }

        return collect($this->attachments)->map(function ($attachment) {
            return asset('storage/' . $attachment['path']);
        })->toArray();
    }

    /**
     * Scope for unread messages.
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for messages by sender type.
     */
    public function scopeBySenderType($query, $senderType)
    {
        return $query->where('sender_type', $senderType);
    }

    /**
     * Scope for messages in conversation.
     */
    public function scopeInConversation($query, $conversationId)
    {
        return $query->where('conversation_id', $conversationId);
    }

    /**
     * Scope for recent messages.
     */
    public function scopeRecent($query, $minutes = 30)
    {
        return $query->where('created_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        // Update conversation's last activity when message is created
        static::created(function ($message) {
            $message->conversation->updateActivity();
        });
    }
}
