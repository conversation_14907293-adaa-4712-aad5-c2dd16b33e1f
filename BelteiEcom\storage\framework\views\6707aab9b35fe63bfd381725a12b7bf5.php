<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner Application - BelteiEcom API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .benefits {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .benefits h3 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .benefit-item {
            text-align: center;
        }

        .benefit-item i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .benefit-item h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .benefit-item p {
            color: #666;
            font-size: 0.9rem;
        }

        .demo-section {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .demo-section h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }

        .demo-credentials {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }

        .back-link {
            display: inline-block;
            color: #667eea;
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 600;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .benefits-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <a href="<?php echo e(route('api.docs')); ?>" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Documentation
        </a>

        <div class="header">
            <h1><i class="fas fa-handshake"></i> Become a Partner</h1>
            <p>Join our partner program and start selling our products on your website with our powerful API integration.</p>
        </div>

        <!-- Benefits Section -->
        <div class="benefits">
            <h3>Partner Benefits</h3>
            <div class="benefits-grid">
                <div class="benefit-item">
                    <i class="fas fa-percentage"></i>
                    <h4>High Commissions</h4>
                    <p>Earn 10-25% commission on every sale</p>
                </div>
                <div class="benefit-item">
                    <i class="fas fa-shipping-fast"></i>
                    <h4>We Handle Shipping</h4>
                    <p>Full fulfillment and customer service</p>
                </div>
                <div class="benefit-item">
                    <i class="fas fa-code"></i>
                    <h4>Easy Integration</h4>
                    <p>Simple REST API with comprehensive docs</p>
                </div>
                <div class="benefit-item">
                    <i class="fas fa-chart-line"></i>
                    <h4>Real-time Analytics</h4>
                    <p>Track sales, commissions, and performance</p>
                </div>
            </div>
        </div>

        <!-- Demo Section -->
        <div class="demo-section">
            <h3><i class="fas fa-flask"></i> Try Our Demo API</h3>
            <p>Test our API immediately with these demo credentials:</p>
            
            <div class="demo-credentials">
API Key: bec_demo_12345678901234567890123456789012<br>
Base URL: <?php echo e(url('/api/v1/partner')); ?><br>
Status: <?php echo e(url('/api/v1/public/status')); ?>

            </div>
            
            <p><strong>Note:</strong> Demo API has limited access and cannot place real orders.</p>
        </div>

        <!-- Application Form -->
        <form action="<?php echo e(route('partner.application.submit')); ?>" method="POST" id="partnerForm">
            <?php echo csrf_field(); ?>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="company_name">Company Name *</label>
                    <input type="text" id="company_name" name="company_name" required>
                </div>
                <div class="form-group">
                    <label for="contact_name">Contact Name *</label>
                    <input type="text" id="contact_name" name="contact_name" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone">
                </div>
            </div>

            <div class="form-group">
                <label for="website">Website URL</label>
                <input type="url" id="website" name="website" placeholder="https://yourwebsite.com">
            </div>

            <div class="form-group">
                <label for="business_description">Business Description *</label>
                <textarea id="business_description" name="business_description" required placeholder="Tell us about your business, target market, and how you plan to use our API..."></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="expected_volume">Expected Monthly Sales Volume</label>
                    <select id="expected_volume" name="expected_volume">
                        <option value="">Select volume...</option>
                        <option value="0-1000">$0 - $1,000</option>
                        <option value="1000-5000">$1,000 - $5,000</option>
                        <option value="5000-10000">$5,000 - $10,000</option>
                        <option value="10000+">$10,000+</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="integration_timeline">Integration Timeline</label>
                    <select id="integration_timeline" name="integration_timeline">
                        <option value="">Select timeline...</option>
                        <option value="immediate">Immediate (within 1 week)</option>
                        <option value="1-month">Within 1 month</option>
                        <option value="3-months">Within 3 months</option>
                        <option value="planning">Still planning</option>
                    </select>
                </div>
            </div>

            <button type="submit" class="btn">
                <i class="fas fa-paper-plane"></i> Submit Application
            </button>
        </form>

        <div style="text-align: center; margin-top: 2rem; color: #666;">
            <p>Applications are typically reviewed within 1-2 business days.</p>
            <p>Questions? Contact us at <strong><EMAIL></strong></p>
        </div>
    </div>

    <script>
        // Form validation and enhancement
        document.getElementById('partnerForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
            submitBtn.disabled = true;
        });

        // Add real-time validation
        document.querySelectorAll('input, textarea, select').forEach(field => {
            field.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.style.borderColor = '#dc3545';
                } else {
                    this.style.borderColor = '#28a745';
                }
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/api/partner-application.blade.php ENDPATH**/ ?>