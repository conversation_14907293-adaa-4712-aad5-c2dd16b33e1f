@extends('layouts.app')

@section('title', 'Newsletter Subscription Confirmed')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0" style="border-radius: 20px; overflow: hidden;">
                <!-- Header -->
                <div class="card-header text-center" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 3rem 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">🎉</div>
                    <h1 style="font-size: 2.5rem; font-weight: 700; margin: 0;">Subscription Confirmed!</h1>
                    <p style="font-size: 1.2rem; margin: 1rem 0 0 0; opacity: 0.9;">Welcome to our newsletter community</p>
                </div>

                <!-- Content -->
                <div class="card-body text-center" style="padding: 3rem 2rem;">
                    <div style="margin-bottom: 2rem;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-check" style="font-size: 2rem; color: white;"></i>
                        </div>
                        
                        <h3 style="color: #2c3e50; margin-bottom: 1rem; font-weight: 600;">Thank You, {{ $newsletter->name ?: 'Valued Customer' }}!</h3>
                        
                        <p style="color: #6c757d; font-size: 1.1rem; line-height: 1.6; margin-bottom: 2rem;">
                            Your email address <strong>{{ $newsletter->email }}</strong> has been successfully confirmed!
                            <br><br>
                            You're now part of our exclusive newsletter community and will receive amazing deals, 
                            early product launches, and insider shopping tips directly to your inbox.
                        </p>
                    </div>

                    <!-- Benefits Section -->
                    <div style="background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%); padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                        <h5 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-gift"></i> What's Coming Your Way:
                        </h5>
                        <div class="row text-left">
                            <div class="col-md-6">
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: #6c757d;">
                                        <i class="fas fa-percent" style="color: #43e97b; margin-right: 0.5rem;"></i>
                                        Exclusive discounts up to 50% off
                                    </li>
                                    <li style="padding: 0.5rem 0; color: #6c757d;">
                                        <i class="fas fa-rocket" style="color: #43e97b; margin-right: 0.5rem;"></i>
                                        Early access to new products
                                    </li>
                                    <li style="padding: 0.5rem 0; color: #6c757d;">
                                        <i class="fas fa-shipping-fast" style="color: #43e97b; margin-right: 0.5rem;"></i>
                                        Free shipping offers
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 0.5rem 0; color: #6c757d;">
                                        <i class="fas fa-lightbulb" style="color: #43e97b; margin-right: 0.5rem;"></i>
                                        Weekly shopping tips & trends
                                    </li>
                                    <li style="padding: 0.5rem 0; color: #6c757d;">
                                        <i class="fas fa-star" style="color: #43e97b; margin-right: 0.5rem;"></i>
                                        Member-only promotions
                                    </li>
                                    <li style="padding: 0.5rem 0; color: #6c757d;">
                                        <i class="fas fa-bolt" style="color: #43e97b; margin-right: 0.5rem;"></i>
                                        Flash sale notifications
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Welcome Email Info -->
                    <div style="background: #f8f9fc; padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                        <h6 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-envelope"></i> Check Your Inbox!
                        </h6>
                        <p style="color: #6c757d; font-size: 0.9rem; margin: 0;">
                            We've sent you a welcome email with more details about your subscription. 
                            If you don't see it in your inbox, please check your spam folder.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div style="margin-top: 2rem;">
                        <a href="{{ route('home') }}" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600; margin-right: 1rem; text-decoration: none;">
                            <i class="fas fa-home"></i> Back to Store
                        </a>
                        
                        <a href="{{ route('products.index') }}" class="btn btn-success" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600;">
                            <i class="fas fa-shopping-bag"></i> Start Shopping
                        </a>
                    </div>

                    <!-- Subscription Details -->
                    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div style="margin-bottom: 1rem;">
                                    <i class="fas fa-calendar-check" style="font-size: 2rem; color: #43e97b; margin-bottom: 0.5rem;"></i>
                                    <h6 style="color: #2c3e50; margin: 0;">Confirmed On</h6>
                                    <small style="color: #6c757d;">{{ $newsletter->email_confirmed_at->format('F j, Y \a\t g:i A') }}</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div style="margin-bottom: 1rem;">
                                    <i class="fas fa-envelope-open-text" style="font-size: 2rem; color: #667eea; margin-bottom: 0.5rem;"></i>
                                    <h6 style="color: #2c3e50; margin: 0;">Email Status</h6>
                                    <span class="badge badge-success" style="font-size: 0.9rem; padding: 0.5rem 1rem; border-radius: 20px;">
                                        Verified & Active
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div style="margin-bottom: 1rem;">
                                    <i class="fas fa-users" style="font-size: 2rem; color: #f093fb; margin-bottom: 0.5rem;"></i>
                                    <h6 style="color: #2c3e50; margin: 0;">Community</h6>
                                    <small style="color: #6c757d;">Newsletter Subscriber</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <p style="color: #6c757d; font-size: 0.9rem; margin: 0;">
                            Questions about your subscription? Contact us at 
                            <a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add some celebration animation
document.addEventListener('DOMContentLoaded', function() {
    // Add confetti effect (simple version)
    const colors = ['#43e97b', '#38f9d7', '#667eea', '#764ba2', '#f093fb'];
    
    for (let i = 0; i < 50; i++) {
        setTimeout(() => {
            createConfetti();
        }, i * 100);
    }
    
    function createConfetti() {
        const confetti = document.createElement('div');
        confetti.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${colors[Math.floor(Math.random() * colors.length)]};
            left: ${Math.random() * 100}vw;
            top: -10px;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: fall 3s linear forwards;
        `;
        
        document.body.appendChild(confetti);
        
        setTimeout(() => {
            confetti.remove();
        }, 3000);
    }
    
    // Add CSS for falling animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fall {
            to {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});
</script>
@endsection
