<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('newsletters', function (Blueprint $table) {
            $table->boolean('email_confirmed')->default(false)->after('is_active');
            $table->timestamp('email_confirmed_at')->nullable()->after('email_confirmed');
            $table->string('confirmation_token')->nullable()->after('subscription_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('newsletters', function (Blueprint $table) {
            $table->dropColumn(['email_confirmed', 'email_confirmed_at', 'confirmation_token']);
        });
    }
};
