@extends('layouts.app')

@section('title', 'Shopping Cart')

@section('styles')
<style>
    .cart-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .cart-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .cart-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .cart-header p {
        color: #666;
        font-size: 1.1rem;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
        font-size: 0.9rem;
        color: #666;
    }

    .breadcrumb a {
        color: #667eea;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .breadcrumb a:hover {
        color: #5a67d8;
    }

    .cart-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 2rem;
        align-items: start;
    }

    .cart-items-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .cart-items-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .cart-items-header h2 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .cart-item {
        padding: 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .cart-item:hover {
        background: #fafafa;
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .item-content {
        display: grid;
        grid-template-columns: 100px 1fr auto auto auto;
        gap: 1rem;
        align-items: center;
    }

    .item-image {
        width: 100px;
        height: 100px;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .item-image:hover img {
        transform: scale(1.05);
    }

    .item-details h3 {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }

    .item-details h3 a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .item-details h3 a:hover {
        color: #667eea;
    }

    .item-category {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .item-price {
        font-size: 1.1rem;
        font-weight: 600;
        color: #667eea;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.25rem;
    }

    .quantity-btn {
        width: 32px;
        height: 32px;
        border: none;
        background: white;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #666;
    }

    .quantity-btn:hover {
        background: #667eea;
        color: white;
    }

    .quantity-input {
        width: 50px;
        text-align: center;
        border: none;
        background: transparent;
        font-weight: 600;
        color: #333;
    }

    .item-subtotal {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
    }

    .remove-btn {
        width: 36px;
        height: 36px;
        border: none;
        background: #fee;
        color: #dc3545;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-btn:hover {
        background: #dc3545;
        color: white;
        transform: scale(1.1);
    }

    .cart-actions {
        padding: 1.5rem;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .continue-shopping {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: white;
        color: #667eea;
        text-decoration: none;
        border-radius: 8px;
        border: 2px solid #667eea;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .continue-shopping:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .clear-cart {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .clear-cart:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .order-summary {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        position: sticky;
        top: 2rem;
    }

    .summary-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
    }

    .summary-header h2 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .summary-content {
        padding: 1.5rem;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
    }

    .summary-row:not(:last-child) {
        border-bottom: 1px solid #f0f0f0;
    }

    .summary-row.total {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        border-top: 2px solid #667eea;
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .checkout-btn {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .checkout-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .empty-cart {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .empty-cart-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: #667eea;
    }

    .empty-cart h2 {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
    }

    .empty-cart p {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .browse-products {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }

    .browse-products:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .cart-container {
            padding: 1rem;
        }

        .cart-content {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .item-content {
            grid-template-columns: 80px 1fr;
            gap: 1rem;
        }

        .item-price,
        .quantity-controls,
        .item-subtotal,
        .remove-btn {
            grid-column: 2;
            justify-self: start;
            margin-top: 0.5rem;
        }

        .cart-actions {
            flex-direction: column;
            gap: 1rem;
        }

        .cart-header h1 {
            font-size: 2rem;
        }
    }
</style>
@endsection

@section('content')
<div class="cart-container">

    <!-- Breadcrumb -->
    <nav class="breadcrumb">
        <a href="{{ route('home') }}"><i class="fas fa-home"></i> Home</a>
        <i class="fas fa-chevron-right"></i>
        <span>Shopping Cart</span>
    </nav>

    <!-- Cart Header -->
    <div class="cart-header">
        <h1><i class="fas fa-shopping-cart"></i> Shopping Cart</h1>
        <p>Review your items and proceed to checkout</p>
    </div>

    @if($cartItems->isEmpty())
        <div class="empty-cart">
            <div class="empty-cart-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h2>Your cart is empty</h2>
            <p>Looks like you haven't added any items to your cart yet. Start shopping to fill it up!</p>
            <a href="{{ route('products.index') }}" class="browse-products">
                <i class="fas fa-shopping-bag"></i> Browse Products
            </a>
        </div>
    @else
        <div class="cart-content">
            <!-- Cart Items Section -->
            <div class="cart-items-section">
                <div class="cart-items-header">
                    <h2>
                        <i class="fas fa-shopping-bag"></i>
                        Cart Items ({{ $cartItems->count() }})
                    </h2>
                </div>

                @foreach($cartItems as $item)
                    <div class="cart-item">
                        <div class="item-content">
                            <div class="item-image">
                                @if($item->product->image)
                                    <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product->name }}">
                                @else
                                    <img src="https://via.placeholder.com/100x100?text=No+Image" alt="No Image">
                                @endif
                            </div>

                            <div class="item-details">
                                <h3>
                                    <a href="{{ route('products.show', $item->product->id) }}">{{ $item->product->name }}</a>
                                </h3>
                                <div class="item-category">{{ $item->product->category->name }}</div>
                                <div class="item-price">${{ number_format($item->product->price, 2) }}</div>
                            </div>

                            <div class="quantity-controls">
                                <form action="{{ route('cart.update', $item->id) }}" method="POST" style="display: flex; align-items: center; gap: 0.5rem;">
                                    @csrf
                                    @method('PATCH')
                                    <button type="button" class="quantity-btn" onclick="decreaseQuantity(this)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" name="quantity" value="{{ $item->quantity }}" min="1" max="{{ $item->product->stock_quantity }}" class="quantity-input" onchange="this.form.submit()">
                                    <button type="button" class="quantity-btn" onclick="increaseQuantity(this)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </form>
                            </div>

                            <div class="item-subtotal">
                                ${{ number_format($item->product->price * $item->quantity, 2) }}
                            </div>

                            <form action="{{ route('cart.remove', $item->id) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="remove-btn" onclick="return confirm('Remove this item from cart?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                @endforeach

                <div class="cart-actions">
                    <a href="{{ route('products.index') }}" class="continue-shopping">
                        <i class="fas fa-arrow-left"></i> Continue Shopping
                    </a>

                    <form action="{{ route('cart.clear') }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="clear-cart" onclick="return confirm('Are you sure you want to clear your entire cart?')">
                            <i class="fas fa-trash"></i> Clear Cart
                        </button>
                    </form>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <div class="summary-header">
                    <h2><i class="fas fa-receipt"></i> Order Summary</h2>
                </div>

                <div class="summary-content">
                    <div class="summary-row">
                        <span>Subtotal ({{ $cartItems->count() }} items):</span>
                        <span>${{ number_format($total, 2) }}</span>
                    </div>

                    <div class="summary-row">
                        <span>Shipping:</span>
                        <span style="color: #28a745; font-weight: 600;">Free</span>
                    </div>

                    <div class="summary-row">
                        <span>Tax:</span>
                        <span>Calculated at checkout</span>
                    </div>

                    <div class="summary-row total">
                        <span>Total:</span>
                        <span>${{ number_format($total, 2) }}</span>
                    </div>

                    <a href="{{ route('orders.checkout') }}" class="checkout-btn">
                        <i class="fas fa-lock"></i> Secure Checkout
                    </a>

                    <div style="text-align: center; margin-top: 1rem; font-size: 0.85rem; color: #666;">
                        <i class="fas fa-shield-alt"></i> Your payment information is secure
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function decreaseQuantity(button) {
    const input = button.parentNode.querySelector('.quantity-input');
    const currentValue = parseInt(input.value);
    if (currentValue > 1) {
        input.value = currentValue - 1;
        input.form.submit();
    }
}

function increaseQuantity(button) {
    const input = button.parentNode.querySelector('.quantity-input');
    const currentValue = parseInt(input.value);
    const maxValue = parseInt(input.max);
    if (currentValue < maxValue) {
        input.value = currentValue + 1;
        input.form.submit();
    }
}
</script>
@endsection
