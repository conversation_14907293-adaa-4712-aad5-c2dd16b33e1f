<?php

namespace Database\Seeders;

use App\Models\ChatConversation;
use App\Models\ChatMessage;
use App\Models\ChatAgentStatus;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ChatSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create agent statuses for admin users
        $admins = User::where('is_admin', true)->get();

        foreach ($admins as $admin) {
            ChatAgentStatus::updateOrCreate(
                ['user_id' => $admin->id],
                [
                    'status' => 'online',
                    'status_message' => 'Available for chat support',
                    'max_conversations' => 5,
                    'current_conversations' => 0,
                    'auto_accept' => true,
                    'skills' => ['general_support', 'technical_support', 'sales'],
                    'last_activity_at' => now(),
                ]
            );
        }

        // Create sample conversations
        $customers = User::where('is_admin', false)->take(5)->get();
        $agent = $admins->first();

        if ($agent && $customers->count() > 0) {
            foreach ($customers as $index => $customer) {
                // Create conversation
                $conversation = ChatConversation::create([
                    'user_id' => $customer->id,
                    'agent_id' => $index < 2 ? $agent->id : null, // First 2 have agents
                    'status' => $index < 2 ? 'active' : 'waiting',
                    'priority' => ['normal', 'high', 'normal', 'urgent', 'low'][$index],
                    'subject' => [
                        'Product inquiry',
                        'Order status question',
                        'Technical support needed',
                        'Refund request',
                        'General question'
                    ][$index],
                    'initial_message' => [
                        'Hi, I have a question about one of your products.',
                        'Can you help me check my order status?',
                        'I\'m having trouble with my account login.',
                        'I need to request a refund for my recent order.',
                        'Do you offer international shipping?'
                    ][$index],
                    'customer_info' => [
                        'ip_address' => '192.168.1.' . (100 + $index),
                        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'url' => 'https://belteiecom.com/products',
                        'timestamp' => now()->subMinutes(rand(5, 60))->toISOString(),
                    ],
                    'started_at' => $index < 2 ? now()->subMinutes(rand(10, 30)) : null,
                    'last_activity_at' => now()->subMinutes(rand(1, 10)),
                ]);

                // Add initial customer message
                ChatMessage::create([
                    'conversation_id' => $conversation->id,
                    'user_id' => $customer->id,
                    'sender_type' => 'customer',
                    'message' => $conversation->initial_message,
                    'message_type' => 'text',
                    'created_at' => $conversation->created_at,
                ]);

                // Add agent responses for active conversations
                if ($conversation->status === 'active') {
                    ChatMessage::create([
                        'conversation_id' => $conversation->id,
                        'user_id' => $agent->id,
                        'sender_type' => 'agent',
                        'message' => 'Hello! I\'m here to help you. Let me look into that for you.',
                        'message_type' => 'text',
                        'created_at' => $conversation->created_at->addMinutes(1),
                    ]);

                    // Add follow-up messages
                    ChatMessage::create([
                        'conversation_id' => $conversation->id,
                        'user_id' => $customer->id,
                        'sender_type' => 'customer',
                        'message' => 'Thank you! I really appreciate your help.',
                        'message_type' => 'text',
                        'created_at' => $conversation->created_at->addMinutes(2),
                    ]);

                    ChatMessage::create([
                        'conversation_id' => $conversation->id,
                        'user_id' => $agent->id,
                        'sender_type' => 'agent',
                        'message' => 'You\'re welcome! Is there anything else I can help you with today?',
                        'message_type' => 'text',
                        'created_at' => $conversation->created_at->addMinutes(3),
                    ]);
                }
            }

            // Create some guest conversations
            for ($i = 0; $i < 3; $i++) {
                $conversation = ChatConversation::create([
                    'user_id' => null,
                    'guest_name' => ['John Doe', 'Jane Smith', 'Mike Johnson'][$i],
                    'guest_email' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'][$i],
                    'session_id' => 'guest_session_' . ($i + 1),
                    'agent_id' => $i === 0 ? $agent->id : null,
                    'status' => $i === 0 ? 'active' : 'waiting',
                    'priority' => 'normal',
                    'subject' => 'Guest inquiry',
                    'initial_message' => 'Hi, I\'m interested in your products but don\'t have an account yet.',
                    'customer_info' => [
                        'ip_address' => '203.0.113.' . (10 + $i),
                        'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                        'url' => 'https://belteiecom.com/',
                        'timestamp' => now()->subMinutes(rand(5, 30))->toISOString(),
                    ],
                    'started_at' => $i === 0 ? now()->subMinutes(rand(5, 15)) : null,
                    'last_activity_at' => now()->subMinutes(rand(1, 5)),
                ]);

                // Add initial message
                ChatMessage::create([
                    'conversation_id' => $conversation->id,
                    'user_id' => null,
                    'sender_type' => 'customer',
                    'message' => $conversation->initial_message,
                    'message_type' => 'text',
                    'created_at' => $conversation->created_at,
                ]);

                // Add agent response for active conversation
                if ($conversation->status === 'active') {
                    ChatMessage::create([
                        'conversation_id' => $conversation->id,
                        'user_id' => $agent->id,
                        'sender_type' => 'agent',
                        'message' => 'Hello! Welcome to BelteiEcom. I\'d be happy to help you learn about our products.',
                        'message_type' => 'text',
                        'created_at' => $conversation->created_at->addMinutes(1),
                    ]);
                }
            }

            // Update agent's current conversation count
            $agentStatus = ChatAgentStatus::where('user_id', $agent->id)->first();
            if ($agentStatus) {
                $agentStatus->update(['current_conversations' => 3]); // 3 active conversations
            }
        }

        $this->command->info('Chat sample data created successfully!');
        $this->command->info('- Created agent statuses for admin users');
        $this->command->info('- Created 8 sample conversations (5 registered users + 3 guests)');
        $this->command->info('- Created sample messages for active conversations');
    }
}
