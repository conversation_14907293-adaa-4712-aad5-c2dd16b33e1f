<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Warehouse;
use App\Models\WarehouseInventory;
use App\Models\InventoryMovement;
use App\Models\Product;
use App\Services\InventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InventoryController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->middleware('auth');
        $this->middleware('admin');
        $this->inventoryService = $inventoryService;
    }

    /**
     * Display inventory dashboard.
     */
    public function index(Request $request)
    {
        $warehouseId = $request->get('warehouse_id');
        $warehouses = Warehouse::getActive();

        // Get inventory summary
        $summary = $this->inventoryService->getInventorySummary($warehouseId);

        // Get low stock alerts
        $lowStockAlerts = $this->inventoryService->getLowStockAlerts($warehouseId);

        // Get recent movements
        $recentMovements = InventoryMovement::with(['warehouse', 'product', 'user'])
                                          ->when($warehouseId, function ($query) use ($warehouseId) {
                                              return $query->where('warehouse_id', $warehouseId);
                                          })
                                          ->orderBy('created_at', 'desc')
                                          ->limit(10)
                                          ->get();

        return view('admin.inventory.index', compact(
            'warehouses',
            'summary',
            'lowStockAlerts',
            'recentMovements',
            'warehouseId'
        ));
    }

    /**
     * Display warehouse inventory.
     */
    public function warehouse(Request $request, $warehouseId)
    {
        $warehouse = Warehouse::findOrFail($warehouseId);

        $query = WarehouseInventory::where('warehouse_id', $warehouseId)
                                  ->with(['product.category']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            switch ($status) {
                case 'low_stock':
                    $query->lowStock();
                    break;
                case 'out_of_stock':
                    $query->outOfStock();
                    break;
                case 'in_stock':
                    $query->inStock();
                    break;
            }
        }

        $inventory = $query->orderBy('quantity', 'asc')->paginate(20);

        return view('admin.inventory.warehouse', compact('warehouse', 'inventory'));
    }

    /**
     * Display product inventory across all warehouses.
     */
    public function product($productId)
    {
        $product = Product::findOrFail($productId);
        $inventory = $this->inventoryService->getProductInventory($productId);

        // Get recent movements for this product
        $movements = InventoryMovement::where('product_id', $productId)
                                    ->with(['warehouse', 'user'])
                                    ->orderBy('created_at', 'desc')
                                    ->limit(20)
                                    ->get();

        return view('admin.inventory.product', compact('product', 'inventory', 'movements'));
    }

    /**
     * Show form to add stock.
     */
    public function addStockForm($productId)
    {
        $product = Product::findOrFail($productId);
        $warehouses = Warehouse::getActive();

        return view('admin.inventory.add-stock', compact('product', 'warehouses'));
    }

    /**
     * Add stock to warehouse.
     */
    public function addStock(Request $request, $productId)
    {
        $request->validate([
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|integer|min:1',
            'unit_cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $this->inventoryService->addStock(
                $productId,
                $request->warehouse_id,
                $request->quantity,
                $request->unit_cost,
                $request->notes,
                Auth::id()
            );

            return redirect()->route('admin.inventory.product', $productId)
                           ->with('success', 'Stock added successfully!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()])->withInput();
        }
    }

    /**
     * Show form to transfer stock.
     */
    public function transferForm($productId)
    {
        $product = Product::findOrFail($productId);
        $warehouses = Warehouse::getActive();
        $inventory = $this->inventoryService->getProductInventory($productId);

        return view('admin.inventory.transfer', compact('product', 'warehouses', 'inventory'));
    }

    /**
     * Transfer stock between warehouses.
     */
    public function transfer(Request $request, $productId)
    {
        $request->validate([
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $this->inventoryService->transferStock(
                $productId,
                $request->from_warehouse_id,
                $request->to_warehouse_id,
                $request->quantity,
                $request->notes,
                Auth::id()
            );

            return redirect()->route('admin.inventory.product', $productId)
                           ->with('success', 'Stock transferred successfully!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()])->withInput();
        }
    }

    /**
     * Adjust stock quantity.
     */
    public function adjust(Request $request, $productId, $warehouseId)
    {
        $request->validate([
            'new_quantity' => 'required|integer|min:0',
            'notes' => 'required|string|max:500',
        ]);

        try {
            $inventory = WarehouseInventory::where('warehouse_id', $warehouseId)
                                         ->where('product_id', $productId)
                                         ->firstOrFail();

            $inventory->adjustStock($request->new_quantity, $request->notes);

            return response()->json([
                'success' => true,
                'message' => 'Stock adjusted successfully!',
                'new_quantity' => $inventory->quantity,
                'available_quantity' => $inventory->available_quantity,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Display inventory movements.
     */
    public function movements(Request $request)
    {
        $filters = $request->only(['warehouse_id', 'product_id', 'type', 'start_date', 'end_date']);
        $movements = $this->inventoryService->getMovements($filters);

        $warehouses = Warehouse::getActive();
        $products = Product::select('id', 'name')->orderBy('name')->get();

        return view('admin.inventory.movements', compact('movements', 'warehouses', 'products', 'filters'));
    }

    /**
     * Export inventory data.
     */
    public function export(Request $request)
    {
        $warehouseId = $request->get('warehouse_id');

        $query = WarehouseInventory::with(['warehouse', 'product.category']);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        $inventory = $query->get();

        $filename = 'inventory_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($inventory) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Warehouse',
                'Product Name',
                'SKU',
                'Category',
                'Quantity',
                'Reserved',
                'Available',
                'Reorder Level',
                'Location Code',
                'Last Restocked',
                'Status'
            ]);

            foreach ($inventory as $item) {
                fputcsv($file, [
                    $item->warehouse->name,
                    $item->product->name,
                    $item->product->sku,
                    $item->product->category->name ?? 'N/A',
                    $item->quantity,
                    $item->reserved_quantity,
                    $item->available_quantity,
                    $item->reorder_level,
                    $item->location_code ?? 'N/A',
                    $item->last_restocked_at ? $item->last_restocked_at->format('Y-m-d') : 'N/A',
                    ucfirst(str_replace('_', ' ', $item->getStockStatus()))
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
