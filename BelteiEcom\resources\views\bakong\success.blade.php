<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - BelteiEcom</title>
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <style>
        :root {
            --primary-color: #0077b6;
            --secondary-color: #00b4d8;
            --accent-color: #90e0ef;
            --light-color: #caf0f8;
            --dark-color: #03045e;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .success-page {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
        }

        .success-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .success-icon {
            color: var(--success-color);
            font-size: 80px;
            margin-bottom: 20px;
        }

        .success-title {
            color: var(--primary-color);
            font-size: 28px;
            margin-bottom: 10px;
        }

        .success-message {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }

        .order-details {
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .order-details h3 {
            color: var(--dark-color);
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--accent-color);
            padding-bottom: 10px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .detail-label {
            font-weight: bold;
            color: var(--dark-color);
        }

        .transaction-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
        }

        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 24px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            text-align: center;
        }

        .btn:hover {
            background-color: var(--dark-color);
        }

        .btn-group {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .product-list {
            margin-top: 20px;
        }

        .product-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .product-name {
            flex: 2;
        }

        .product-price, .product-quantity, .product-total {
            flex: 1;
            text-align: right;
        }

        .order-total {
            font-weight: bold;
            text-align: right;
            margin-top: 15px;
            font-size: 18px;
            color: var(--dark-color);
        }

        @media (max-width: 768px) {
            .detail-row {
                flex-direction: column;
            }
            .detail-value {
                margin-top: 5px;
            }
            .btn-group {
                flex-direction: column;
            }
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-page">
            <div class="success-header">
                <div class="success-icon">✓</div>
                <h1 class="success-title">Payment Successful!</h1>
                <p class="success-message">Your payment has been processed successfully. Thank you for your purchase!</p>
            </div>

            <div class="order-details">
                <h3>Order Details</h3>
                <div class="detail-row">
                    <span class="detail-label">Order ID:</span>
                    <span class="detail-value">{{ $order->id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Order Date:</span>
                    <span class="detail-value">{{ $order->created_at->format('F j, Y, g:i a') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Method:</span>
                    <span class="detail-value">Bakong KHQR</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Payment Status:</span>
                    <span class="detail-value" style="color: var(--success-color); font-weight: bold;">Completed</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Total Amount:</span>
                    <span class="detail-value">${{ number_format($payment->amount, 2) }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Shipping Address:</span>
                    <span class="detail-value">{{ $payment->shipping_address }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Contact Number:</span>
                    <span class="detail-value">{{ $payment->phone_number }}</span>
                </div>
            </div>

            <div class="transaction-details">
                <h3>Transaction Details</h3>
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value">{{ $payment->transaction_id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Transaction Hash:</span>
                    <span class="detail-value">{{ $transactionData['hash'] ?? 'N/A' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">From Account:</span>
                    <span class="detail-value">{{ $transactionData['fromAccountId'] ?? 'N/A' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">To Account:</span>
                    <span class="detail-value">{{ $transactionData['toAccountId'] ?? 'N/A' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Currency:</span>
                    <span class="detail-value">{{ $transactionData['currency'] ?? 'USD' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Amount:</span>
                    <span class="detail-value">{{ $transactionData['amount'] ?? $payment->amount }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">{{ $transactionData['description'] ?? 'Payment for order #' . $order->id }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Transaction Date:</span>
                    <span class="detail-value">
                        @if(isset($transactionData['createdDateMs']))
                            {{ date('F j, Y, g:i a', $transactionData['createdDateMs'] / 1000) }}
                        @else
                            {{ $payment->payment_date ? $payment->payment_date->format('F j, Y, g:i a') : 'N/A' }}
                        @endif
                    </span>
                </div>
            </div>

            <div class="order-details">
                <h3>Purchased Items</h3>
                <div class="product-list">
                    <div class="product-item" style="font-weight: bold;">
                        <div class="product-name">Product</div>
                        <div class="product-price">Price</div>
                        <div class="product-quantity">Qty</div>
                        <div class="product-total">Total</div>
                    </div>

                    @if(isset($order->orderItems) && count($order->orderItems) > 0)
                        @foreach($order->orderItems as $item)
                        <div class="product-item">
                            <div class="product-name">{{ $item->product->name ?? 'Product' }}</div>
                            <div class="product-price">${{ number_format($item->price ?? 0, 2) }}</div>
                            <div class="product-quantity">{{ $item->quantity ?? 1 }}</div>
                            <div class="product-total">${{ number_format(($item->price ?? 0) * ($item->quantity ?? 1), 2) }}</div>
                        </div>
                        @endforeach
                    @else
                        <div class="product-item">
                            <div class="product-name">Test Product</div>
                            <div class="product-price">$0.01</div>
                            <div class="product-quantity">1</div>
                            <div class="product-total">$0.01</div>
                        </div>
                    @endif
                </div>

                <div class="order-total">
                    Total: ${{ number_format($order->total_amount ?? 0.01, 2) }}
                </div>
            </div>

            <div class="btn-group">
                <a href="{{ route('orders.history') }}" class="btn">View Order History</a>
                <a href="{{ route('home') }}" class="btn">Continue Shopping</a>
            </div>
        </div>
    </div>
</body>
</html>
