@extends('admin.layouts.app')

@section('styles')
<style>
    .customer-profile {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .customer-profile::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }

    .customer-profile:hover::before {
        right: -30%;
    }
    
    .customer-avatar-large {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        border: 4px solid rgba(255,255,255,0.3);
    }
    
    .customer-info {
        display: flex;
        align-items: center;
        gap: 2rem;
        position: relative;
        z-index: 5;
    }
    
    .customer-details h2 {
        margin-bottom: 0.5rem;
        font-size: 2rem;
    }
    
    .customer-meta {
        opacity: 0.9;
        margin-bottom: 1rem;
    }
    
    .customer-badges {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .customer-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .orders-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    
    .section-header {
        background: var(--primary-gradient);
        color: white;
        padding: 1.5rem;
        margin: 0;
    }
    
    .order-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e3e6f0;
        transition: all 0.3s ease;
    }
    
    .order-item:hover {
        background: rgba(102, 126, 234, 0.05);
    }
    
    .order-item:last-child {
        border-bottom: none;
    }
    
    .order-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .order-number {
        font-weight: 700;
        color: #667eea;
    }
    
    .order-status {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-new { background: #d1ecf1; color: #0c5460; }
    .status-processing { background: #fff3cd; color: #856404; }
    .status-shipped { background: #d4edda; color: #155724; }
    .status-delivered { background: #d1ecf1; color: #0c5460; }
    .status-cancelled { background: #f8d7da; color: #721c24; }
    
    .order-meta {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        position: relative;
        z-index: 10;
    }

    .action-buttons .btn {
        position: relative;
        z-index: 11;
        pointer-events: auto;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 2px solid rgba(255,255,255,0.3);
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 8px;
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        border-color: rgba(255,255,255,0.5);
    }

    .action-buttons .btn-light {
        background: rgba(255,255,255,0.9);
        color: #667eea;
    }

    .action-buttons .btn-light:hover {
        background: white;
        color: #5a6fd8;
    }

    .action-buttons .btn-outline-light {
        background: transparent;
        color: white;
        border-color: rgba(255,255,255,0.5);
    }

    .action-buttons .btn-outline-light:hover {
        background: rgba(255,255,255,0.1);
        color: white;
        border-color: white;
    }
</style>
@endsection

@section('content')
    <!-- Customer Profile Header -->
    <div class="customer-profile fade-in">
        <div class="customer-info">
            <div class="customer-avatar-large">
                {{ substr($customer->name, 0, 1) }}
            </div>
            <div class="customer-details flex-grow-1">
                <h2>{{ $customer->name }}</h2>
                <div class="customer-meta">
                    <div><i class="fas fa-envelope"></i> {{ $customer->email }}</div>
                    @if($customer->phone)
                        <div><i class="fas fa-phone"></i> {{ $customer->phone }}</div>
                    @endif
                    @if($customer->address)
                        <div><i class="fas fa-map-marker-alt"></i> {{ $customer->address }}</div>
                    @endif
                    <div><i class="fas fa-calendar"></i> Member since {{ $customer->created_at->format('F j, Y') }}</div>
                </div>
                <div class="customer-badges">
                    @if($customerStats['total_spent'] > 1000)
                        <span class="customer-badge">
                            <i class="fas fa-crown"></i> VIP Customer
                        </span>
                    @endif
                    @if($customer->created_at->diffInDays(now()) <= 7)
                        <span class="customer-badge">
                            <i class="fas fa-star"></i> New Customer
                        </span>
                    @endif
                    @if($customerStats['last_order'] && $customerStats['last_order']->created_at->diffInDays(now()) <= 30)
                        <span class="customer-badge">
                            <i class="fas fa-bolt"></i> Active
                        </span>
                    @endif
                </div>
            </div>
            <div class="action-buttons">
                <a href="{{ route('admin.customers.edit', $customer->id) }}" class="btn btn-light">
                    <i class="fas fa-edit"></i> Edit Customer
                </a>
                <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left"></i> Back to Customers
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-row slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-number">{{ $customerStats['total_orders'] }}</div>
            <div class="stat-label">Total Orders</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${{ number_format($customerStats['total_spent'], 2) }}</div>
            <div class="stat-label">Total Spent</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${{ number_format($customerStats['average_order'], 2) }}</div>
            <div class="stat-label">Average Order</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                @if($customerStats['last_order'])
                    {{ $customerStats['last_order']->created_at->diffForHumans() }}
                @else
                    Never
                @endif
            </div>
            <div class="stat-label">Last Order</div>
        </div>
    </div>

    <!-- Orders Section -->
    <div class="orders-section slide-up" style="animation-delay: 0.4s;">
        <div class="section-header">
            <h5 class="mb-0">
                <i class="fas fa-shopping-bag"></i> Order History ({{ $customer->orders->count() }})
            </h5>
        </div>
        <div class="section-body">
            @if($customer->orders->count() > 0)
                @foreach($customer->orders as $order)
                    <div class="order-item">
                        <div class="order-header">
                            <div class="order-number">
                                <a href="{{ route('admin.orders.show', $order->id) }}">Order #{{ $order->id }}</a>
                            </div>
                            <div class="order-status status-{{ $order->status }}">
                                {{ ucfirst($order->status) }}
                            </div>
                        </div>
                        <div class="order-meta">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>${{ number_format($order->total_amount, 2) }}</strong>
                                </div>
                                <div class="col-md-3">
                                    {{ $order->created_at->format('M j, Y') }}
                                </div>
                                <div class="col-md-3">
                                    {{ $order->created_at->format('g:i A') }}
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="text-center py-5">
                    <i class="fas fa-shopping-bag fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">No orders yet</h5>
                    <p class="text-gray-400">This customer hasn't placed any orders.</p>
                </div>
            @endif
        </div>
    </div>
@endsection
