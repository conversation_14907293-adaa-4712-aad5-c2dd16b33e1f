@extends('admin.layouts.app')

@section('title', 'Add Stock - ' . $product->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.inventory.index') }}">Inventory</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.inventory.product', $product->id) }}">{{ $product->name }}</a>
                    </li>
                    <li class="breadcrumb-item active">Add Stock</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus text-success"></i>
                Add Stock - {{ $product->name }}
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.inventory.product', $product->id) }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Product
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Add Stock Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-plus"></i>
                        Add Stock to Warehouse
                    </h6>
                </div>
                <div class="card-body">
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.inventory.add-stock', $product->id) }}">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse *</label>
                                    <select name="warehouse_id" id="warehouse_id" class="form-control" required>
                                        <option value="">Select Warehouse</option>
                                        @foreach($warehouses as $warehouse)
                                            <option value="{{ $warehouse->id }}" {{ old('warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                                                {{ $warehouse->name }} ({{ $warehouse->code }})
                                                @if($warehouse->is_default) - Default @endif
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="quantity">Quantity to Add *</label>
                                    <input type="number" name="quantity" id="quantity" class="form-control" 
                                           min="1" value="{{ old('quantity') }}" required>
                                    <small class="form-text text-muted">Enter the number of units to add to inventory</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="unit_cost">Unit Cost (Optional)</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="number" name="unit_cost" id="unit_cost" class="form-control" 
                                               step="0.01" min="0" value="{{ old('unit_cost') }}">
                                    </div>
                                    <small class="form-text text-muted">Cost per unit for this stock addition</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_cost">Total Cost</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text" id="total_cost" class="form-control" readonly>
                                    </div>
                                    <small class="form-text text-muted">Automatically calculated</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3" 
                                      placeholder="Optional notes about this stock addition...">{{ old('notes') }}</textarea>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Stock
                            </button>
                            <a href="{{ route('admin.inventory.product', $product->id) }}" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Product Info & Current Inventory -->
        <div class="col-lg-4">
            <!-- Product Info -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Product Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($product->image)
                            <img src="{{ asset('storage/' . $product->image) }}" 
                                 alt="{{ $product->name }}" 
                                 class="img-fluid rounded" style="max-height: 150px;">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    <h6 class="text-center">{{ $product->name }}</h6>
                    <hr>
                    <p><strong>SKU:</strong> {{ $product->sku }}</p>
                    <p><strong>Category:</strong> {{ $product->category->name ?? 'N/A' }}</p>
                    <p><strong>Price:</strong> ${{ number_format($product->price, 2) }}</p>
                </div>
            </div>

            <!-- Current Inventory -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">Current Inventory</h6>
                </div>
                <div class="card-body">
                    @php
                        $inventory = $product->inventory ?? collect();
                        $totalStock = $inventory->sum('quantity');
                        $totalReserved = $inventory->sum('reserved_quantity');
                        $totalAvailable = $totalStock - $totalReserved;
                    @endphp
                    
                    <div class="text-center mb-3">
                        <div class="row">
                            <div class="col-4">
                                <h5 class="text-primary">{{ $totalStock }}</h5>
                                <small class="text-muted">Total</small>
                            </div>
                            <div class="col-4">
                                <h5 class="text-warning">{{ $totalReserved }}</h5>
                                <small class="text-muted">Reserved</small>
                            </div>
                            <div class="col-4">
                                <h5 class="text-success">{{ $totalAvailable }}</h5>
                                <small class="text-muted">Available</small>
                            </div>
                        </div>
                    </div>

                    @if($inventory->count() > 0)
                        <hr>
                        <h6 class="text-muted mb-3">By Warehouse:</h6>
                        @foreach($inventory as $item)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ $item->warehouse->name }}</strong><br>
                                    <small class="text-muted">{{ $item->warehouse->code }}</small>
                                </div>
                                <div class="text-right">
                                    <span class="badge badge-{{ $item->quantity > 0 ? 'primary' : 'danger' }}">
                                        {{ $item->quantity }}
                                    </span>
                                    @if($item->reserved_quantity > 0)
                                        <br><small class="text-warning">({{ $item->reserved_quantity }} reserved)</small>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No inventory found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Calculate total cost automatically
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const unitCostInput = document.getElementById('unit_cost');
    const totalCostInput = document.getElementById('total_cost');

    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitCost = parseFloat(unitCostInput.value) || 0;
        const total = quantity * unitCost;
        
        totalCostInput.value = total > 0 ? total.toFixed(2) : '';
    }

    quantityInput.addEventListener('input', calculateTotal);
    unitCostInput.addEventListener('input', calculateTotal);
});
</script>
@endsection
