@extends('admin.layouts.app')

@section('title', 'Inventory Management')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-warehouse text-primary"></i>
            Advanced Inventory Management
        </h1>
        <div class="btn-group">
            <a href="{{ route('admin.inventory.movements') }}" class="btn btn-info btn-sm">
                <i class="fas fa-exchange-alt"></i> View Movements
            </a>
            <a href="{{ route('admin.inventory.export') }}" class="btn btn-success btn-sm">
                <i class="fas fa-download"></i> Export Data
            </a>
        </div>
    </div>

    <!-- Warehouse Filter -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" action="{{ route('admin.inventory.index') }}">
                <div class="input-group">
                    <select name="warehouse_id" class="form-control" onchange="this.form.submit()">
                        <option value="">All Warehouses</option>
                        @foreach($warehouses as $warehouse)
                            <option value="{{ $warehouse->id }}" {{ $warehouseId == $warehouse->id ? 'selected' : '' }}>
                                {{ $warehouse->name }} ({{ $warehouse->code }})
                            </option>
                        @endforeach
                    </select>
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($summary['total_products']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($summary['total_quantity']) }}
                            </div>
                            <div class="text-xs text-muted">
                                Available: {{ number_format($summary['total_available']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cubes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($summary['low_stock_count']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Out of Stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($summary['out_of_stock_count']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Low Stock Alerts -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low Stock Alerts
                    </h6>
                    <span class="badge badge-warning">{{ $lowStockAlerts->count() }}</span>
                </div>
                <div class="card-body">
                    @if($lowStockAlerts->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Warehouse</th>
                                        <th>Stock</th>
                                        <th>Reorder Level</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lowStockAlerts->take(10) as $alert)
                                        <tr>
                                            <td>
                                                <strong>{{ $alert->product->name }}</strong><br>
                                                <small class="text-muted">{{ $alert->product->sku }}</small>
                                            </td>
                                            <td>{{ $alert->warehouse->name }}</td>
                                            <td>
                                                <span class="badge badge-{{ $alert->quantity <= 0 ? 'danger' : 'warning' }}">
                                                    {{ $alert->quantity }}
                                                </span>
                                            </td>
                                            <td>{{ $alert->reorder_level }}</td>
                                            <td>
                                                <a href="{{ route('admin.inventory.add-stock-form', $alert->product_id) }}" 
                                                   class="btn btn-sm btn-primary">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($lowStockAlerts->count() > 10)
                            <div class="text-center mt-3">
                                <small class="text-muted">Showing 10 of {{ $lowStockAlerts->count() }} alerts</small>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">No low stock alerts! All products are well stocked.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Movements -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-exchange-alt"></i>
                        Recent Movements
                    </h6>
                    <a href="{{ route('admin.inventory.movements') }}" class="btn btn-sm btn-info">View All</a>
                </div>
                <div class="card-body">
                    @if($recentMovements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Type</th>
                                        <th>Qty</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentMovements as $movement)
                                        <tr>
                                            <td>
                                                <strong>{{ $movement->product->name }}</strong><br>
                                                <small class="text-muted">{{ $movement->warehouse->name }}</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $movement->type_color }}">
                                                    <i class="{{ $movement->type_icon }}"></i>
                                                    {{ $movement->type_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-{{ $movement->quantity > 0 ? 'success' : 'danger' }}">
                                                    {{ $movement->quantity > 0 ? '+' : '' }}{{ $movement->quantity }}
                                                </span>
                                            </td>
                                            <td>
                                                <small>{{ $movement->created_at->format('M j, H:i') }}</small>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent inventory movements.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouses Overview -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-warehouse"></i>
                        Warehouses Overview
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($warehouses as $warehouse)
                            <div class="col-md-4 mb-3">
                                <div class="card border-left-{{ $warehouse->is_default ? 'primary' : 'secondary' }}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="card-title mb-0">
                                                {{ $warehouse->name }}
                                                @if($warehouse->is_default)
                                                    <span class="badge badge-primary badge-sm">Default</span>
                                                @endif
                                            </h6>
                                            <span class="badge badge-{{ $warehouse->is_active ? 'success' : 'secondary' }}">
                                                {{ $warehouse->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt"></i>
                                                {{ $warehouse->city }}, {{ $warehouse->country }}
                                            </small><br>
                                            <small class="text-muted">
                                                <i class="fas fa-user"></i>
                                                {{ $warehouse->manager_name }}
                                            </small>
                                        </p>
                                        <div class="d-flex justify-content-between">
                                            <a href="{{ route('admin.inventory.warehouse', $warehouse->id) }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View Inventory
                                            </a>
                                            <small class="text-muted align-self-center">
                                                {{ $warehouse->getUniqueProductsCount() }} products
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

.badge-sm {
    font-size: 0.7em;
}
</style>
@endsection
