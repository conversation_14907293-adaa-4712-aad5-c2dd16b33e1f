@extends('layouts.app')

@section('title', 'Change Password')

@section('styles')
<style>
    .profile-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 2rem;
    }

    .breadcrumb-nav {
        margin-bottom: 1rem;
    }

    .breadcrumb-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0;
        transition: all 0.3s ease;
    }

    .breadcrumb-link:hover {
        color: #5a67d8;
        text-decoration: none;
        transform: translateX(-2px);
    }

    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        text-align: center;
    }

    .profile-header h1 {
        font-size: 1.2rem;
        margin-bottom: 0.25rem;
    }

    .profile-header p {
        font-size: 0.85rem;
        margin: 0;
        opacity: 0.9;
    }

    .profile-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .password-input-wrapper {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 0;
        font-size: 1rem;
    }

    .password-toggle:hover {
        color: #667eea;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
    }

    .alert {
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .alert-danger {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .password-requirements {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .password-requirements h6 {
        margin-bottom: 0.5rem;
        color: #333;
    }

    .password-requirements ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
    }

    .password-requirements li {
        margin-bottom: 0.25rem;
    }

    .security-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        border-left: 4px solid #667eea;
    }

    .security-info h4 {
        margin-bottom: 0.5rem;
        color: #333;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .security-info p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .profile-container {
            padding: 1rem;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .btn {
            text-align: center;
            justify-content: center;
        }
    }
</style>
@endsection

@section('content')
<div class="profile-container">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
        <a href="{{ route('profile.index') }}" class="breadcrumb-link">
            <i class="fas fa-arrow-left"></i> Back to Profile
        </a>
    </nav>

    <!-- Profile Header -->
    <div class="profile-header">
        <h1><i class="fas fa-lock"></i> Change Password</h1>
        <p>Update your account password for better security</p>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <ul style="margin: 0; padding-left: 1.5rem;">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Security Info -->
    <div class="security-info">
        <h4><i class="fas fa-shield-alt"></i> Security Tips</h4>
        <p>Choose a strong password that you haven't used elsewhere. A good password should be unique to this account and difficult for others to guess.</p>
    </div>

    <div class="profile-card">
        <form action="{{ route('profile.password.update') }}" method="POST">
            @csrf
            @method('PUT')

            <div class="form-group">
                <label for="current_password" class="form-label">Current Password</label>
                <div class="password-input-wrapper">
                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                        <i class="fas fa-eye" id="current_password_icon"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">New Password</label>
                <div class="password-input-wrapper">
                    <input type="password" class="form-control" id="password" name="password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="password_icon"></i>
                    </button>
                </div>
                <div class="password-requirements">
                    <h6>Password Requirements:</h6>
                    <ul>
                        <li>At least 8 characters long</li>
                        <li>Mix of uppercase and lowercase letters</li>
                        <li>At least one number</li>
                        <li>At least one special character (!@#$%^&*)</li>
                    </ul>
                </div>
            </div>

            <div class="form-group">
                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                <div class="password-input-wrapper">
                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                        <i class="fas fa-eye" id="password_confirmation_icon"></i>
                    </button>
                </div>
            </div>

            <div class="form-actions">
                <a href="{{ route('profile.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Password
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
@endsection
