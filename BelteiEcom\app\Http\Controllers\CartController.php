<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CartController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user's cart.
     */
    public function index()
    {
        $cartItems = Auth::user()->cartItems()->with('product')->get();
        $total = $cartItems->sum(function ($item) {
            return $item->product->price * $item->quantity;
        });

        return view('cart.index', compact('cartItems', 'total'));
    }

    /**
     * Add a product to the cart.
     */
    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
        ]);

        $product = Product::findOrFail($request->product_id);

        // Check if the product has enough stock
        if ($request->quantity > $product->stock) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Sorry, only ' . $product->stock . ' items available in stock.'
                ], 400);
            }
            return redirect()->back()->with('error', 'Sorry, only ' . $product->stock . ' items available in stock.');
        }

        // Check if the product is already in the cart
        $cartItem = CartItem::where('user_id', Auth::id())
                           ->where('product_id', $request->product_id)
                           ->first();

        if ($cartItem) {
            // Check if the updated quantity exceeds stock
            if (($cartItem->quantity + $request->quantity) > $product->stock) {
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Sorry, only ' . $product->stock . ' items available in stock.'
                    ], 400);
                }
                return redirect()->back()->with('error', 'Sorry, only ' . $product->stock . ' items available in stock.');
            }

            // Update quantity if the product is already in the cart
            $cartItem->quantity += $request->quantity;
            $cartItem->save();
            $action = 'updated';
        } else {
            // Add new cart item
            CartItem::create([
                'user_id' => Auth::id(),
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
            ]);
            $action = 'added';
        }

        // Get updated cart count
        $cartCount = CartItem::where('user_id', Auth::id())->sum('quantity');

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $product->name . ' ' . $action . ' to cart successfully!',
                'cart_count' => $cartCount,
                'product_name' => $product->name,
                'action' => $action
            ]);
        }

        return redirect()->back()->with('success', 'Product added to cart successfully!');
    }

    /**
     * Update the quantity of a cart item.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        $cartItem = CartItem::where('user_id', Auth::id())
                           ->where('id', $id)
                           ->firstOrFail();

        // Check if the requested quantity exceeds stock
        $product = Product::findOrFail($cartItem->product_id);
        if ($request->quantity > $product->stock) {
            return redirect()->back()->with('error', 'Sorry, only ' . $product->stock . ' items available in stock.');
        }

        $cartItem->quantity = $request->quantity;
        $cartItem->save();

        return redirect()->route('cart.index')->with('success', 'Cart updated successfully!');
    }

    /**
     * Remove a cart item.
     */
    public function remove($id)
    {
        $cartItem = CartItem::where('user_id', Auth::id())
                           ->where('id', $id)
                           ->firstOrFail();

        $cartItem->delete();

        return redirect()->route('cart.index')->with('success', 'Item removed from cart!');
    }

    /**
     * Clear the entire cart.
     */
    public function clear()
    {
        CartItem::where('user_id', Auth::id())->delete();

        return redirect()->route('cart.index')->with('success', 'Cart cleared successfully!');
    }
}
