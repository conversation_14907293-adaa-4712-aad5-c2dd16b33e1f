@extends('layouts.app')

@section('title', 'Home')

@section('styles')
<style>
    /* Full-width sections */
    .full-width-section {
        width: 100vw;
        position: relative;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
    }

    /* Remove any gaps for home page */
    main {
        padding: 0;
        margin: 0;
    }

    body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    /* Hero Section */
    .hero-section {
        background:
            linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.8) 100%),
            url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        color: white;
        padding: 0;
        margin: 0;
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        display: flex;
        align-items: center;
        width: 100vw;
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 30% 20%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        animation: float 8s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) scale(1);
            opacity: 0.3;
        }
        50% {
            transform: translateY(-20px) scale(1.05);
            opacity: 0.5;
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.6);
        }
    }

    .hero-section::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 120px;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="white"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="white"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="white"></path></svg>');
        background-size: cover;
    }

    .hero-content {
        position: relative;
        z-index: 3;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        max-width: 1400px;
        margin: 0 auto;
        padding: 8rem 2rem;
        min-height: 100vh;
    }

    .hero-text {
        text-align: left;
    }

    .hero-visual {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .hero-badge {
        display: inline-block;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #2d3436;
        padding: 0.75rem 2rem;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 800;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
        animation: pulse 3s infinite;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .hero-title {
        font-size: 4.5rem;
        font-weight: 900;
        margin-bottom: 1.5rem;
        line-height: 1.1;
        text-shadow: 0 4px 20px rgba(0,0,0,0.5);
        letter-spacing: -0.02em;
    }

    .hero-subtitle {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        opacity: 0.95;
        font-weight: 400;
        line-height: 1.4;
        color: #ffd700;
    }

    .hero-description {
        font-size: 1.2rem;
        margin-bottom: 3rem;
        opacity: 0.9;
        font-weight: 300;
        line-height: 1.6;
        max-width: 500px;
    }

    .hero-features {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 3rem;
    }

    .hero-feature {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: rgba(255,255,255,0.9);
    }

    .hero-feature-icon {
        width: 24px;
        height: 24px;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        color: #2d3436;
        flex-shrink: 0;
        font-weight: 800;
    }

    .hero-image-container {
        position: relative;
        width: 100%;
        max-width: 500px;
        height: 400px;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
        transition: all 0.3s ease;
    }

    .hero-image-container:hover {
        transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.05);
    }

    .hero-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .hero-image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(78, 205, 196, 0.2) 100%);
    }

    .hero-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-btn {
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .hero-btn-primary {
        background: rgba(255,255,255,0.2);
        color: white;
        border: 2px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
    }

    .hero-btn-primary:hover {
        background: white;
        color: #667eea;
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .hero-btn-secondary {
        background: transparent;
        color: white;
        border: 2px solid rgba(255,255,255,0.5);
    }

    .hero-btn-secondary:hover {
        background: rgba(255,255,255,0.1);
        transform: translateY(-3px);
        color: white;
    }

    /* Stats Section */
    .stats-section {
        background: white;
        padding: 4rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 3rem;
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 2rem;
        text-align: center;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-size: 1.1rem;
        color: #636e72;
        font-weight: 600;
    }

    /* Features Section */
    .features-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 8rem 0;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .feature-card {
        text-align: center;
        padding: 2rem;
        border-radius: 20px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }

    .feature-card:hover .feature-icon {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }

    .feature-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #2d3436;
    }

    .feature-description {
        color: #636e72;
        line-height: 1.6;
    }

    /* Products Section */
    .products-section {
        background: white;
        padding: 8rem 0;
    }

    /* Testimonials Section */
    .testimonials-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #2d3436;
        padding: 8rem 0;
        position: relative;
        overflow: hidden;
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 3rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        position: relative;
        z-index: 2;
    }

    .testimonial-card {
        background: white;
        border: none;
        border-radius: 20px;
        padding: 2.5rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .testimonial-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .testimonial-card:hover::before {
        opacity: 0.05;
    }

    .testimonial-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .testimonial-card > * {
        position: relative;
        z-index: 2;
    }

    .testimonial-quote {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 2rem;
        font-style: italic;
        color: #636e72;
    }

    .testimonial-author {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2d3436;
    }

    .testimonial-role {
        color: #636e72;
        font-size: 0.9rem;
    }

    .testimonial-stars {
        color: #ffd43b;
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
    }

    /* Newsletter Section */
    .newsletter-section {
        background: transparent;
        padding: 6rem 0;
        color: white;
    }

    .newsletter-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 4rem;
    }

    .newsletter-text {
        flex: 1;
        max-width: 500px;
        text-align: left;
    }

    .newsletter-form {
        display: flex;
        gap: 1rem;
        flex: 1;
        max-width: 400px;
        min-width: 350px;
    }

    .newsletter-input {
        flex: 1;
        padding: 1rem 1.5rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 1rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .newsletter-input::placeholder {
        color: #b2bec3;
    }

    .newsletter-input:focus {
        outline: none;
        border-color: #667eea;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    }

    .newsletter-btn {
        padding: 1rem 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 50px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .newsletter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .section-header {
        text-align: center;
        margin-bottom: 4rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        padding: 0 2rem;
    }

    .section-title {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .section-subtitle {
        font-size: 1.2rem;
        color: #636e72;
        line-height: 1.6;
    }

    /* Newsletter section specific styles */
    .newsletter-section .section-title {
        color: white;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .newsletter-section .section-subtitle {
        color: #b2bec3;
    }

    /* Categories Section */
    .categories-section {
        background: white;
        padding: 6rem 0;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .category-card {
        background: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        transition: all 0.4s ease;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .category-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .category-card:hover::before {
        opacity: 0.05;
    }

    .category-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .category-card > * {
        position: relative;
        z-index: 2;
    }

    .category-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all 0.3s ease;
    }

    .category-card:hover .category-icon {
        transform: scale(1.1);
    }

    .category-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 1rem;
    }

    .category-description {
        color: #636e72;
        margin-bottom: 1.5rem;
    }

    .category-link {
        color: #667eea;
        font-weight: 600;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .category-link:hover {
        color: #764ba2;
    }



    /* Responsive */
    @media (max-width: 768px) {
        .hero-section {
            background-attachment: scroll;
        }

        .hero-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            padding: 4rem 1rem;
            text-align: center;
        }

        .hero-text {
            text-align: center;
            order: 1;
        }

        .hero-visual {
            order: 2;
        }

        .hero-title {
            font-size: 3rem;
        }

        .hero-subtitle {
            font-size: 1.4rem;
        }

        .hero-description {
            font-size: 1.1rem;
            max-width: none;
        }

        .hero-image-container {
            max-width: 350px;
            height: 300px;
            transform: none;
        }

        .hero-image-container:hover {
            transform: scale(1.02);
        }

        .hero-buttons {
            flex-direction: column;
            align-items: center;
        }

        .section-title {
            font-size: 2.5rem;
        }

        .features-grid,
        .categories-grid,
        .testimonials-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }

        .newsletter-content {
            flex-direction: column;
            text-align: center;
            gap: 2rem;
        }

        .newsletter-text {
            text-align: center;
            max-width: 100%;
        }

        .newsletter-form {
            flex-direction: column;
            max-width: 300px;
            margin: 0 auto;
        }



        .features-section,
        .products-section,
        .categories-section,
        .testimonials-section {
            padding: 4rem 0;
        }
    }

    @media (max-width: 480px) {
        .hero-title {
            font-size: 2.2rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .stat-number {
            font-size: 2.5rem;
        }
    }

    /* Add to Cart Animation */
    .add-to-cart-btn.loading {
        position: relative;
        color: transparent !important;
    }

    .add-to-cart-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .add-to-cart-btn.success {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Toast Notification */
    .toast-notification {
        position: fixed;
        top: 100px;
        right: 20px;
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        max-width: 350px;
        backdrop-filter: blur(10px);
    }

    .toast-notification.show {
        transform: translateX(0);
    }

    .toast-notification.error {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    }

    .toast-notification .toast-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .toast-notification .toast-icon {
        font-size: 1.2rem;
    }

    .toast-notification .toast-message {
        font-weight: 600;
        font-size: 0.9rem;
    }

    /* Product Grid Styles */
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .product-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        position: relative;
    }

    .product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .product-image {
        position: relative;
        overflow: hidden;
        height: 250px;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .product-info {
        padding: 1.5rem;
    }

    .product-title {
        margin-bottom: 0.5rem;
    }

    .product-title a {
        color: #2d3436;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        transition: color 0.3s ease;
    }

    .product-title a:hover {
        color: #667eea;
    }

    .product-price {
        font-size: 1.3rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .product-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .product-actions .btn {
        flex: 1;
        min-width: 120px;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 2px solid transparent;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
</style>
@endsection

@section('content')
    <!-- Hero Section -->
    <div class="full-width-section hero-section">
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-badge">✨ Premium Shopping Experience</div>
                <h1 class="hero-title">Your Ultimate Shopping Destination</h1>
                <p class="hero-subtitle">Discover Premium Products at Unbeatable Prices</p>
                <p class="hero-description">Experience the future of online shopping with our curated collection of high-quality products, lightning-fast delivery, and world-class customer service that exceeds expectations.</p>

                <div class="hero-features">
                    <div class="hero-feature">
                        <div class="hero-feature-icon">✓</div>
                        <span>Free Express Shipping Worldwide</span>
                    </div>
                    <div class="hero-feature">
                        <div class="hero-feature-icon">✓</div>
                        <span>24/7 Premium Customer Support</span>
                    </div>
                    <div class="hero-feature">
                        <div class="hero-feature-icon">✓</div>
                        <span>100% Satisfaction Guarantee</span>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="{{ route('products.index') }}" class="hero-btn hero-btn-primary">
                        <i class="fas fa-shopping-bag"></i> Explore Collection
                    </a>
                    <a href="#features" class="hero-btn hero-btn-secondary">
                        <i class="fas fa-star"></i> Why Choose Us
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="hero-image-container">
                    <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80" alt="Shopping Experience" class="hero-image">
                    <div class="hero-image-overlay"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="full-width-section stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ \App\Models\Product::count() }}+</div>
                    <div class="stat-label">Products Available</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ \App\Models\User::where('is_admin', false)->count() }}+</div>
                    <div class="stat-label">Happy Customers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ \App\Models\Order::count() }}+</div>
                    <div class="stat-label">Orders Delivered</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ \App\Models\Category::count() }}+</div>
                    <div class="stat-label">Categories</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="full-width-section features-section" id="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose BelteiEcom?</h2>
                <p class="section-subtitle">Experience the difference with our commitment to excellence, quality, and customer satisfaction that sets us apart from the competition.</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3 class="feature-title">Lightning-Fast Delivery</h3>
                    <p class="feature-description">Experience our premium express shipping service with same-day delivery in major cities and free worldwide shipping on all orders over $50.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Bank-Level Security</h3>
                    <p class="feature-description">Shop with complete peace of mind using our military-grade encryption and advanced fraud protection systems that safeguard every transaction.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">Premium Support</h3>
                    <p class="feature-description">Access our award-winning customer service team available 24/7 through live chat, phone, and email for instant assistance and expert guidance.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Products Section -->
    <div class="full-width-section products-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Premium Collection</h2>
                <p class="section-subtitle">Discover our carefully curated selection of trending products, bestsellers, and exclusive items that define quality and style.</p>
            </div>
            <div class="product-grid">
                @forelse($products as $product)
                    <div class="product-card">
                        <div class="product-image">
                            @if($product->image)
                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                            @else
                                <img src="https://via.placeholder.com/300x200?text=No+Image" alt="No Image">
                            @endif
                        </div>
                        <div class="product-info">
                            <h3 class="product-title">
                                <a href="{{ route('products.show', $product->slug) }}">{{ $product->name }}</a>
                            </h3>
                            <div class="product-price">${{ number_format($product->price, 2) }}</div>
                            <div class="product-actions">
                                <a href="{{ route('products.show', $product->slug) }}" class="btn btn-outline">
                                    <i class="fas fa-eye"></i> View Details
                                </a>

                                @auth
                                    <form action="{{ route('cart.add') }}" method="POST" class="add-to-cart-form" data-product-id="{{ $product->id }}" data-product-name="{{ $product->name }}">
                                        @csrf
                                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-secondary add-to-cart-btn">
                                            <i class="fas fa-cart-plus"></i> Add to Cart
                                        </button>
                                    </form>
                                @endauth
                            </div>
                        </div>
                    </div>
                @empty
                    <div style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
                        <p>No products available at the moment.</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Categories Section -->
    <div class="full-width-section categories-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Explore Categories</h2>
                <p class="section-subtitle">Browse through our comprehensive collection of premium products organized by category for your convenience.</p>
            </div>
            <div class="categories-grid">
            @php
                $electronicsCategory = \App\Models\Category::where('name', 'Electronics')->first();
                $clothingCategory = \App\Models\Category::where('name', 'Clothing')->first();
                $homeKitchenCategory = \App\Models\Category::where('name', 'Home & Kitchen')->first();
                $booksCategory = \App\Models\Category::where('name', 'Books')->first();
            @endphp

            @if($electronicsCategory)
            <a href="{{ route('products.category', $electronicsCategory->slug) }}" style="text-decoration: none; color: inherit;">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="category-title">Electronics</h3>
                    <p class="category-description">Cutting-edge technology and premium gadgets for the modern lifestyle</p>
                    <span class="category-link">Explore Electronics →</span>
                </div>
            </a>
            @endif

            @if($clothingCategory)
            <a href="{{ route('products.category', $clothingCategory->slug) }}" style="text-decoration: none; color: inherit;">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <h3 class="category-title">Clothing</h3>
                    <p class="category-description">Premium fashion and accessories for every style and occasion</p>
                    <span class="category-link">Discover Clothing →</span>
                </div>
            </a>
            @endif

            @if($homeKitchenCategory)
            <a href="{{ route('products.category', $homeKitchenCategory->slug) }}" style="text-decoration: none; color: inherit;">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="category-title">Home & Kitchen</h3>
                    <p class="category-description">Transform your space with our curated home and kitchen essentials</p>
                    <span class="category-link">Browse Home →</span>
                </div>
            </a>
            @endif

            @if($booksCategory)
            <a href="{{ route('products.category', $booksCategory->slug) }}" style="text-decoration: none; color: inherit;">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="category-title">Books</h3>
                    <p class="category-description">Expand your knowledge with our collection of books and educational content</p>
                    <span class="category-link">Explore Books →</span>
                </div>
            </a>
            @endif
            </div>
        </div>
    </div>

    <!-- Testimonials Section -->
    <div class="full-width-section testimonials-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Customer Success Stories</h2>
                <p class="section-subtitle">Join thousands of satisfied customers who have experienced the BelteiEcom difference and discovered their perfect shopping destination.</p>
            </div>
            <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-stars">★★★★★</div>
                <p class="testimonial-quote">"Exceptional quality and lightning-fast delivery! BelteiEcom has become my go-to destination for premium products. The user experience is seamless, and their customer service team goes above and beyond expectations."</p>
                <div class="testimonial-author">Sophea Chea</div>
                <div class="testimonial-role">Premium Customer • Phnom Penh</div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-stars">★★★★★</div>
                <p class="testimonial-quote">"Outstanding service and unbeatable value! The team helped me find exactly what I needed, and the express shipping to Siem Reap was incredibly fast. This is how online shopping should be done!"</p>
                <div class="testimonial-author">Dara Sok</div>
                <div class="testimonial-role">VIP Customer • Siem Reap</div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-stars">★★★★★</div>
                <p class="testimonial-quote">"Incredible product variety and premium quality! From cutting-edge electronics to beautiful home essentials, BelteiEcom offers everything I need with reliable delivery and professional service."</p>
                <div class="testimonial-author">Channary Lim</div>
                <div class="testimonial-role">Loyal Customer • Battambang</div>
            </div>
        </div>
    </div>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle add to cart forms
    document.querySelectorAll('.add-to-cart-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const button = this.querySelector('.add-to-cart-btn');
            const originalText = button.innerHTML;
            const productName = this.dataset.productName;

            // Show loading state
            button.classList.add('loading');
            button.disabled = true;

            // Prepare form data
            const formData = new FormData(this);

            // Send AJAX request
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading state
                button.classList.remove('loading');
                button.disabled = false;

                if (data.success) {
                    // Show success state
                    button.classList.add('success');
                    button.innerHTML = '<i class="fas fa-check"></i> Added!';

                    // Update cart count in header if exists
                    updateCartCount(data.cart_count);

                    // Show success toast
                    showToast(data.message, 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.classList.remove('success');
                        button.innerHTML = originalText;
                    }, 2000);
                } else {
                    // Show error toast
                    showToast(data.message, 'error');
                    button.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Remove loading state
                button.classList.remove('loading');
                button.disabled = false;
                button.innerHTML = originalText;

                // Show error toast
                showToast('Something went wrong. Please try again.', 'error');
            });
        });
    });
});

function updateCartCount(count) {
    // Update cart count in header if cart link exists
    const cartLink = document.querySelector('.cart-link');
    if (cartLink) {
        // Look for existing count badge or create one
        let countBadge = cartLink.querySelector('.cart-count');
        if (!countBadge) {
            countBadge = document.createElement('span');
            countBadge.className = 'cart-count';
            countBadge.style.cssText = `
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ff6b6b;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 0.7rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
            `;
            cartLink.style.position = 'relative';
            cartLink.appendChild(countBadge);
        }
        countBadge.textContent = count;

        // Animate the badge
        countBadge.style.transform = 'scale(1.3)';
        setTimeout(() => {
            countBadge.style.transform = 'scale(1)';
        }, 200);
    }
}

function showToast(message, type = 'success') {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => {
        toast.remove();
    });

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <div class="toast-icon">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            </div>
            <div class="toast-message">${message}</div>
        </div>
    `;

    // Add to page
    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Hide toast after 4 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 4000);
}
</script>
@endsection

@endsection
