<!-- Live Chat Widget -->
<div id="chat-widget" class="chat-widget">
    <!-- Chat <PERSON>ton -->
    <div id="chat-button" class="chat-button" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
        <span id="unread-count" class="unread-count" style="display: none;">0</span>
    </div>

    <!-- Chat Window -->
    <div id="chat-window" class="chat-window" style="display: none;">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="d-flex align-items-center">
                <div class="chat-avatar">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="chat-info">
                    <h6 class="mb-0">Live Support</h6>
                    <small id="agent-status">We're here to help!</small>
                </div>
            </div>
            <div class="chat-controls">
                <button class="btn-minimize" onclick="minimizeChat()">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="btn-close" onclick="closeChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Chat Body -->
        <div class="chat-body">
            <!-- Start Chat Form -->
            <div id="start-chat-form" class="start-chat-form">
                <div class="welcome-message">
                    <h5>👋 Welcome to BelteiEcom!</h5>
                    <p>How can we help you today?</p>
                </div>
                
                <form id="chat-start-form">
                    @guest
                        <div class="form-group">
                            <input type="text" id="guest-name" class="form-control" placeholder="Your name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="guest-email" class="form-control" placeholder="Your email">
                        </div>
                    @endguest
                    
                    <div class="form-group">
                        <select id="chat-subject" class="form-control">
                            <option value="">Select a topic</option>
                            <option value="Product inquiry">Product inquiry</option>
                            <option value="Order status">Order status</option>
                            <option value="Technical support">Technical support</option>
                            <option value="Billing question">Billing question</option>
                            <option value="General question">General question</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <textarea id="initial-message" class="form-control" rows="3" 
                                  placeholder="Type your message here..." required></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-paper-plane"></i> Start Chat
                    </button>
                </form>
                
                <div class="chat-status mt-3">
                    <div id="online-status" class="text-center">
                        <i class="fas fa-circle text-success"></i>
                        <small>Our team is online</small>
                    </div>
                </div>
            </div>

            <!-- Chat Messages -->
            <div id="chat-messages" class="chat-messages" style="display: none;">
                <!-- Messages will be loaded here -->
            </div>
        </div>

        <!-- Chat Footer -->
        <div id="chat-footer" class="chat-footer" style="display: none;">
            <form id="message-form" class="message-form">
                <div class="input-group">
                    <input type="text" id="message-input" class="form-control" 
                           placeholder="Type a message..." maxlength="1000">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </form>
            
            <div class="chat-actions mt-2">
                <button class="btn btn-sm btn-outline-secondary" onclick="endChat()">
                    <i class="fas fa-times"></i> End Chat
                </button>
                <small class="text-muted ml-2" id="typing-indicator" style="display: none;">
                    Agent is typing...
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
}

.chat-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0,0,0,0.2);
}

.chat-button i {
    font-size: 24px;
}

.unread-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.chat-window {
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    display: flex;
    flex-direction: column;
    position: absolute;
    bottom: 80px;
    right: 0;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.chat-info h6 {
    margin: 0;
    font-weight: 600;
}

.chat-controls {
    display: flex;
    gap: 5px;
}

.btn-minimize, .btn-close {
    background: none;
    border: none;
    color: white;
    padding: 5px;
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.2s;
}

.btn-minimize:hover, .btn-close:hover {
    background: rgba(255,255,255,0.2);
}

.chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.start-chat-form .welcome-message {
    text-align: center;
    margin-bottom: 20px;
}

.start-chat-form .welcome-message h5 {
    color: #333;
    margin-bottom: 10px;
}

.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
}

.btn-block {
    width: 100%;
}

.chat-status {
    text-align: center;
    padding: 10px;
    border-top: 1px solid #eee;
}

.chat-messages {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px 0;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.message.customer {
    justify-content: flex-end;
}

.message.agent {
    justify-content: flex-start;
}

.message-content {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 15px;
    font-size: 14px;
    line-height: 1.4;
}

.message.customer .message-content {
    background: #667eea;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.agent .message-content {
    background: #f1f3f4;
    color: #333;
    border-bottom-left-radius: 5px;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
}

.chat-footer {
    padding: 15px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.chat-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.btn-outline-secondary {
    background: none;
    border: 1px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .chat-widget {
        bottom: 10px;
        right: 10px;
    }
    
    .chat-window {
        width: 300px;
        height: 450px;
    }
}
</style>

<script>
let currentConversationId = null;
let lastMessageId = 0;
let chatInterval = null;

// Initialize chat widget
document.addEventListener('DOMContentLoaded', function() {
    checkChatStatus();
    
    // Set up form handlers
    document.getElementById('chat-start-form').addEventListener('submit', startChat);
    document.getElementById('message-form').addEventListener('submit', sendMessage);
});

function toggleChat() {
    const chatWindow = document.getElementById('chat-window');
    const chatButton = document.getElementById('chat-button');
    
    if (chatWindow.style.display === 'none') {
        chatWindow.style.display = 'flex';
        chatButton.style.display = 'none';
    } else {
        chatWindow.style.display = 'none';
        chatButton.style.display = 'flex';
    }
}

function minimizeChat() {
    document.getElementById('chat-window').style.display = 'none';
    document.getElementById('chat-button').style.display = 'flex';
}

function closeChat() {
    if (currentConversationId && confirm('Are you sure you want to end this chat?')) {
        endChat();
    } else {
        minimizeChat();
    }
}

function checkChatStatus() {
    fetch('/chat/widget')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateOnlineStatus(data.is_available);
            
            if (data.has_active_conversation) {
                currentConversationId = data.active_conversation_id;
                showChatInterface();
                loadMessages();
                startPolling();
            }
        }
    })
    .catch(error => {
        console.error('Error checking chat status:', error);
    });
}

function updateOnlineStatus(isOnline) {
    const statusElement = document.getElementById('online-status');
    if (isOnline) {
        statusElement.innerHTML = '<i class="fas fa-circle text-success"></i> <small>Our team is online</small>';
    } else {
        statusElement.innerHTML = '<i class="fas fa-circle text-secondary"></i> <small>We\'ll respond soon</small>';
    }
}

function startChat(e) {
    e.preventDefault();
    
    const formData = {
        message: document.getElementById('initial-message').value,
        subject: document.getElementById('chat-subject').value,
        priority: 'normal'
    };
    
    // Add guest info if not logged in
    const guestName = document.getElementById('guest-name');
    const guestEmail = document.getElementById('guest-email');
    
    if (guestName) {
        formData.guest_name = guestName.value;
    }
    if (guestEmail) {
        formData.guest_email = guestEmail.value;
    }
    
    fetch('/chat/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentConversationId = data.conversation.id;
            showChatInterface();
            loadMessages();
            startPolling();
        } else {
            alert('Error starting chat: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error starting chat:', error);
        alert('An error occurred while starting the chat.');
    });
}

function showChatInterface() {
    document.getElementById('start-chat-form').style.display = 'none';
    document.getElementById('chat-messages').style.display = 'block';
    document.getElementById('chat-footer').style.display = 'block';
    document.getElementById('agent-status').textContent = 'Connected to support';
}

function sendMessage(e) {
    e.preventDefault();
    
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();
    
    if (!message || !currentConversationId) return;
    
    // Add message to UI immediately
    addMessageToUI(message, 'customer', 'Just now');
    messageInput.value = '';
    
    fetch(`/chat/conversation/${currentConversationId}/message`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('Error sending message: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
    });
}

function loadMessages() {
    if (!currentConversationId) return;
    
    fetch(`/chat/conversation/${currentConversationId}/messages?last_message_id=${lastMessageId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.messages.length > 0) {
            data.messages.forEach(message => {
                addMessageToUI(message.message, message.sender_type, message.formatted_time);
                lastMessageId = Math.max(lastMessageId, message.id);
            });
        }
    })
    .catch(error => {
        console.error('Error loading messages:', error);
    });
}

function addMessageToUI(message, senderType, time) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${senderType}`;
    
    messageDiv.innerHTML = `
        <div class="message-content">
            ${message}
            <div class="message-time">${time}</div>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function startPolling() {
    if (chatInterval) clearInterval(chatInterval);
    
    chatInterval = setInterval(() => {
        loadMessages();
    }, 3000); // Poll every 3 seconds
}

function endChat() {
    if (!currentConversationId) return;
    
    fetch(`/chat/conversation/${currentConversationId}/close`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (chatInterval) clearInterval(chatInterval);
            currentConversationId = null;
            lastMessageId = 0;
            
            // Reset UI
            document.getElementById('start-chat-form').style.display = 'block';
            document.getElementById('chat-messages').style.display = 'none';
            document.getElementById('chat-footer').style.display = 'none';
            document.getElementById('chat-messages').innerHTML = '';
            document.getElementById('chat-start-form').reset();
            
            minimizeChat();
        }
    })
    .catch(error => {
        console.error('Error ending chat:', error);
    });
}
</script>
