@extends('admin.layouts.app')

@section('styles')
<style>
    .category-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .category-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .category-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .category-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .category-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .products-count {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .parent-badge {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .category-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-edit-cat {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-edit-cat:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-delete-cat {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-delete-cat:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        color: white;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .page-header .d-flex {
        position: relative;
        z-index: 2;
    }

    .btn {
        position: relative;
        z-index: 10;
        pointer-events: auto;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">🏷️ Categories Management</h1>
                <p class="mb-0 opacity-75">Organize your products with categories</p>
            </div>
            <div>
                <a href="{{ route('admin.categories.create') }}" class="btn btn-light btn-lg" style="text-decoration: none; position: relative; z-index: 10; display: inline-block;" onclick="console.log('Category button clicked!'); return true;">
                    <i class="fas fa-plus"></i> Add New Category
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-cards slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Category::count() }}</div>
            <div class="stat-label">Total Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Category::whereNull('parent_id')->count() }}</div>
            <div class="stat-label">Parent Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Category::whereNotNull('parent_id')->count() }}</div>
            <div class="stat-label">Sub Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Product::count() }}</div>
            <div class="stat-label">Total Products</div>
        </div>
    </div>

    <!-- Categories Grid -->
    <div class="slide-up" style="animation-delay: 0.4s;">
        @if($categories->count() > 0)
            <div class="categories-grid">
                @foreach($categories as $category)
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-{{ $category->parent ? 'tag' : 'folder' }}"></i>
                        </div>

                        <div class="category-name">{{ $category->name }}</div>

                        @if($category->description)
                            <div class="category-description">{{ Str::limit($category->description, 100) }}</div>
                        @endif

                        <div class="category-stats">
                            <div class="products-count">
                                <i class="fas fa-cube"></i> {{ $category->products()->count() }} Products
                            </div>

                            @if($category->parent)
                                <div class="parent-badge">
                                    <i class="fas fa-arrow-up"></i> {{ $category->parent->name }}
                                </div>
                            @else
                                <div class="parent-badge" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                    <i class="fas fa-crown"></i> Parent
                                </div>
                            @endif
                        </div>

                        <div class="category-actions">
                            <a href="{{ route('admin.categories.edit', $category->id) }}" class="btn btn-edit-cat" title="Edit Category">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <form action="{{ route('admin.categories.destroy', $category->id) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-delete-cat" onclick="return confirm('Are you sure you want to delete this category?')" title="Delete Category">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="mt-4 d-flex justify-content-center">
                {{ $categoriesPaginator->links() }}
            </div>
        @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-tags fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">No categories found</h5>
                    <p class="text-gray-400">Start by creating your first product category.</p>
                    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Category
                    </a>
                </div>
            </div>
        @endif
    </div>
@endsection
