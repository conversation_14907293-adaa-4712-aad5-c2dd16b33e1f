<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class OtpCode extends Model
{
    protected $fillable = [
        'email',
        'code',
        'type',
        'status',
        'user_id',
        'ip_address',
        'user_agent',
        'expires_at',
        'used_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
    ];

    /**
     * Get the user associated with this OTP (if exists)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the OTP is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the OTP is valid for use
     */
    public function isValid(): bool
    {
        return $this->status === 'pending' && !$this->isExpired();
    }

    /**
     * Generate a 6-digit OTP code
     */
    public static function generateCode(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Create a new OTP for email login
     */
    public static function createForLogin(string $email): self
    {
        // Check if user exists in system
        $user = User::where('email', $email)->first();

        // Invalidate any existing pending OTPs for this email
        self::where('email', $email)
            ->where('status', 'pending')
            ->update(['status' => 'expired']);

        return self::create([
            'email' => $email,
            'code' => self::generateCode(),
            'type' => 'login',
            'status' => 'pending',
            'user_id' => $user?->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'expires_at' => Carbon::now()->addMinute(), // 1 minute expiry
        ]);
    }

    /**
     * Verify OTP code
     */
    public static function verify(string $email, string $code): ?self
    {
        $otp = self::where('email', $email)
            ->where('code', $code)
            ->where('status', 'pending')
            ->first();

        if (!$otp || $otp->isExpired()) {
            return null;
        }

        $otp->update([
            'status' => 'used',
            'used_at' => Carbon::now(),
        ]);

        return $otp;
    }

    /**
     * Clean up expired OTPs
     */
    public static function cleanupExpired(): int
    {
        return self::where('expires_at', '<', Carbon::now())
            ->where('status', 'pending')
            ->update(['status' => 'expired']);
    }

    /**
     * Check if user exists in system
     */
    public function hasUser(): bool
    {
        return !is_null($this->user_id);
    }
}
