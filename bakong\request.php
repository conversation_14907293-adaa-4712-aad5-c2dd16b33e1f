<?php

namespace BelteiEcom\Bakong;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

// This file handles the initial KHQR payment request
// It receives the order details and prepares the payment request

// Include the save.php file to save payment details
require_once __DIR__ . '/save.php';

// Get the order ID from the request
$orderId = $_GET['order_id'] ?? null;

if (!$orderId) {
    // Redirect back to checkout if no order ID is provided
    header('Location: /checkout');
    exit;
}

try {
    // Connect to the database
    $pdo = DB::connection()->getPdo();
    
    // Get order details
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new \Exception("Order not found");
    }
    
    // Get user details
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$order['user_id']]);
    $user = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new \Exception("User not found");
    }
    
    // Prepare payment data
    $paymentData = [
        'order_id' => $order['id'],
        'amount' => $order['total_amount'],
        'user_id' => $user['id'],
        'user_name' => $user['name'],
        'user_email' => $user['email'],
        'phone_number' => $order['phone_number'],
        'shipping_address' => $order['shipping_address'],
        'payment_method' => 'khqr',
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Save payment data
    $paymentId = savePayment($paymentData);
    
    // Redirect to QR code page
    header("Location: /bakong/qrcode.php?payment_id=$paymentId");
    exit;
    
} catch (\Exception $e) {
    // Log the error
    Log::error('KHQR Payment Request Error: ' . $e->getMessage());
    
    // Redirect back to checkout with error
    header('Location: /checkout?error=' . urlencode('Failed to process KHQR payment. Please try again.'));
    exit;
}
