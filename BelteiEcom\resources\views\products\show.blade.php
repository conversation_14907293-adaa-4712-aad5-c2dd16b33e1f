@extends('layouts.app')

@section('title', $product->name)

@section('styles')
<style>
    /* Override main container for product page */
    main .container {
        max-width: 1400px;
        padding: 0 2rem;
    }

    /* Breadcrumb Navigation */
    .breadcrumb-nav {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem 0;
        margin-bottom: 3rem;
        border-radius: 0 0 20px 20px;
    }

    .breadcrumb-content {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .back-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: white;
        color: #667eea;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border: 2px solid transparent;
    }

    .back-btn:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        text-decoration: none;
    }

    .breadcrumb-divider {
        color: #636e72;
        font-size: 1.2rem;
    }

    .breadcrumb-current {
        color: #667eea;
        font-weight: 600;
        font-size: 1.1rem;
    }

    /* Product Layout */
    .product-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        max-width: 1400px;
        margin: 0 auto 4rem;
        padding: 0 2rem;
    }

    /* Product Image Section */
    .product-image-section {
        position: relative;
    }

    .product-image-container {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        background: #f8f9fa;
        box-shadow: 0 20px 50px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .product-image-container:hover {
        transform: translateY(-5px);
        box-shadow: 0 30px 70px rgba(0,0,0,0.15);
    }

    .product-main-image {
        width: 100%;
        height: 500px;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .product-image-container:hover .product-main-image {
        transform: scale(1.05);
    }

    .product-badge {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 700;
        font-size: 0.9rem;
        z-index: 2;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }

    .stock-badge {
        position: absolute;
        top: 1.5rem;
        left: 1.5rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
        z-index: 2;
    }

    .stock-in {
        background: #28a745;
        color: white;
    }

    .stock-low {
        background: #ffc107;
        color: #212529;
    }

    .stock-out {
        background: #dc3545;
        color: white;
    }

    /* Product Info Section */
    .product-info-section {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .product-header {
        border-bottom: 2px solid #f8f9fa;
        padding-bottom: 2rem;
    }

    .product-category {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #667eea;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .product-category:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
    }

    .product-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: #2d3436;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .product-price {
        font-size: 2rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .stars {
        color: #ffd43b;
        font-size: 1.2rem;
    }

    .rating-text {
        color: #636e72;
        font-size: 0.9rem;
    }

    /* Product Description */
    .product-description {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .description-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .description-text {
        color: #636e72;
        line-height: 1.6;
        font-size: 1rem;
    }

    /* Purchase Section */
    .purchase-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.05);
        position: sticky;
        top: 2rem;
    }

    .quantity-selector {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .quantity-label {
        font-weight: 600;
        color: #2d3436;
        font-size: 1rem;
    }

    .quantity-input {
        width: 80px;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        text-align: center;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .quantity-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .purchase-btn {
        width: 100%;
        padding: 1rem 2rem;
        border: none;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        text-decoration: none;
        margin-bottom: 1rem;
    }

    .purchase-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .purchase-btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .purchase-btn-disabled {
        background: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
    }

    /* Professional Wishlist Button */
    .wishlist-btn {
        width: 100%;
        padding: 1rem 1.5rem;
        border: 2px solid #dc3545;
        background: white;
        color: #dc3545;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin-top: 1rem;
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .wishlist-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .wishlist-btn:hover::before {
        left: 100%;
    }

    .wishlist-btn:hover {
        background: #dc3545;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        text-decoration: none;
    }

    .wishlist-btn.in-wishlist {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .wishlist-btn.in-wishlist:hover {
        background: #c82333;
        border-color: #c82333;
    }

    .wishlist-btn i {
        font-size: 1.2rem;
        transition: all 0.3s ease;
    }

    .wishlist-btn.in-wishlist i {
        animation: heartBeat 0.5s ease;
    }

    @keyframes heartBeat {
        0% { transform: scale(1); }
        25% { transform: scale(1.2); }
        50% { transform: scale(1); }
        75% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .purchase-btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .purchase-btn-outline:hover {
        background: #667eea;
        color: white;
        transform: translateY(-3px);
        text-decoration: none;
    }

    /* Product Features */
    .product-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 2rem;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
    }

    .feature-text {
        font-weight: 600;
        color: #2d3436;
        font-size: 0.9rem;
    }

    /* Related Products Section */
    .related-products {
        max-width: 1400px;
        margin: 4rem auto 0;
        padding: 0 2rem;
    }

    .related-title {
        font-size: 2.5rem;
        font-weight: 800;
        text-align: center;
        margin-bottom: 3rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .related-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
    }

    .related-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .related-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0,0,0,0.15);
    }

    .related-image {
        height: 200px;
        overflow: hidden;
        background: #f8f9fa;
    }

    .related-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .related-card:hover .related-image img {
        transform: scale(1.05);
    }

    .related-info {
        padding: 1.5rem;
    }

    .related-name {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #2d3436;
    }

    .related-name a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .related-name a:hover {
        color: #667eea;
    }

    .related-price {
        font-size: 1.2rem;
        font-weight: 800;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .related-actions {
        display: flex;
        gap: 0.75rem;
    }

    .related-btn {
        flex: 1;
        padding: 0.75rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .related-btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .related-btn-outline:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
    }

    .related-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 2px solid transparent;
    }

    .related-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    /* Professional Reviews Section */
    .reviews-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        padding: 5rem 0;
        margin-top: 4rem;
        position: relative;
        overflow: hidden;
    }

    .reviews-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%);
    }

    .reviews-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .reviews-header {
        text-align: center;
        margin-bottom: 4rem;
    }

    .reviews-title {
        font-size: 2.8rem;
        font-weight: 800;
        color: #2d3436;
        margin-bottom: 1rem;
        position: relative;
        display: inline-block;
    }

    .reviews-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .reviews-subtitle {
        font-size: 1.1rem;
        color: #636e72;
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    .reviews-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        margin-bottom: 3rem;
        flex-wrap: wrap;
    }

    .review-stat {
        text-align: center;
        padding: 1.5rem;
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        min-width: 150px;
        transition: all 0.3s ease;
    }

    .review-stat:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.12);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: block;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #636e72;
        font-weight: 600;
        margin-top: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .rating-breakdown {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 3rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .rating-breakdown-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .rating-bars {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .rating-bar {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .rating-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 80px;
        font-weight: 600;
        color: #2d3436;
    }

    .rating-progress {
        flex: 1;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }

    .rating-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 1s ease;
    }

    .rating-count {
        min-width: 40px;
        text-align: right;
        font-weight: 600;
        color: #636e72;
        font-size: 0.9rem;
    }

    /* Professional Review Form */
    .review-form-container {
        background: white;
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 4rem;
        box-shadow: 0 20px 60px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }

    .review-form-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .review-form-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .review-form-title {
        font-size: 2rem;
        font-weight: 800;
        color: #2d3436;
        margin-bottom: 0.5rem;
    }

    .review-form-subtitle {
        color: #636e72;
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 2rem;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-label {
        display: block;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .form-input, .form-textarea {
        width: 100%;
        padding: 1rem 1.25rem;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #fafbfc;
        font-family: inherit;
    }

    .form-input:focus, .form-textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        background: white;
        transform: translateY(-2px);
    }

    .form-textarea {
        resize: vertical;
        min-height: 120px;
        line-height: 1.6;
    }

    .file-upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: #fafbfc;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .file-upload-area:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
        transform: translateY(-2px);
    }

    .file-upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .file-upload-icon {
        font-size: 2.5rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .file-upload-text {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 0.5rem;
    }

    .file-upload-hint {
        font-size: 0.9rem;
        color: #636e72;
    }

    .form-file {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .uploaded-files {
        margin-top: 1rem;
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .uploaded-file {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #e9ecef;
    }

    .uploaded-file img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .remove-file {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Professional Star Rating */
    .rating-container {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .rating-label-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 1rem;
    }

    .star-rating {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        font-size: 2.5rem;
        color: #e9ecef;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .star-rating label:hover {
        transform: scale(1.2);
        color: #ffd43b;
    }

    .star-rating label:hover ~ label {
        color: #ffd43b;
        transform: scale(1.1);
    }

    .star-rating input:checked ~ label {
        color: #ffd43b;
        animation: starGlow 0.3s ease;
    }

    @keyframes starGlow {
        0% { transform: scale(1); }
        50% { transform: scale(1.3); }
        100% { transform: scale(1.1); }
    }

    .rating-description {
        font-size: 0.9rem;
        color: #636e72;
        font-weight: 500;
        min-height: 20px;
        transition: all 0.3s ease;
    }

    .submit-review-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.25rem 3rem;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin: 2rem auto 0;
        min-width: 200px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .submit-review-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s ease;
    }

    .submit-review-btn:hover::before {
        left: 100%;
    }

    .submit-review-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }

    .submit-review-btn:active {
        transform: translateY(-1px);
    }

    /* Professional User Notices */
    .user-notice {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 4rem;
        text-align: center;
        font-weight: 600;
        box-shadow: 0 15px 40px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }

    .user-notice::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }

    .user-review-notice::before {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .banned-notice::before {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    }

    .login-prompt::before {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .notice-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .user-review-notice .notice-icon {
        color: #28a745;
    }

    .banned-notice .notice-icon {
        color: #dc3545;
    }

    .login-prompt .notice-icon {
        color: #667eea;
    }

    .notice-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #2d3436;
    }

    .notice-text {
        color: #636e72;
        font-weight: 500;
        line-height: 1.6;
    }

    .login-prompt a {
        color: #667eea;
        text-decoration: none;
        font-weight: 700;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .login-prompt a:hover {
        border-bottom-color: #667eea;
        transform: translateY(-1px);
    }

    /* Responsive Design */
    @media (max-width: 992px) {
        .product-layout {
            grid-template-columns: 1fr;
            gap: 3rem;
        }

        .product-title {
            font-size: 2rem;
        }

        .product-price {
            font-size: 1.8rem;
        }

        .purchase-section {
            position: static;
        }
    }

    @media (max-width: 768px) {
        .breadcrumb-content {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .product-title {
            font-size: 1.8rem;
        }

        .product-price {
            font-size: 1.5rem;
        }

        .product-main-image {
            height: 300px;
        }

        .related-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .related-title {
            font-size: 2rem;
        }
    }

    @media (max-width: 480px) {
        .product-layout,
        .related-products {
            padding: 0 1rem;
        }

        .product-title {
            font-size: 1.5rem;
        }

        .related-grid {
            grid-template-columns: 1fr;
        }

        .related-actions {
            flex-direction: column;
        }
    }

    /* Reviews Section */
    .reviews-section {
        background: #f8f9fa;
        padding: 4rem 0;
        margin-top: 4rem;
    }

    .reviews-title {
        font-size: 2.5rem;
        font-weight: 800;
        text-align: center;
        margin-bottom: 3rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .review-count {
        background: #667eea;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 1rem;
        font-weight: 600;
    }

    /* Review Form */
    .review-form-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 3rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(0,0,0,0.05);
    }

    .review-form-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 0.5rem;
    }

    .form-input, .form-textarea {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-input:focus, .form-textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-file {
        width: 100%;
        padding: 0.75rem;
        border: 2px dashed #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .form-file:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }

    .file-help {
        font-size: 0.8rem;
        color: #636e72;
        margin-top: 0.5rem;
    }

    /* Star Rating */
    .star-rating {
        display: flex;
        flex-direction: row-reverse;
        gap: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        font-size: 2rem;
        color: #e9ecef;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .star-rating label:hover,
    .star-rating label:hover ~ label,
    .star-rating input:checked ~ label {
        color: #ffd43b;
        transform: scale(1.1);
    }

    .submit-review-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1.1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .submit-review-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }

    /* User Notices */
    .user-review-notice, .banned-notice, .login-prompt {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 3rem;
        text-align: center;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .user-review-notice {
        color: #28a745;
        border: 2px solid #28a745;
    }

    .banned-notice {
        color: #dc3545;
        border: 2px solid #dc3545;
    }

    .login-prompt {
        color: #667eea;
        border: 2px solid #667eea;
    }

    .login-prompt a {
        color: #667eea;
        text-decoration: none;
        font-weight: 700;
    }

    .login-prompt a:hover {
        text-decoration: underline;
    }

    /* Professional Reviews List */
    .reviews-list {
        display: grid;
        gap: 2rem;
        margin-top: 3rem;
    }

    .review-item {
        background: white;
        border-radius: 25px;
        padding: 2.5rem;
        box-shadow: 0 15px 40px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .review-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        transition: all 0.3s ease;
    }

    .review-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 60px rgba(0,0,0,0.12);
    }

    .review-item:hover::before {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .featured-review {
        border: 2px solid #ffd43b;
        background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
        position: relative;
    }

    .featured-review::before {
        background: linear-gradient(135deg, #ffd43b 0%, #ffb347 100%);
    }

    .featured-review::after {
        content: 'FEATURED';
        position: absolute;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #ffd43b 0%, #ffb347 100%);
        color: #2d3436;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 700;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(255, 212, 59, 0.3);
    }

    .review-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #f1f3f4;
    }

    .reviewer-info {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .reviewer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid #f1f3f4;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .reviewer-avatar:hover {
        transform: scale(1.05);
        border-color: #667eea;
    }

    .reviewer-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .avatar-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.5rem;
    }

    .reviewer-details {
        flex: 1;
    }

    .reviewer-name {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .review-date {
        color: #636e72;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .review-rating-section {
        text-align: right;
    }

    .review-stars {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    .review-rating-text {
        font-size: 0.85rem;
        color: #636e72;
        font-weight: 600;
    }

    .review-content {
        margin-bottom: 2rem;
    }

    .review-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 1rem;
        line-height: 1.3;
    }

    .review-comment {
        color: #4a5568;
        line-height: 1.7;
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .review-helpful {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f1f3f4;
    }

    .helpful-text {
        color: #636e72;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .helpful-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .helpful-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #e2e8f0;
        background: white;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .helpful-btn:hover {
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-1px);
    }

    /* Review Images */
    .review-images {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .review-image {
        width: 100px;
        height: 100px;
        border-radius: 10px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid #e9ecef;
    }

    .review-image:hover {
        transform: scale(1.05);
        border-color: #667eea;
    }

    .review-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Review Actions */
    .review-actions {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .feature-btn {
        background: #ffd43b;
        color: #2d3436;
    }

    .feature-btn:hover {
        background: #ffb347;
        transform: translateY(-2px);
    }

    .unfeature-btn {
        background: #e9ecef;
        color: #636e72;
    }

    .unfeature-btn:hover {
        background: #dee2e6;
        transform: translateY(-2px);
    }

    .delete-btn {
        background: #dc3545;
        color: white;
    }

    .delete-btn:hover {
        background: #c82333;
        transform: translateY(-2px);
    }

    /* User Badges */
    .badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .badge-admin {
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        color: white;
    }

    .badge-verified {
        background: #28a745;
        color: white;
    }

    .badge-premium {
        background: linear-gradient(135deg, #ffd43b 0%, #ffb347 100%);
        color: #2d3436;
    }

    .badge-vip {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
    }

    .badge-customer {
        background: #6c757d;
        color: white;
    }

    /* No Reviews */
    .no-reviews {
        text-align: center;
        padding: 3rem;
        color: #636e72;
    }

    .no-reviews i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #e9ecef;
    }

    .no-reviews h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    /* Image Modal */
    .image-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.9);
        cursor: pointer;
    }

    .modal-content {
        position: relative;
        margin: auto;
        padding: 20px;
        width: 90%;
        max-width: 800px;
        top: 50%;
        transform: translateY(-50%);
    }

    .close-modal {
        position: absolute;
        top: 15px;
        right: 35px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
    }

    .close-modal:hover {
        color: #bbb;
    }

    #modalImage {
        width: 100%;
        height: auto;
        border-radius: 10px;
    }

    /* Error Messages */
    .error-message {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 0.25rem;
    }

    /* Pagination */
    .reviews-pagination {
        margin-top: 3rem;
        display: flex;
        justify-content: center;
    }

    /* Professional Mobile Responsive */
    @media (max-width: 1024px) {
        .reviews-stats {
            gap: 2rem;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .reviews-section {
            padding: 3rem 0;
        }

        .reviews-container {
            padding: 0 1rem;
        }

        .reviews-title {
            font-size: 2.2rem;
        }

        .reviews-stats {
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .review-stat {
            min-width: 200px;
        }

        .rating-breakdown {
            padding: 1.5rem;
        }

        .review-form-container {
            padding: 2rem;
        }

        .star-rating label {
            font-size: 2rem;
        }

        .review-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .review-rating-section {
            text-align: left;
        }

        .reviewer-info {
            gap: 1rem;
        }

        .reviewer-avatar {
            width: 50px;
            height: 50px;
        }

        .review-images {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        }

        .review-image {
            width: 80px;
            height: 80px;
        }

        .review-actions {
            flex-direction: column;
        }

        .action-btn {
            justify-content: center;
        }

        .helpful-buttons {
            flex-direction: column;
            gap: 0.5rem;
        }

        .helpful-btn {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .reviews-title {
            font-size: 1.8rem;
        }

        .review-form-container {
            padding: 1.5rem;
        }

        .review-item {
            padding: 1.5rem;
        }

        .star-rating label {
            font-size: 1.8rem;
        }

        .submit-review-btn {
            padding: 1rem 2rem;
            font-size: 1rem;
        }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Error Messages */
    .error-message {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: rgba(220, 53, 69, 0.1);
        border-radius: 8px;
        border-left: 3px solid #dc3545;
    }

    /* Success Messages */
    .success-message {
        color: #28a745;
        font-size: 0.9rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: rgba(40, 167, 69, 0.1);
        border-radius: 8px;
        border-left: 3px solid #28a745;
    }

    /* AI Recommendations Section */
    .recommendations-section, .personalized-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 5rem 0;
        margin-top: 4rem;
        position: relative;
        overflow: hidden;
    }

    .recommendations-section::before, .personalized-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="circuit" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><path d="M5,10 L15,10 M10,5 L10,15" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23circuit)"/></svg>') repeat;
        opacity: 0.3;
    }

    .recommendations-title, .personalized-title {
        font-size: 3rem;
        font-weight: 800;
        text-align: center;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .recommendations-subtitle, .personalized-subtitle {
        font-size: 1.2rem;
        text-align: center;
        opacity: 0.9;
        margin-bottom: 3rem;
        position: relative;
        z-index: 2;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .recommendations-grid, .personalized-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        position: relative;
        z-index: 2;
    }

    .recommendation-card, .personalized-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        position: relative;
    }

    .recommendation-card:hover, .personalized-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 60px rgba(0,0,0,0.3);
    }

    .rec-image, .pers-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .rec-image img, .pers-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .recommendation-card:hover .rec-image img,
    .personalized-card:hover .pers-image img {
        transform: scale(1.1);
    }

    .rec-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: rgba(102, 126, 234, 0.9);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 700;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .rec-info, .pers-info {
        padding: 1.5rem;
        color: #2d3436;
    }

    .rec-name, .pers-name {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.75rem;
        line-height: 1.3;
    }

    .rec-name a, .pers-name a {
        color: #2d3436;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .rec-name a:hover, .pers-name a:hover {
        color: #667eea;
    }

    .rec-rating, .pers-rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .rec-review-count, .pers-review-count {
        color: #636e72;
        font-size: 0.9rem;
    }

    .rec-price, .pers-price {
        font-size: 1.5rem;
        font-weight: 800;
        color: #667eea;
        margin-bottom: 0.75rem;
    }

    .rec-reason {
        color: #636e72;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        font-style: italic;
    }

    .rec-confidence {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .confidence-bar {
        flex: 1;
        height: 6px;
        background: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
    }

    .confidence-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3px;
        transition: width 1s ease;
    }

    .confidence-text {
        font-size: 0.8rem;
        font-weight: 600;
        color: #667eea;
    }

    .pers-category {
        color: #667eea;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    /* Responsive Design for Recommendations */
    @media (max-width: 768px) {
        .recommendations-title, .personalized-title {
            font-size: 2rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .recommendations-grid, .personalized-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .recommendations-section, .personalized-section {
            padding: 3rem 0;
        }
    }
</style>

<script>
// Professional Review System JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Star Rating Functionality
    const starRating = document.querySelector('.star-rating');
    const ratingDescription = document.getElementById('ratingDescription');

    if (starRating && ratingDescription) {
        const ratingTexts = {
            1: 'Poor - Not satisfied with this product',
            2: 'Fair - Below expectations',
            3: 'Good - Meets expectations',
            4: 'Very Good - Exceeds expectations',
            5: 'Excellent - Outstanding product!'
        };

        starRating.addEventListener('change', function(e) {
            if (e.target.type === 'radio') {
                const rating = e.target.value;
                ratingDescription.textContent = ratingTexts[rating];
                ratingDescription.style.color = rating >= 4 ? '#28a745' : rating >= 3 ? '#ffc107' : '#dc3545';
            }
        });

        // Hover effects for stars
        const starLabels = starRating.querySelectorAll('label');
        starLabels.forEach((label, index) => {
            label.addEventListener('mouseenter', function() {
                const rating = 5 - index;
                ratingDescription.textContent = ratingTexts[rating];
                ratingDescription.style.color = rating >= 4 ? '#28a745' : rating >= 3 ? '#ffc107' : '#dc3545';
            });
        });

        starRating.addEventListener('mouseleave', function() {
            const checkedInput = starRating.querySelector('input:checked');
            if (checkedInput) {
                const rating = checkedInput.value;
                ratingDescription.textContent = ratingTexts[rating];
                ratingDescription.style.color = rating >= 4 ? '#28a745' : rating >= 3 ? '#ffc107' : '#dc3545';
            } else {
                ratingDescription.textContent = 'Select a rating';
                ratingDescription.style.color = '#636e72';
            }
        });
    }

    // File Upload Functionality
    const fileInput = document.getElementById('images');
    const uploadedFiles = document.getElementById('uploadedFiles');
    const fileUploadArea = document.querySelector('.file-upload-area');

    if (fileInput && uploadedFiles && fileUploadArea) {
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        // Drag and drop functionality
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        function handleFiles(files) {
            uploadedFiles.innerHTML = '';
            Array.from(files).slice(0, 5).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const fileDiv = document.createElement('div');
                        fileDiv.className = 'uploaded-file';
                        fileDiv.innerHTML = `
                            <img src="${e.target.result}" alt="Upload ${index + 1}">
                            <button type="button" class="remove-file" onclick="removeFile(this, ${index})">×</button>
                        `;
                        uploadedFiles.appendChild(fileDiv);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    }

    // Animate rating bars on scroll
    const ratingBars = document.querySelectorAll('.rating-fill');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.transition = 'width 1.5s ease';
            }
        });
    });

    ratingBars.forEach(bar => observer.observe(bar));

    // Smooth scroll to reviews
    const reviewLinks = document.querySelectorAll('a[href*="#reviews"]');
    reviewLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector('.reviews-section').scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
});

// Remove uploaded file
function removeFile(button, index) {
    const fileInput = document.getElementById('images');
    const dt = new DataTransfer();
    const files = fileInput.files;

    for (let i = 0; i < files.length; i++) {
        if (i !== index) {
            dt.items.add(files[i]);
        }
    }

    fileInput.files = dt.files;
    button.parentElement.remove();
}

// Image Modal Functions
function openImageModal(imageSrc) {
    document.getElementById('imageModal').style.display = 'block';
    document.getElementById('modalImage').src = imageSrc;
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    document.getElementById('imageModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside the image
document.addEventListener('click', function(e) {
    const modal = document.getElementById('imageModal');
    if (e.target === modal) {
        closeImageModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});

// Mark review as helpful
function markHelpful(reviewId, isHelpful) {
    const button = event.target.closest('.helpful-btn');
    const icon = button.querySelector('i');
    const text = button.querySelector('span') || button.childNodes[button.childNodes.length - 1];

    // Visual feedback
    button.style.background = isHelpful ? '#28a745' : '#dc3545';
    button.style.color = 'white';
    button.style.borderColor = isHelpful ? '#28a745' : '#dc3545';

    // Disable both buttons
    const allButtons = button.parentElement.querySelectorAll('.helpful-btn');
    allButtons.forEach(btn => {
        btn.disabled = true;
        btn.style.opacity = '0.6';
    });

    // Re-enable and highlight the clicked button
    button.disabled = false;
    button.style.opacity = '1';

    // Update text
    if (typeof text.textContent !== 'undefined') {
        text.textContent = isHelpful ? ' Thank you!' : ' Thanks for feedback';
    }

    // Here you could add AJAX call to save the feedback
    console.log(`Review ${reviewId} marked as ${isHelpful ? 'helpful' : 'not helpful'}`);
}

// Wishlist Toggle Function
function toggleWishlist(productId) {
    const button = document.getElementById('wishlistBtn');
    const icon = button.querySelector('i');
    const text = button.querySelector('.wishlist-text');

    // Add loading state
    button.disabled = true;
    button.style.opacity = '0.7';
    icon.className = 'fas fa-spinner fa-spin';
    text.textContent = 'Processing...';

    fetch('{{ route("wishlist.toggle") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button state
            if (data.in_wishlist) {
                button.classList.add('in-wishlist');
                icon.className = 'fas fa-heart';
                text.textContent = 'In Wishlist';
            } else {
                button.classList.remove('in-wishlist');
                icon.className = 'fas fa-heart';
                text.textContent = 'Add to Wishlist';
            }

            // Show success message
            showToast(data.message, 'success');

            // Update wishlist count in header if it exists
            updateWishlistCount(data.wishlist_count);
        } else {
            if (data.redirect) {
                window.location.href = data.redirect;
                return;
            }
            showToast(data.message, 'error');

            // Reset button state
            icon.className = 'fas fa-heart';
            text.textContent = button.classList.contains('in-wishlist') ? 'In Wishlist' : 'Add to Wishlist';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');

        // Reset button state
        icon.className = 'fas fa-heart';
        text.textContent = button.classList.contains('in-wishlist') ? 'In Wishlist' : 'Add to Wishlist';
    })
    .finally(() => {
        // Remove loading state
        button.disabled = false;
        button.style.opacity = '1';
    });
}

// Toast notification function
function showToast(message, type = 'success') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        existingToast.remove();
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#dc3545'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        z-index: 1000;
        transform: translateX(400px);
        transition: all 0.3s ease;
        font-weight: 600;
        max-width: 300px;
    `;

    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Hide toast
    setTimeout(() => {
        toast.style.transform = 'translateX(400px)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

function updateWishlistCount(count) {
    // Update wishlist count in header/navigation if it exists
    const wishlistCounters = document.querySelectorAll('.wishlist-count');
    wishlistCounters.forEach(counter => {
        counter.textContent = count;
        if (count > 0) {
            counter.style.display = 'inline';
        } else {
            counter.style.display = 'none';
        }
    });
}

// AI Recommendations System
document.addEventListener('DOMContentLoaded', function() {
    // Record product view
    recordProductView();

    // Load recommendations
    loadRecommendations();

    // Track view duration
    trackViewDuration();
});

function recordProductView() {
    const productId = {{ $product->id }};

    fetch('{{ route("recommendations.record-view") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            view_duration: 0
        })
    })
    .catch(error => {
        console.log('View tracking error:', error);
    });
}

function loadRecommendations() {
    const productId = {{ $product->id }};

    // Load product recommendations
    fetch(`{{ route("recommendations.product", ":productId") }}`.replace(':productId', productId))
        .then(response => response.json())
        .then(data => {
            if (data.success && data.recommendations.length > 0) {
                displayRecommendations(data.recommendations);
            }
        })
        .catch(error => {
            console.log('Recommendations loading error:', error);
        });

    // Load personalized recommendations if user is logged in
    @auth
    fetch('{{ route("recommendations.personalized") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.recommendations.length > 0) {
                displayPersonalizedRecommendations(data.recommendations);
            }
        })
        .catch(error => {
            console.log('Personalized recommendations error:', error);
        });
    @endauth
}

function displayRecommendations(recommendations) {
    const container = document.getElementById('ai-recommendations');
    if (!container) return;

    let html = `
        <div class="recommendations-section">
            <div class="container">
                <h2 class="recommendations-title">
                    <i class="fas fa-robot"></i>
                    AI Recommended for You
                </h2>
                <p class="recommendations-subtitle">
                    Smart recommendations based on customer behavior and product analysis
                </p>
                <div class="recommendations-grid">
    `;

    recommendations.forEach(product => {
        html += `
            <div class="recommendation-card" data-type="${product.type}">
                <div class="rec-image">
                    <a href="/products/${product.slug}">
                        <img src="${product.image || '/images/no-image.png'}" alt="${product.name}">
                    </a>
                    <div class="rec-badge">${getRecommendationBadge(product.type)}</div>
                </div>
                <div class="rec-info">
                    <h3 class="rec-name">
                        <a href="/products/${product.slug}">${product.name}</a>
                    </h3>
                    <div class="rec-rating">
                        ${generateStars(product.rating)}
                        <span class="rec-review-count">(${product.review_count})</span>
                    </div>
                    <div class="rec-price">$${parseFloat(product.price).toFixed(2)}</div>
                    <div class="rec-reason">${product.reason}</div>
                    <div class="rec-confidence">
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${product.confidence * 100}%"></div>
                        </div>
                        <span class="confidence-text">${Math.round(product.confidence * 100)}% match</span>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function displayPersonalizedRecommendations(recommendations) {
    const container = document.getElementById('personalized-recommendations');
    if (!container) return;

    let html = `
        <div class="personalized-section">
            <div class="container">
                <h2 class="personalized-title">
                    <i class="fas fa-user-cog"></i>
                    Personalized Just for You
                </h2>
                <p class="personalized-subtitle">
                    Based on your browsing history, purchases, and preferences
                </p>
                <div class="personalized-grid">
    `;

    recommendations.slice(0, 6).forEach(product => {
        html += `
            <div class="personalized-card">
                <div class="pers-image">
                    <a href="/products/${product.slug}">
                        <img src="${product.image || '/images/no-image.png'}" alt="${product.name}">
                    </a>
                </div>
                <div class="pers-info">
                    <div class="pers-category">${product.category}</div>
                    <h3 class="pers-name">
                        <a href="/products/${product.slug}">${product.name}</a>
                    </h3>
                    <div class="pers-rating">
                        ${generateStars(product.rating)}
                        <span class="pers-review-count">(${product.review_count})</span>
                    </div>
                    <div class="pers-price">$${parseFloat(product.price).toFixed(2)}</div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function getRecommendationBadge(type) {
    const badges = {
        'collaborative': '<i class="fas fa-users"></i> Popular',
        'content_based': '<i class="fas fa-tags"></i> Similar',
        'trending': '<i class="fas fa-fire"></i> Trending',
        'similar': '<i class="fas fa-clone"></i> Related'
    };
    return badges[type] || '<i class="fas fa-star"></i> Recommended';
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star" style="color: #ffd43b;"></i>';
        } else {
            stars += '<i class="far fa-star" style="color: #e9ecef;"></i>';
        }
    }
    return stars;
}

let viewStartTime = Date.now();
function trackViewDuration() {
    // Track when user leaves the page
    window.addEventListener('beforeunload', function() {
        const viewDuration = Math.round((Date.now() - viewStartTime) / 1000);

        if (viewDuration > 5) { // Only track if viewed for more than 5 seconds
            navigator.sendBeacon('{{ route("recommendations.record-view") }}', JSON.stringify({
                product_id: {{ $product->id }},
                view_duration: viewDuration,
                _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }));
        }
    });
}
</script>
@endsection

@section('content')
<!-- Breadcrumb Navigation -->
<div class="breadcrumb-nav">
    <div class="breadcrumb-content">
        <a href="{{ route('products.index') }}" class="back-btn">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
        <span class="breadcrumb-divider">•</span>
        <span class="breadcrumb-current">{{ $product->name }}</span>
    </div>
</div>

<!-- Product Layout -->
<div class="product-layout">
    <!-- Product Image Section -->
    <div class="product-image-section">
        <div class="product-image-container">
            @if($product->image)
                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="product-main-image">
            @else
                <img src="https://via.placeholder.com/600x500?text=No+Image&bg=f8f9fa&color=636e72" alt="No Image" class="product-main-image">
            @endif

            <!-- Stock Badge -->
            @if($product->stock > 0)
                @if($product->stock < 10)
                    <div class="stock-badge stock-low">Low Stock</div>
                @else
                    <div class="stock-badge stock-in">In Stock</div>
                @endif
            @else
                <div class="stock-badge stock-out">Out of Stock</div>
            @endif

            <!-- Product Badge -->
            <div class="product-badge">Featured</div>
        </div>
    </div>

    <!-- Product Info Section -->
    <div class="product-info-section">
        <!-- Product Header -->
        <div class="product-header">
            <a href="{{ route('products.category', $product->category_id) }}" class="product-category">
                <i class="fas fa-tag"></i> {{ $product->category->name }}
            </a>

            <h1 class="product-title">{{ $product->name }}</h1>

            <div class="product-price">${{ number_format($product->price, 2) }}</div>

            <!-- Product Rating -->
            <div class="product-rating">
                <div class="stars">{!! $product->stars_html !!}</div>
                <span class="rating-text">
                    @if($product->review_count > 0)
                        ({{ number_format($product->average_rating, 1) }} out of 5 stars from {{ $product->review_count }} {{ Str::plural('review', $product->review_count) }})
                    @else
                        (No reviews yet)
                    @endif
                </span>
            </div>
        </div>

        <!-- Product Description -->
        <div class="product-description">
            <h3 class="description-title">
                <i class="fas fa-info-circle"></i> Product Description
            </h3>
            <p class="description-text">{{ $product->description ?: 'No description available for this product.' }}</p>
        </div>

        <!-- Purchase Section -->
        <div class="purchase-section">
            @auth
                @if($product->stock > 0)
                    <form action="{{ route('cart.add') }}" method="POST">
                        @csrf
                        <input type="hidden" name="product_id" value="{{ $product->id }}">

                        <div class="quantity-selector">
                            <label for="quantity" class="quantity-label">Quantity:</label>
                            <input type="number" name="quantity" id="quantity" value="1" min="1" max="{{ $product->stock }}" class="quantity-input">
                        </div>

                        <button type="submit" class="purchase-btn purchase-btn-primary">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                    </form>
                @else
                    <button class="purchase-btn purchase-btn-disabled" disabled>
                        <i class="fas fa-times"></i> Out of Stock
                    </button>
                @endif
            @else
                <a href="{{ route('login') }}" class="purchase-btn purchase-btn-outline">
                    <i class="fas fa-sign-in-alt"></i> Login to Purchase
                </a>
            @endauth

            <!-- Wishlist Button -->
            @auth
                <button class="wishlist-btn {{ $product->isInWishlist() ? 'in-wishlist' : '' }}"
                        onclick="toggleWishlist({{ $product->id }})"
                        id="wishlistBtn">
                    <i class="fas fa-heart"></i>
                    <span class="wishlist-text">
                        {{ $product->isInWishlist() ? 'In Wishlist' : 'Add to Wishlist' }}
                    </span>
                </button>
            @else
                <a href="{{ route('login') }}" class="wishlist-btn">
                    <i class="fas fa-heart"></i>
                    <span class="wishlist-text">Add to Wishlist</span>
                </a>
            @endauth

            <!-- Product Features -->
            <div class="product-features">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <span class="feature-text">Free Shipping</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <span class="feature-text">30-Day Returns</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <span class="feature-text">Secure Payment</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <span class="feature-text">24/7 Support</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Related Products -->
@if($relatedProducts->count() > 0)
    <div class="related-products">
        <h2 class="related-title">You Might Also Like</h2>

        <div class="related-grid">
            @foreach($relatedProducts as $relatedProduct)
                <div class="related-card">
                    <div class="related-image">
                        @if($relatedProduct->image)
                            <img src="{{ asset('storage/' . $relatedProduct->image) }}" alt="{{ $relatedProduct->name }}">
                        @else
                            <img src="https://via.placeholder.com/300x200?text=No+Image&bg=f8f9fa&color=636e72" alt="No Image">
                        @endif
                    </div>
                    <div class="related-info">
                        <h3 class="related-name">
                            <a href="{{ route('products.show', $relatedProduct->slug) }}">{{ $relatedProduct->name }}</a>
                        </h3>
                        <div class="related-price">${{ number_format($relatedProduct->price, 2) }}</div>
                        <div class="related-actions">
                            <a href="{{ route('products.show', $relatedProduct->slug) }}" class="related-btn related-btn-outline">
                                <i class="fas fa-eye"></i> View
                            </a>

                            @auth
                                @if($relatedProduct->stock > 0)
                                    <form action="{{ route('cart.add') }}" method="POST" style="flex: 1;">
                                        @csrf
                                        <input type="hidden" name="product_id" value="{{ $relatedProduct->id }}">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="related-btn related-btn-primary" style="width: 100%;">
                                            <i class="fas fa-cart-plus"></i> Add to Cart
                                        </button>
                                    </form>
                                @else
                                    <button class="related-btn" style="background: #e74c3c; color: white; cursor: not-allowed;" disabled>
                                        <i class="fas fa-times"></i> Out of Stock
                                    </button>
                                @endif
                            @else
                                <a href="{{ route('login') }}" class="related-btn related-btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endif

<!-- Professional Reviews Section -->
<div class="reviews-section">
    <div class="reviews-container">
        <!-- Reviews Header -->
        <div class="reviews-header">
            <h2 class="reviews-title">Customer Reviews & Ratings</h2>
            <p class="reviews-subtitle">
                See what our customers are saying about this product. Your feedback helps others make informed decisions.
            </p>

            <!-- Reviews Statistics -->
            <div class="reviews-stats">
                <div class="review-stat">
                    <span class="stat-number">{{ number_format($product->average_rating, 1) }}</span>
                    <span class="stat-label">Average Rating</span>
                </div>
                <div class="review-stat">
                    <span class="stat-number">{{ $product->review_count }}</span>
                    <span class="stat-label">Total Reviews</span>
                </div>
                <div class="review-stat">
                    <span class="stat-number">{{ $reviews->where('rating', 5)->count() }}</span>
                    <span class="stat-label">5-Star Reviews</span>
                </div>
            </div>

            <!-- Rating Breakdown -->
            @if($product->review_count > 0)
                <div class="rating-breakdown">
                    <h3 class="rating-breakdown-title">Rating Breakdown</h3>
                    <div class="rating-bars">
                        @for($i = 5; $i >= 1; $i--)
                            @php
                                $count = $reviews->where('rating', $i)->count();
                                $percentage = $product->review_count > 0 ? ($count / $product->review_count) * 100 : 0;
                            @endphp
                            <div class="rating-bar">
                                <div class="rating-label">
                                    @for($j = 1; $j <= $i; $j++)
                                        <i class="fas fa-star" style="color: #ffd43b;"></i>
                                    @endfor
                                </div>
                                <div class="rating-progress">
                                    <div class="rating-fill" style="width: {{ $percentage }}%"></div>
                                </div>
                                <div class="rating-count">{{ $count }}</div>
                            </div>
                        @endfor
                    </div>
                </div>
            @endif
        </div>

        <!-- Review Form -->
        @auth
            @if(!$userReview && !auth()->user()->isBanned())
                <div class="review-form-container">
                    <div class="review-form-header">
                        <h3 class="review-form-title">Share Your Experience</h3>
                        <p class="review-form-subtitle">Help other customers by sharing your honest review of this product</p>
                    </div>

                    <form action="{{ route('reviews.store', $product) }}" method="POST" enctype="multipart/form-data" class="review-form">
                        @csrf

                        <!-- Rating Section -->
                        <div class="form-group full-width">
                            <label class="form-label">How would you rate this product? *</label>
                            <div class="rating-container">
                                <div class="rating-label-text">Click the stars to rate</div>
                                <div class="star-rating">
                                    @for($i = 5; $i >= 1; $i--)
                                        <input type="radio" name="rating" value="{{ $i }}" id="star{{ $i }}" {{ old('rating') == $i ? 'checked' : '' }}>
                                        <label for="star{{ $i }}" class="star">★</label>
                                    @endfor
                                </div>
                                <div class="rating-description" id="ratingDescription">Select a rating</div>
                            </div>
                            @error('rating')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-grid">
                            <!-- Title -->
                            <div class="form-group">
                                <label for="title" class="form-label">Review Title</label>
                                <input type="text" name="title" id="title" class="form-input" placeholder="Summarize your experience..." value="{{ old('title') }}">
                                @error('title')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Images -->
                            <div class="form-group">
                                <label class="form-label">Add Photos</label>
                                <div class="file-upload-area" onclick="document.getElementById('images').click()">
                                    <div class="file-upload-icon">
                                        <i class="fas fa-camera"></i>
                                    </div>
                                    <div class="file-upload-text">Click to upload photos</div>
                                    <div class="file-upload-hint">Up to 5 images, max 2MB each</div>
                                    <input type="file" name="images[]" id="images" class="form-file" multiple accept="image/*">
                                </div>
                                <div class="uploaded-files" id="uploadedFiles"></div>
                                @error('images.*')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Comment -->
                        <div class="form-group full-width">
                            <label for="comment" class="form-label">Your Detailed Review *</label>
                            <textarea name="comment" id="comment" class="form-textarea" placeholder="Share your detailed experience with this product. What did you like? What could be improved? How does it compare to similar products?">{{ old('comment') }}</textarea>
                            @error('comment')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <button type="submit" class="submit-review-btn">
                            <i class="fas fa-paper-plane"></i>
                            <span>Submit Review</span>
                        </button>
                    </form>
                </div>
            @elseif($userReview)
                <div class="user-notice user-review-notice">
                    <i class="fas fa-check-circle notice-icon"></i>
                    <div class="notice-title">Review Submitted</div>
                    <div class="notice-text">Thank you for your feedback! You have already reviewed this product.</div>
                </div>
            @elseif(auth()->user()->isBanned())
                <div class="user-notice banned-notice">
                    <i class="fas fa-ban notice-icon"></i>
                    <div class="notice-title">Account Restricted</div>
                    <div class="notice-text">You are currently unable to post reviews. Reason: {{ auth()->user()->ban_reason }}</div>
                </div>
            @endif
        @else
            <div class="user-notice login-prompt">
                <i class="fas fa-sign-in-alt notice-icon"></i>
                <div class="notice-title">Join the Conversation</div>
                <div class="notice-text">
                    <a href="{{ route('login') }}">Sign in</a> to share your experience and help other customers make informed decisions.
                </div>
            </div>
        @endauth

        <!-- Professional Reviews List -->
        @if($reviews->count() > 0)
            <div class="reviews-list">
                @foreach($reviews as $review)
                    <div class="review-item {{ $review->is_featured ? 'featured-review' : '' }}">
                        <!-- Review Header -->
                        <div class="review-header">
                            <div class="reviewer-info">
                                <div class="reviewer-avatar">
                                    @if($review->user->profile_picture)
                                        <img src="{{ asset('storage/' . $review->user->profile_picture) }}" alt="{{ $review->user->name }}">
                                    @else
                                        <div class="avatar-placeholder">{{ substr($review->user->name, 0, 1) }}</div>
                                    @endif
                                </div>
                                <div class="reviewer-details">
                                    <div class="reviewer-name">
                                        {{ $review->user->name }}
                                        {!! $review->user->badge_display !!}
                                    </div>
                                    <div class="review-date">
                                        {{ $review->created_at->format('F j, Y') }} • {{ $review->created_at->diffForHumans() }}
                                    </div>
                                </div>
                            </div>

                            <div class="review-rating-section">
                                <div class="review-stars">{!! $review->stars_html !!}</div>
                                <div class="review-rating-text">{{ $review->rating }}/5 Stars</div>
                            </div>
                        </div>

                        <!-- Review Content -->
                        <div class="review-content">
                            @if($review->title)
                                <h4 class="review-title">{{ $review->title }}</h4>
                            @endif
                            <p class="review-comment">{{ $review->comment }}</p>

                            <!-- Review Images -->
                            @if($review->images->count() > 0)
                                <div class="review-images">
                                    @foreach($review->images as $image)
                                        <div class="review-image">
                                            <img src="{{ $image->url }}" alt="Review image" onclick="openImageModal('{{ $image->url }}')">
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            <!-- Review Helpful Section -->
                            <div class="review-helpful">
                                <span class="helpful-text">Was this review helpful?</span>
                                <div class="helpful-buttons">
                                    <button class="helpful-btn" onclick="markHelpful({{ $review->id }}, true)">
                                        <i class="fas fa-thumbs-up"></i> Yes
                                    </button>
                                    <button class="helpful-btn" onclick="markHelpful({{ $review->id }}, false)">
                                        <i class="fas fa-thumbs-down"></i> No
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Review Actions -->
                        @auth
                            @if(auth()->user()->is_admin || auth()->id() === $review->user_id)
                                <div class="review-actions">
                                    @if(auth()->user()->is_admin)
                                        @if(!$review->is_featured)
                                            <form action="{{ route('admin.reviews.toggle-featured', $review) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="action-btn feature-btn">
                                                    <i class="fas fa-star"></i> Feature
                                                </button>
                                            </form>
                                        @else
                                            <form action="{{ route('admin.reviews.toggle-featured', $review) }}" method="POST" style="display: inline;">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="action-btn unfeature-btn">
                                                    <i class="far fa-star"></i> Unfeature
                                                </button>
                                            </form>
                                        @endif
                                    @endif

                                    <form action="{{ route('reviews.destroy', $review) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this review?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="action-btn delete-btn">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            @endif
                        @endauth
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="reviews-pagination">
                {{ $reviews->links() }}
            </div>
        @else
            <div class="no-reviews">
                <i class="fas fa-comment-slash"></i>
                <h3>No reviews yet</h3>
                <p>Be the first to review this product!</p>
            </div>
        @endif
    </div>
</div>

<!-- AI Recommendations Container -->
<div id="ai-recommendations"></div>

<!-- Personalized Recommendations Container -->
<div id="personalized-recommendations"></div>

<!-- Image Modal -->
<div id="imageModal" class="image-modal" onclick="closeImageModal()">
    <div class="modal-content">
        <span class="close-modal">&times;</span>
        <img id="modalImage" src="" alt="Review image">
    </div>
</div>

@endsection
