<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained('chat_conversations')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who sent the message
            $table->enum('sender_type', ['customer', 'agent', 'system']); // Message sender type
            $table->text('message'); // Message content
            $table->json('attachments')->nullable(); // File attachments
            $table->enum('message_type', ['text', 'image', 'file', 'system'])->default('text');
            $table->boolean('is_read')->default(false); // Read status
            $table->timestamp('read_at')->nullable(); // When message was read
            $table->boolean('is_edited')->default(false); // If message was edited
            $table->timestamp('edited_at')->nullable(); // When message was edited
            $table->json('metadata')->nullable(); // Additional data (typing indicators, etc.)
            $table->timestamps();

            // Indexes for performance
            $table->index(['conversation_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['sender_type', 'created_at']);
            $table->index('is_read');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
