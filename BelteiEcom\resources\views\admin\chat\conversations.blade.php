@extends('admin.layouts.app')

@section('title', 'All Conversations - Live Chat')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.chat.index') }}">Live Chat</a>
                    </li>
                    <li class="breadcrumb-item active">All Conversations</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-list text-primary"></i>
                All Conversations
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.chat.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ route('admin.chat.agent') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-headset"></i> Agent Dashboard
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-search"></i>
                        Search & Filters
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.chat.conversations') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" name="q" id="search" class="form-control" 
                                           placeholder="Customer name, email, or message..." 
                                           value="{{ $query }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="waiting" {{ ($filters['status'] ?? '') === 'waiting' ? 'selected' : '' }}>Waiting</option>
                                        <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="closed" {{ ($filters['status'] ?? '') === 'closed' ? 'selected' : '' }}>Closed</option>
                                        <option value="transferred" {{ ($filters['status'] ?? '') === 'transferred' ? 'selected' : '' }}>Transferred</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="agent_id">Agent</label>
                                    <select name="agent_id" id="agent_id" class="form-control">
                                        <option value="">All Agents</option>
                                        @foreach($agents as $agent)
                                            <option value="{{ $agent->id }}" {{ ($filters['agent_id'] ?? '') == $agent->id ? 'selected' : '' }}>
                                                {{ $agent->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="priority">Priority</label>
                                    <select name="priority" id="priority" class="form-control">
                                        <option value="">All Priorities</option>
                                        <option value="low" {{ ($filters['priority'] ?? '') === 'low' ? 'selected' : '' }}>Low</option>
                                        <option value="normal" {{ ($filters['priority'] ?? '') === 'normal' ? 'selected' : '' }}>Normal</option>
                                        <option value="high" {{ ($filters['priority'] ?? '') === 'high' ? 'selected' : '' }}>High</option>
                                        <option value="urgent" {{ ($filters['priority'] ?? '') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <a href="{{ route('admin.chat.conversations') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from">Date From</label>
                                    <input type="date" name="date_from" id="date_from" class="form-control" 
                                           value="{{ $filters['date_from'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to">Date To</label>
                                    <input type="date" name="date_to" id="date_to" class="form-control" 
                                           value="{{ $filters['date_to'] ?? '' }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversations List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comments"></i>
                        Conversations ({{ $conversations->total() }})
                    </h6>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-info btn-sm" onclick="refreshConversations()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if($conversations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Customer</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                        <th>Agent</th>
                                        <th>Started</th>
                                        <th>Last Activity</th>
                                        <th>Duration</th>
                                        <th>Messages</th>
                                        <th>Rating</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($conversations as $conversation)
                                        <tr class="{{ $conversation->status === 'waiting' ? 'table-warning' : '' }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm mr-2">
                                                        <div class="bg-{{ $conversation->status === 'waiting' ? 'warning' : 'primary' }} rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <strong>{{ $conversation->customer_name }}</strong>
                                                        @if($conversation->customer)
                                                            <span class="badge badge-success badge-sm">Registered</span>
                                                        @else
                                                            <span class="badge badge-secondary badge-sm">Guest</span>
                                                        @endif
                                                        <br>
                                                        <small class="text-muted">{{ $conversation->customer_email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>{{ $conversation->subject ?: 'General Inquiry' }}</strong>
                                                @if($conversation->latestMessage)
                                                    <br><small class="text-muted">{{ Str::limit($conversation->latestMessage->message, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $conversation->status_color }}">
                                                    {{ ucfirst($conversation->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $conversation->priority_color }}">
                                                    {{ ucfirst($conversation->priority) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($conversation->agent)
                                                    <strong>{{ $conversation->agent->name }}</strong>
                                                @else
                                                    <span class="text-muted">Unassigned</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $conversation->created_at->format('M j, Y H:i') }}</small>
                                            </td>
                                            <td>
                                                <small>{{ $conversation->last_activity_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                @if($conversation->getDuration())
                                                    <small>{{ $conversation->getDuration() }} min</small>
                                                @else
                                                    <small class="text-muted">-</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-info">{{ $conversation->messages->count() }}</span>
                                            </td>
                                            <td>
                                                @if($conversation->rating)
                                                    <div class="text-warning">
                                                        @for($i = 1; $i <= 5; $i++)
                                                            <i class="fas fa-star{{ $i <= $conversation->rating ? '' : '-o' }}"></i>
                                                        @endfor
                                                    </div>
                                                    <small>({{ $conversation->rating }}/5)</small>
                                                @else
                                                    <small class="text-muted">No rating</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.chat.conversation', $conversation->id) }}" 
                                                       class="btn btn-info btn-sm" title="View Conversation">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    
                                                    @if($conversation->status === 'waiting')
                                                        <button class="btn btn-success btn-sm" 
                                                                onclick="takeConversation({{ $conversation->id }})"
                                                                title="Take Conversation">
                                                            <i class="fas fa-hand-paper"></i>
                                                        </button>
                                                    @endif
                                                    
                                                    @if($conversation->status === 'active')
                                                        <button class="btn btn-warning btn-sm" 
                                                                onclick="transferConversation({{ $conversation->id }})"
                                                                title="Transfer Conversation">
                                                            <i class="fas fa-exchange-alt"></i>
                                                        </button>
                                                        <button class="btn btn-secondary btn-sm" 
                                                                onclick="closeConversation({{ $conversation->id }})"
                                                                title="Close Conversation">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $conversations->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No conversations found</h5>
                            <p class="text-muted">
                                @if($query || array_filter($filters))
                                    No conversations match your search criteria. Try adjusting your filters.
                                @else
                                    No chat conversations have been started yet.
                                @endif
                            </p>
                            @if($query || array_filter($filters))
                                <a href="{{ route('admin.chat.conversations') }}" class="btn btn-primary">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Transfer Conversation</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="transferForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="transfer_agent_id">Transfer to Agent</label>
                        <select name="agent_id" id="transfer_agent_id" class="form-control" required>
                            <option value="">Select Agent</option>
                            @foreach($agents as $agent)
                                <option value="{{ $agent->id }}">{{ $agent->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="transfer_reason">Reason (Optional)</label>
                        <textarea name="reason" id="transfer_reason" class="form-control" rows="3" 
                                  placeholder="Reason for transfer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Transfer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.badge-sm {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}

.table th {
    position: sticky;
    top: 0;
    background: #f8f9fc;
    z-index: 10;
}

.avatar-sm {
    flex-shrink: 0;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>

<script>
let transferConversationId = null;

function refreshConversations() {
    location.reload();
}

function takeConversation(conversationId) {
    if (confirm('Take this conversation?')) {
        fetch(`/admin/chat/conversation/${conversationId}/take`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect;
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while taking the conversation.');
        });
    }
}

function transferConversation(conversationId) {
    transferConversationId = conversationId;
    $('#transferModal').modal('show');
}

function closeConversation(conversationId) {
    if (confirm('Are you sure you want to close this conversation?')) {
        const reason = prompt('Reason for closing (optional):');
        
        fetch(`/admin/chat/conversation/${conversationId}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while closing the conversation.');
        });
    }
}

// Handle transfer form submission
document.getElementById('transferForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        agent_id: formData.get('agent_id'),
        reason: formData.get('reason')
    };
    
    fetch(`/admin/chat/conversation/${transferConversationId}/transfer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#transferModal').modal('hide');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while transferring the conversation.');
    });
});

// Auto-submit form when date changes
document.getElementById('date_from').addEventListener('change', function() {
    if (this.value && document.getElementById('date_to').value) {
        this.form.submit();
    }
});

document.getElementById('date_to').addEventListener('change', function() {
    if (this.value && document.getElementById('date_from').value) {
        this.form.submit();
    }
});
</script>
@endsection
