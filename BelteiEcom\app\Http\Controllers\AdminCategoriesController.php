<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AdminCategoriesController extends Controller
{
    /**
     * Display a listing of the categories.
     */
    public function index()
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $categories = Category::orderBy('name')->paginate(10);
        $categoriesPaginator = $categories; // Keep a reference to the paginator

        return view('admin.categories.index', compact('categories', 'categoriesPaginator'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        // Get all categories for the parent category dropdown
        $categories = Category::all();

        return view('admin.categories.create', compact('categories'));
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
        ]);

        // Generate a unique slug
        $slug = Str::slug($request->name);
        $count = 1;

        // Check if the slug already exists
        while (Category::where('slug', $slug)->exists()) {
            $slug = Str::slug($request->name) . '-' . $count;
            $count++;
        }

        Category::create([
            'name' => $request->name,
            'description' => $request->description,
            'slug' => $slug,
            'parent_id' => $request->parent_id,
        ]);

        return redirect()->route('admin.categories.index')->with('success', 'Category created successfully.');
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $category = Category::findOrFail($id);

        // Get all categories for the parent category dropdown
        // Exclude the current category to prevent circular references
        $categories = Category::where('id', '!=', $id)->get();

        return view('admin.categories.edit', compact('category', 'categories'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, $id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $category = Category::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
        ]);

        // Generate a unique slug if the name has changed
        $slug = $category->slug;
        if ($category->name !== $request->name) {
            $slug = Str::slug($request->name);
            $count = 1;

            // Check if the slug already exists
            while (Category::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                $slug = Str::slug($request->name) . '-' . $count;
                $count++;
            }
        }

        $category->update([
            'name' => $request->name,
            'description' => $request->description,
            'slug' => $slug,
            'parent_id' => $request->parent_id,
        ]);

        return redirect()->route('admin.categories.index')->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $category = Category::findOrFail($id);

        // Check if category has products
        if ($category->products()->count() > 0) {
            return redirect()->route('admin.categories.index')->with('error', 'Cannot delete category with associated products.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')->with('success', 'Category deleted successfully.');
    }
}
