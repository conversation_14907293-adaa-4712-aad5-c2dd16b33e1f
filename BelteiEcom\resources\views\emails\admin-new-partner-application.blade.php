<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Partner Application</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .alert-badge {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
            border-left: 4px solid #ffc107;
        }

        .content {
            padding: 2rem;
        }

        .company-info {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 2rem;
            margin: 1.5rem 0;
            border-left: 5px solid #1976d2;
        }

        .company-info h3 {
            color: #1976d2;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }

        .info-grid {
            display: grid;
            gap: 1rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .info-label {
            font-weight: 600;
            color: #666;
            min-width: 120px;
        }

        .info-value {
            color: #333;
            font-weight: 500;
            flex: 1;
            text-align: right;
        }

        .business-description {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .business-description h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }

        .business-description p {
            color: #333;
            line-height: 1.6;
            font-style: italic;
        }

        .action-section {
            background: #d1ecf1;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }

        .action-section h3 {
            color: #0c5460;
            margin-bottom: 1.5rem;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            margin: 0.5rem;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            text-decoration: none;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .priority-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .priority-notice h4 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .priority-notice p {
            color: #856404;
            font-size: 0.9rem;
        }

        .footer {
            background: #f8f9fa;
            padding: 2rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .content {
                padding: 1rem;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .info-row {
                flex-direction: column;
            }
            
            .info-value {
                text-align: left;
                margin-top: 0.25rem;
            }
            
            .btn {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🚨 New Partner Application</h1>
            <p>Requires Admin Review</p>
        </div>

        <!-- Alert Badge -->
        <div class="alert-badge">
            ⏰ Application submitted {{ $partner->created_at->diffForHumans() }}
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Company Information -->
            <div class="company-info">
                <h3>📋 Application Details</h3>
                <div class="info-grid">
                    <div class="info-row">
                        <span class="info-label">Company:</span>
                        <span class="info-value">{{ $partner->company_name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Contact Person:</span>
                        <span class="info-value">{{ $partner->contact_name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        <span class="info-value">{{ $partner->email }}</span>
                    </div>
                    @if($partner->phone)
                    <div class="info-row">
                        <span class="info-label">Phone:</span>
                        <span class="info-value">{{ $partner->phone }}</span>
                    </div>
                    @endif
                    @if($partner->website)
                    <div class="info-row">
                        <span class="info-label">Website:</span>
                        <span class="info-value">
                            <a href="{{ $partner->website }}" target="_blank" style="color: #1976d2;">
                                {{ $partner->website }}
                            </a>
                        </span>
                    </div>
                    @endif
                    <div class="info-row">
                        <span class="info-label">Applied:</span>
                        <span class="info-value">{{ $partner->created_at->format('F j, Y \a\t g:i A') }}</span>
                    </div>
                </div>
            </div>

            <!-- Business Description -->
            <div class="business-description">
                <h3>💼 Business Description</h3>
                <p>"{{ $partner->business_description }}"</p>
            </div>

            <!-- Additional Information -->
            @if(isset($partner->settings['expected_volume']) || isset($partner->settings['integration_timeline']))
            <div class="company-info">
                <h3>📊 Additional Information</h3>
                <div class="info-grid">
                    @if(isset($partner->settings['expected_volume']))
                    <div class="info-row">
                        <span class="info-label">Expected Volume:</span>
                        <span class="info-value">{{ $partner->settings['expected_volume'] }}</span>
                    </div>
                    @endif
                    @if(isset($partner->settings['integration_timeline']))
                    <div class="info-row">
                        <span class="info-label">Timeline:</span>
                        <span class="info-value">{{ $partner->settings['integration_timeline'] }}</span>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-number">{{ App\Models\ApiPartner::where('status', 'pending')->count() }}</div>
                    <div class="stat-label">Pending Applications</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ App\Models\ApiPartner::where('status', 'approved')->count() }}</div>
                    <div class="stat-label">Active Partners</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ App\Models\ApiOrder::count() }}</div>
                    <div class="stat-label">Total API Orders</div>
                </div>
            </div>

            <!-- Priority Notice -->
            <div class="priority-notice">
                <h4>⚡ Action Required</h4>
                <p>This application is waiting for your review. Please approve or reject within 1-2 business days.</p>
            </div>

            <!-- Action Section -->
            <div class="action-section">
                <h3>🎯 Review Application</h3>
                <p>Click below to review and make a decision on this partner application.</p>
                
                <a href="{{ url('/admin/partners') }}" class="btn btn-primary">
                    🔍 Review in Admin Dashboard
                </a>
                
                <a href="{{ url('/admin/partners/' . $partner->id) }}" class="btn btn-secondary">
                    📋 View Full Details
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>BelteiEcom Admin Notification</strong></p>
            <p>This email was sent to notify you of a new partner application requiring review.</p>
            <p style="margin-top: 1rem; font-size: 0.8rem;">
                Sent to: {{ config('mail.from.address') }} • {{ now()->format('F j, Y \a\t g:i A') }}
            </p>
        </div>
    </div>
</body>
</html>
