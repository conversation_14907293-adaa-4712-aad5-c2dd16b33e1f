@extends('admin.layouts.app')

@section('styles')
<style>


    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    .dashboard-card.sales {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .dashboard-card.orders {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .dashboard-card.products {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .dashboard-card.customers {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .dashboard-card.today-sales {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .dashboard-card.today-orders {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    .dashboard-card.month-sales {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }

    .dashboard-card.low-stock {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.1);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .dashboard-card:hover::before {
        opacity: 1;
    }

    .card-icon {
        font-size: 3rem;
        opacity: 0.3;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    .counter {
        font-size: 2.5rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .card-title {
        color: rgba(255,255,255,0.9);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 10px;
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 100%;
        min-height: 400px;
    }

    .chart-container:hover {
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .chart-wrapper {
        position: relative;
        height: 300px;
        width: 100%;
        padding: 20px;
    }

    .chart-wrapper canvas {
        max-width: 100% !important;
        height: auto !important;
    }

    /* Responsive chart adjustments */
    @media (max-width: 768px) {
        .chart-container {
            min-height: 350px;
            margin-bottom: 2rem;
        }

        .chart-wrapper {
            height: 250px;
            padding: 15px;
        }
    }

    @media (max-width: 576px) {
        .chart-container {
            min-height: 300px;
        }

        .chart-wrapper {
            height: 200px;
            padding: 10px;
        }
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 1;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .quick-action-btn {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .quick-action-btn:hover {
        background: rgba(255,255,255,0.3);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .animate-counter {
        animation: countUp 2s ease-out;
    }

    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.6s ease-out forwards;
        opacity: 0;
    }

    @keyframes fadeIn {
        to { opacity: 1; }
    }

    .slide-up {
        animation: slideUp 0.8s ease-out forwards;
        transform: translateY(30px);
        opacity: 0;
    }

    @keyframes slideUp {
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">🚀 Admin Dashboard</h1>
                <p class="mb-0 opacity-75">Welcome back! Here's what's happening with your store today.</p>
                <div class="quick-actions">
                    <a href="{{ route('admin.products.create') }}" class="quick-action-btn">
                        <i class="fas fa-plus"></i> Add Product
                    </a>
                    <a href="{{ route('admin.orders.index') }}" class="quick-action-btn">
                        <i class="fas fa-list"></i> View Orders
                    </a>
                    <a href="{{ route('admin.categories.index') }}" class="quick-action-btn">
                        <i class="fas fa-tags"></i> Manage Categories
                    </a>
                </div>
            </div>
            <div class="text-right">
                <div class="h4 mb-1">{{ date('l') }}</div>
                <div class="opacity-75">{{ date('F j, Y') }}</div>
            </div>
        </div>
    </div>

    <!-- Stats Cards Row 1 -->
    <div class="row slide-up" style="animation-delay: 0.2s;">
        <!-- Total Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card sales h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Total Sales</div>
                    <div class="counter animate-counter" data-target="{{ $totalSales }}">${{ number_format($totalSales, 2) }}</div>
                    <i class="fas fa-chart-line card-icon"></i>
                </div>
            </div>
        </div>

        <!-- Total Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card orders h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Total Orders</div>
                    <div class="counter animate-counter" data-target="{{ $totalOrders }}">{{ number_format($totalOrders) }}</div>
                    <i class="fas fa-shopping-cart card-icon"></i>
                </div>
            </div>
        </div>

        <!-- Total Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card products h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Total Products</div>
                    <div class="counter animate-counter" data-target="{{ $totalProducts }}">{{ number_format($totalProducts) }}</div>
                    <i class="fas fa-box card-icon"></i>
                </div>
            </div>
        </div>

        <!-- Total Customers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card customers h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Total Customers</div>
                    <div class="counter animate-counter" data-target="{{ $totalCustomers }}">{{ number_format($totalCustomers) }}</div>
                    <i class="fas fa-users card-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards Row 2 -->
    <div class="row slide-up" style="animation-delay: 0.4s;">
        <!-- Today Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card today-sales h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Today's Sales</div>
                    <div class="counter animate-counter" data-target="{{ $todaySales }}">${{ number_format($todaySales, 2) }}</div>
                    <i class="fas fa-calendar-day card-icon"></i>
                </div>
            </div>
        </div>

        <!-- Today Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card today-orders h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Today's Orders</div>
                    <div class="counter animate-counter" data-target="{{ $todayOrders }}">{{ number_format($todayOrders) }}</div>
                    <i class="fas fa-clock card-icon"></i>
                </div>
            </div>
        </div>

        <!-- Month Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card month-sales h-100">
                <div class="card-body position-relative">
                    <div class="card-title">This Month</div>
                    <div class="counter animate-counter" data-target="{{ $monthSales }}">${{ number_format($monthSales, 2) }}</div>
                    <i class="fas fa-calendar-alt card-icon"></i>
                </div>
            </div>
        </div>

        <!-- Low Stock Alert Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card low-stock h-100">
                <div class="card-body position-relative">
                    <div class="card-title">Low Stock Alert</div>
                    <div class="counter animate-counter" data-target="{{ $lowStockProducts }}">{{ number_format($lowStockProducts) }}</div>
                    <i class="fas fa-exclamation-triangle card-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row slide-up" style="animation-delay: 0.6s;">
        <!-- Sales Trend Chart -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">📈 Sales Trend (Last 7 Days)</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw" style="color: rgba(255,255,255,0.7);"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="#">Export Data</a>
                            <a class="dropdown-item" href="#">View Details</a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="chart-wrapper">
                        <canvas id="salesTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Status Distribution -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">🎯 Order Status Distribution</h6>
                </div>
                <div class="card-body p-0">
                    <div class="chart-wrapper">
                        <canvas id="orderStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Charts Row -->
    <div class="row slide-up" style="animation-delay: 0.8s;">
        <!-- Monthly Revenue Chart -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">💰 Monthly Revenue (Last 6 Months)</h6>
                </div>
                <div class="card-body p-0">
                    <div class="chart-wrapper">
                        <canvas id="monthlyRevenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Performance Chart -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">🏷️ Category Performance</h6>
                </div>
                <div class="card-body p-0">
                    <div class="chart-wrapper">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Growth Chart -->
    <div class="row slide-up" style="animation-delay: 1.0s;">
        <div class="col-12 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">👥 Customer Growth (Last 12 Months)</h6>
                </div>
                <div class="card-body p-0">
                    <div class="chart-wrapper">
                        <canvas id="customerGrowthChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables Row -->
    <div class="row slide-up" style="animation-delay: 1.2s;">
        <!-- Recent Orders -->
        <div class="col-lg-6 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">📋 Recent Orders</h6>
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-sm btn-light">View All</a>
                </div>
                <div class="card-body">
                    @if($recentOrders->isEmpty())
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-gray-300 mb-3"></i>
                            <p class="text-gray-500">No orders yet.</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Order #</th>
                                        <th>Customer</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentOrders as $order)
                                        <tr>
                                            <td>
                                                <a href="{{ route('admin.orders.show', $order->id) }}" class="font-weight-bold" style="color: #667eea;">#{{ $order->id }}</a>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm rounded-circle d-flex align-items-center justify-content-center text-white mr-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                                        {{ substr($order->user->name, 0, 1) }}
                                                    </div>
                                                    {{ $order->user->name }}
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{
                                                    $order->status == 'new' ? 'info' :
                                                    ($order->status == 'processing' ? 'warning' :
                                                    ($order->status == 'shipped' ? 'primary' :
                                                    ($order->status == 'delivered' ? 'success' : 'danger')))
                                                }} badge-pill">
                                                    {{ ucfirst($order->status) }}
                                                </span>
                                            </td>
                                            <td class="font-weight-bold">${{ number_format($order->total_amount, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Popular Products -->
        <div class="col-lg-6 mb-4">
            <div class="card chart-container">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border: none;">
                    <h6 class="m-0 font-weight-bold" style="color: white;">🔥 Popular Products</h6>
                    <a href="{{ route('admin.products.index') }}" class="btn btn-sm btn-light">View All</a>
                </div>
                <div class="card-body">
                    @if($popularProducts->isEmpty())
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-gray-300 mb-3"></i>
                            <p class="text-gray-500">No products ordered yet.</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Sold</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($popularProducts as $product)
                                        <tr>
                                            <td>
                                                <a href="{{ route('admin.products.edit', $product->id) }}" class="font-weight-bold" style="color: #43e97b;">{{ $product->name }}</a>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">{{ $product->category->name }}</span>
                                            </td>
                                            <td class="font-weight-bold">${{ number_format($product->price, 2) }}</td>
                                            <td>
                                                <span class="badge badge-success">{{ $product->total_quantity }} sold</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Trend Chart
    const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
    const salesTrendChart = new Chart(salesTrendCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($salesTrend['labels']) !!},
            datasets: [{
                label: 'Sales ($)',
                data: {!! json_encode($salesTrend['data']) !!},
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(102, 126, 234)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: 'rgb(102, 126, 234)'
                }
            }
        }
    });

    // Order Status Chart
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    const orderStatusChart = new Chart(orderStatusCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode($orderStatusData['labels']) !!},
            datasets: [{
                data: {!! json_encode($orderStatusData['data']) !!},
                backgroundColor: {!! json_encode($orderStatusData['colors']) !!},
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Monthly Revenue Chart
    const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    const monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
        type: 'bar',
        data: {
            labels: {!! json_encode($monthlyRevenue['labels']) !!},
            datasets: [{
                label: 'Revenue ($)',
                data: {!! json_encode($monthlyRevenue['data']) !!},
                backgroundColor: 'rgba(79, 172, 254, 0.8)',
                borderColor: 'rgb(79, 172, 254)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Category Performance Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    const categoryChart = new Chart(categoryCtx, {
        type: 'pie',
        data: {
            labels: {!! json_encode($categoryPerformance['labels']) !!},
            datasets: [{
                data: {!! json_encode($categoryPerformance['data']) !!},
                backgroundColor: {!! json_encode($categoryPerformance['colors']) !!},
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Customer Growth Chart
    const customerGrowthCtx = document.getElementById('customerGrowthChart').getContext('2d');
    const customerGrowthChart = new Chart(customerGrowthCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($customerGrowth['labels']) !!},
            datasets: [{
                label: 'New Customers',
                data: {!! json_encode($customerGrowth['data']) !!},
                borderColor: 'rgb(67, 233, 123)',
                backgroundColor: 'rgba(67, 233, 123, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(67, 233, 123)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Store chart instances for responsive handling
    const chartInstances = [salesTrendChart, orderStatusChart, monthlyRevenueChart, categoryChart, customerGrowthChart];

    // Handle window resize for better responsiveness
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            chartInstances.forEach(chart => {
                if (chart && typeof chart.resize === 'function') {
                    chart.resize();
                }
            });
        }, 100);
    });

    // Add some extra styling
    const style = document.createElement('style');
    style.textContent = `
        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 12px;
            font-weight: bold;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.08);
        }

        .badge-pill {
            border-radius: 50px;
        }

        /* Additional responsive chart styles */
        .chart-wrapper {
            overflow: hidden;
        }

        /* Fix table text colors for better readability */
        .table {
            color: #2c3e50 !important;
        }

        .table td, .table th {
            color: #2c3e50 !important;
            font-weight: 500;
        }

        .table thead th {
            color: white !important;
            font-weight: 600;
        }

        .table tbody tr td a {
            color: #667eea !important;
            font-weight: 600;
            text-decoration: none;
        }

        .table tbody tr td a:hover {
            color: #5a6fd8 !important;
            text-decoration: underline;
        }

        /* Fix text colors in cards */
        .card-body {
            color: #2c3e50 !important;
        }

        .text-gray-500 {
            color: #6c757d !important;
        }

        .text-gray-300 {
            color: #adb5bd !important;
        }

        /* Ensure proper contrast for all text elements */
        .font-weight-bold {
            color: #2c3e50 !important;
        }

        @media (max-width: 992px) {
            .chart-container {
                margin-bottom: 2rem;
            }
        }
    `;
    document.head.appendChild(style);

    // Loading Screen Management
    function hideLoadingScreen() {
        const loadingScreen = document.getElementById('dashboardLoading');
        const dashboardContent = document.getElementById('dashboardContent');

        // Hide loading screen
        loadingScreen.classList.add('hidden');

        // Show dashboard content with animation
        setTimeout(() => {
            dashboardContent.classList.add('loaded');
        }, 300);

        // Remove loading screen from DOM after animation
        setTimeout(() => {
            loadingScreen.remove();
        }, 800);
    }

    // Simulate loading time and wait for charts to be ready
    let chartsLoaded = 0;
    const totalCharts = 5;

    function checkChartsReady() {
        chartsLoaded++;
        if (chartsLoaded >= totalCharts) {
            // Add a minimum loading time for better UX
            setTimeout(hideLoadingScreen, 800);
        }
    }

    // Mark charts as loaded (call this after each chart is created)
    checkChartsReady(); // Sales Trend
    checkChartsReady(); // Order Status
    checkChartsReady(); // Monthly Revenue
    checkChartsReady(); // Category Performance
    checkChartsReady(); // Customer Growth
});
</script>
@endsection
