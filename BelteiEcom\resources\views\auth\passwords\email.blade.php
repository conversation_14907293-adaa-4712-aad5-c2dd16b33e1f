<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Laravel') }} - Reset Password</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

<style>
    /* Override main layout for reset page */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        min-height: 100vh;
    }

    main {
        display: none !important;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    /* Reset Page Specific Styles */
    .reset-page {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        z-index: 998;
    }

    /* Header Override */
    .reset-page .header {
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
        backdrop-filter: blur(20px);
        padding: 1rem 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1000;
    }

    .reset-page .header-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .reset-page .logo {
        font-size: 1.8rem;
        font-weight: bold;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .reset-page .logo i {
        font-size: 1.5rem;
        color: #ffd700;
        -webkit-text-fill-color: #ffd700;
        background: none;
    }

    .nav-wrapper {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    nav ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 2rem;
    }

    nav a {
        color: #333;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
    }

    .auth-buttons {
        display: flex;
        gap: 1rem;
    }

    .auth-buttons .btn {
        padding: 0.5rem 1.5rem;
        border-radius: 2rem;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .btn-login {
        color: #667eea;
        border-color: #667eea;
    }

    .btn-register {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    /* Main Content */
    .main-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        position: relative;
        overflow: hidden;
    }

    .main-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateY(0px) rotate(0deg); }
        100% { transform: translateY(-20px) rotate(360deg); }
    }

    /* Floating Shapes */
    .floating-shapes {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        overflow: hidden;
    }

    .shape {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: floatShapes 15s infinite ease-in-out;
    }

    .shape-1 {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .shape-2 {
        width: 60px;
        height: 60px;
        top: 20%;
        right: 20%;
        animation-delay: 2s;
    }

    .shape-3 {
        width: 40px;
        height: 40px;
        bottom: 30%;
        left: 15%;
        animation-delay: 4s;
    }

    .shape-4 {
        width: 100px;
        height: 100px;
        bottom: 10%;
        right: 10%;
        animation-delay: 6s;
    }

    .shape-5 {
        width: 50px;
        height: 50px;
        top: 50%;
        left: 50%;
        animation-delay: 8s;
    }

    @keyframes floatShapes {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(180deg); }
    }

    /* Reset Container */
    .reset-container {
        position: relative;
        z-index: 10;
        width: 100%;
        max-width: 450px;
    }

    .reset-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .reset-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
    }

    /* Reset Header */
    .reset-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .logo-icon {
        font-size: 2.5rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .reset-header h2 {
        font-size: 2rem;
        font-weight: 800;
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
    }

    .reset-header h3 {
        color: #333;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .reset-header p {
        color: #666;
        font-size: 1rem;
    }

    /* Success Message */
    .success-message {
        background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
        box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
    }

    .success-message i {
        font-size: 1.2rem;
    }

    /* Form Styles */
    .reset-form {
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #333;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .form-group input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e0e0e0;
        border-radius: 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-group input:focus {
        outline: none;
        border-color: #ff6b6b;
        box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        transform: translateY(-1px);
    }

    .error-message {
        color: #e74c3c;
        font-size: 0.8rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Submit Button */
    .reset-submit-btn {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
        color: white;
        border: none;
        border-radius: 15px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        margin-bottom: 2rem;
    }

    .reset-submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    }

    /* Back to Login Link */
    .back-to-login {
        text-align: center;
        color: #666;
        font-size: 0.9rem;
    }

    .back-to-login a {
        color: #ff6b6b;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .back-to-login a:hover {
        color: #e55555;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .header-container {
            flex-direction: column;
            gap: 1rem;
        }

        .nav-wrapper {
            flex-direction: column;
            gap: 1rem;
        }

        .main-content {
            padding: 1rem;
        }

        .reset-card {
            padding: 2rem;
        }

        .reset-header h3 {
            font-size: 1.3rem;
        }

        .reset-header p {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 480px) {
        .reset-card {
            padding: 1.5rem;
        }

        .reset-header h3 {
            font-size: 1.2rem;
        }
    }
</style>
</head>
<body>
<div class="reset-page">
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <a href="{{ route('home') }}">
                    <i class="fas fa-store"></i>
                    {{ config('app.name', 'BelteiEcom') }}
                </a>
            </div>

            <div class="nav-wrapper">
                <nav>
                    <ul>
                        <li><a href="{{ route('home') }}"><i class="fas fa-home"></i> Home</a></li>
                        <li><a href="{{ route('products.index') }}"><i class="fas fa-box"></i> Products</a></li>
                    </ul>
                </nav>

                <div class="auth-buttons">
                    <a href="{{ route('login') }}" class="btn btn-login">Login</a>
                    <a href="{{ route('register') }}" class="btn btn-register">Register</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Floating Shapes -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>

        <!-- Reset Container -->
        <div class="reset-container">
            <div class="reset-card">
                <!-- Reset Header -->
                <div class="reset-header">
                    <div class="logo-container">
                        <i class="fas fa-key logo-icon"></i>
                    </div>
                    <h3>Reset Password</h3>
                    <p>Enter your email address and we'll send you a link to reset your password.</p>
                </div>

                <!-- Success Message -->
                @if (session('status'))
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        {{ session('status') }}
                    </div>
                @endif

                <!-- Reset Form -->
                <form method="POST" action="{{ route('password.email') }}" class="reset-form">
                    @csrf

                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input id="email" type="email" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus placeholder="Enter your email address">
                        @error('email')
                            <span class="error-message">{{ $message }}</span>
                        @enderror
                    </div>

                    <button type="submit" class="reset-submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        Send Password Reset Link
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="back-to-login">
                    Remember your password? <a href="{{ route('login') }}">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
