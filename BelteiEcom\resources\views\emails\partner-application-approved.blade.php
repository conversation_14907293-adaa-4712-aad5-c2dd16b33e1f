<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to BelteiEcom Partners!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .celebration {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }

        .celebration h2 {
            color: #856404;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .celebration .emoji {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .content {
            padding: 2rem;
        }

        .welcome-message {
            text-align: center;
            margin-bottom: 2rem;
        }

        .welcome-message h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .credentials-section {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #1976d2;
        }

        .credentials-section h3 {
            color: #1976d2;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
            text-align: center;
        }

        .credential-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .credential-label {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .credential-value {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            word-break: break-all;
            font-size: 0.9rem;
        }

        .partner-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .partner-info h3 {
            color: #667eea;
            margin-bottom: 1rem;
        }

        .info-grid {
            display: grid;
            gap: 0.75rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #666;
        }

        .info-value {
            color: #333;
            font-weight: 500;
        }

        .tier-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .tier-basic { background: #d4edda; color: #155724; }
        .tier-premium { background: #cce5ff; color: #004085; }
        .tier-enterprise { background: #f8d7da; color: #721c24; }

        .next-steps {
            background: #d1ecf1;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .next-steps h3 {
            color: #0c5460;
            margin-bottom: 1rem;
        }

        .next-steps ol {
            padding-left: 1.5rem;
        }

        .next-steps li {
            margin-bottom: 0.75rem;
            color: #0c5460;
        }

        .cta-section {
            text-align: center;
            margin: 2rem 0;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 15px;
            font-weight: 600;
            margin: 0.5rem;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .support-section {
            background: #d4edda;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }

        .support-section h3 {
            color: #155724;
            margin-bottom: 1rem;
        }

        .support-section p {
            color: #155724;
            margin-bottom: 0.5rem;
        }

        .support-section a {
            color: #155724;
            font-weight: 600;
            text-decoration: none;
        }

        .footer {
            background: #f8f9fa;
            padding: 2rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .security-notice h4 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .security-notice p {
            color: #856404;
            font-size: 0.9rem;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .content {
                padding: 1rem;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .info-row {
                flex-direction: column;
            }
            
            .btn {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Welcome to BelteiEcom Partners!</h1>
            <p>Your application has been approved</p>
        </div>

        <!-- Celebration Section -->
        <div class="celebration">
            <div class="emoji">🚀✨🎊</div>
            <h2>Congratulations!</h2>
            <p>You're now an official BelteiEcom partner</p>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Welcome Message -->
            <div class="welcome-message">
                <h3>Hello {{ $partner->contact_name }},</h3>
                <p>We're thrilled to welcome <strong>{{ $partner->company_name }}</strong> to the BelteiEcom Partner Program! Your application has been approved and you're ready to start selling our products.</p>
            </div>

            <!-- API Credentials -->
            <div class="credentials-section">
                <h3>🔑 Your API Credentials</h3>
                
                <div class="credential-item">
                    <div class="credential-label">API Key</div>
                    <div class="credential-value">{{ $partner->api_key }}</div>
                </div>
                
                <div class="credential-item">
                    <div class="credential-label">Base URL</div>
                    <div class="credential-value">{{ url('/api/v1/partner') }}</div>
                </div>

                <div class="security-notice">
                    <h4>🔒 Security Notice</h4>
                    <p>Keep your API key secure and never share it publicly. Use it only in server-side applications.</p>
                </div>
            </div>

            <!-- Partner Information -->
            <div class="partner-info">
                <h3>📊 Your Partner Details</h3>
                <div class="info-grid">
                    <div class="info-row">
                        <span class="info-label">Partner Tier:</span>
                        <span class="info-value">
                            <span class="tier-badge tier-{{ $partner->tier }}">{{ ucfirst($partner->tier) }}</span>
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Commission Rate:</span>
                        <span class="info-value">{{ $partner->commission_rate }}%</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Minimum Order:</span>
                        <span class="info-value">${{ number_format($partner->minimum_order, 2) }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="info-value" style="color: #28a745; font-weight: 600;">✅ Active</span>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h3>🎯 Next Steps</h3>
                <ol>
                    <li>Review the API documentation and integration guides</li>
                    <li>Set up your development environment with the provided credentials</li>
                    <li>Test the API endpoints in your sandbox environment</li>
                    <li>Implement product catalog integration on your website</li>
                    <li>Configure order processing and webhook notifications</li>
                    <li>Go live and start earning commissions!</li>
                </ol>
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <a href="{{ url('/api/docs') }}" class="btn">
                    📚 View API Documentation
                </a>
                <a href="{{ url('/partner/dashboard') }}" class="btn btn-secondary">
                    📊 Access Partner Dashboard
                </a>
            </div>

            <!-- Support Section -->
            <div class="support-section">
                <h3>🤝 Partner Success Team</h3>
                <p>Our dedicated team is here to help you succeed!</p>
                <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>📞 Phone: <a href="tel:+1234567890">+****************</a></p>
                <p>🕒 Business Hours: Monday - Friday, 9 AM - 6 PM EST</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>BelteiEcom Partner Program</strong></p>
            <p>Thank you for choosing to partner with us. We look forward to a successful collaboration!</p>
            <p style="margin-top: 1rem; font-size: 0.8rem;">
                This email contains sensitive information. Please keep your API credentials secure.
            </p>
        </div>
    </div>
</body>
</html>
