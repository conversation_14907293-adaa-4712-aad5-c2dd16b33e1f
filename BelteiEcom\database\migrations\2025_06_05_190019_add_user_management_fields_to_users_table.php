<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('badge')->nullable()->after('is_admin'); // 'admin', 'verified', 'premium', etc.
            $table->boolean('is_banned')->default(false)->after('badge');
            $table->timestamp('banned_at')->nullable()->after('is_banned');
            $table->text('ban_reason')->nullable()->after('banned_at');
            $table->foreignId('banned_by')->nullable()->constrained('users')->onDelete('set null')->after('ban_reason');
            $table->timestamp('ban_expires_at')->nullable()->after('banned_by');

            // Index for performance
            $table->index(['is_banned']);
            $table->index(['badge']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['banned_by']);
            $table->dropColumn([
                'badge',
                'is_banned',
                'banned_at',
                'ban_reason',
                'banned_by',
                'ban_expires_at'
            ]);
        });
    }
};
