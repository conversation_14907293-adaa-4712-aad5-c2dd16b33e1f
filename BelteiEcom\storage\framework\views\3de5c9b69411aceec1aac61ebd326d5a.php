<?php $__env->startSection('title', 'My Profile'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .profile-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .profile-header > * {
        position: relative;
        z-index: 2;
    }

    .profile-avatar-large {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
        border: 2px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        backdrop-filter: blur(10px);
        flex-shrink: 0;
    }

    .profile-avatar-large img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .profile-info {
        flex: 1;
    }

    .profile-name {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 0.1rem;
    }

    .profile-email {
        font-size: 0.8rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
    }

    .profile-stats {
        display: flex;
        gap: 1rem;
    }

    .stat-item {
        text-align: left;
    }

    .stat-number {
        font-size: 0.9rem;
        font-weight: bold;
        display: block;
        line-height: 1.2;
    }

    .stat-label {
        font-size: 0.7rem;
        opacity: 0.8;
    }

    .profile-content {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .profile-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .profile-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .profile-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .profile-menu li {
        margin-bottom: 0.5rem;
    }

    .profile-menu a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: #333;
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .profile-menu a:hover,
    .profile-menu a.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(5px);
    }

    .profile-menu i {
        width: 20px;
        text-align: center;
    }

    .recent-orders {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .recent-orders h3 {
        margin-bottom: 1rem;
        color: #333;
        font-weight: 600;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 1px solid #f0f0f0;
        border-radius: 10px;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
    }

    .order-item:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }

    .order-info h4 {
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .order-info p {
        margin: 0;
        font-size: 0.8rem;
        color: #666;
    }

    .order-amount {
        font-weight: bold;
        color: #667eea;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.4rem;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 1.5rem;
        color: #666;
    }

    .empty-state i {
        font-size: 2rem;
        margin-bottom: 0.75rem;
        opacity: 0.5;
    }

    .empty-state h4 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .profile-content {
            grid-template-columns: 1fr;
        }

        .profile-header {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
            padding: 1rem;
        }

        .profile-stats {
            gap: 0.75rem;
            justify-content: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 0.85rem;
        }

        .stat-label {
            font-size: 0.65rem;
        }

        .profile-container {
            padding: 1rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-avatar-large">
            <?php
                $profilePictureUrl = App\Http\Controllers\UserProfileController::getProfilePictureUrl($user);
            ?>
            <?php if($profilePictureUrl): ?>
                <img src="<?php echo e($profilePictureUrl); ?>" alt="<?php echo e($user->name); ?>">
            <?php else: ?>
                <?php echo e(App\Http\Controllers\UserProfileController::getUserInitials($user)); ?>

            <?php endif; ?>
        </div>
        <div class="profile-info">
            <h1 class="profile-name"><?php echo e($user->name); ?></h1>
            <p class="profile-email"><?php echo e($user->email); ?></p>

            <div class="profile-stats">
                <div class="stat-item">
                    <span class="stat-number"><?php echo e($totalOrders); ?></span>
                    <span class="stat-label">Total Orders</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">$<?php echo e(number_format($totalSpent, 2)); ?></span>
                    <span class="stat-label">Total Spent</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo e($user->created_at->format('M Y')); ?></span>
                    <span class="stat-label">Member Since</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <!-- Sidebar -->
        <div class="profile-sidebar">
            <div class="profile-card">
                <h3>Account Menu</h3>
                <ul class="profile-menu">
                    <li><a href="<?php echo e(route('profile.index')); ?>" class="active"><i class="fas fa-user"></i> Profile Overview</a></li>
                    <li><a href="<?php echo e(route('profile.edit')); ?>"><i class="fas fa-edit"></i> Edit Profile</a></li>
                    <li><a href="<?php echo e(route('orders.history')); ?>"><i class="fas fa-shopping-bag"></i> Order History</a></li>
                    <li><a href="<?php echo e(route('profile.password')); ?>"><i class="fas fa-lock"></i> Change Password</a></li>
                    <li><a href="<?php echo e(route('profile.qr-scanner')); ?>"><i class="fas fa-qrcode"></i> Scan QR Code</a></li>
                    <?php if($user->is_admin): ?>
                        <li><a href="<?php echo e(route('admin.dashboard')); ?>"><i class="fas fa-tachometer-alt"></i> Admin Dashboard</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="recent-orders">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3>Recent Orders</h3>
                <a href="<?php echo e(route('orders.history')); ?>" class="btn-primary">
                    <i class="fas fa-history"></i> View All Orders
                </a>
            </div>

            <?php if($recentOrders->count() > 0): ?>
                <?php $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="order-item">
                        <div class="order-info">
                            <h4>Order #<?php echo e($order->id); ?></h4>
                            <p><?php echo e($order->created_at->format('M d, Y')); ?> • <?php echo e(ucfirst($order->status)); ?></p>
                        </div>
                        <div class="order-amount">$<?php echo e(number_format($order->total_amount, 2)); ?></div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-shopping-bag"></i>
                    <h4>No Orders Yet</h4>
                    <p>Start shopping to see your orders here!</p>
                    <a href="<?php echo e(route('products.index')); ?>" class="btn-primary">
                        <i class="fas fa-shopping-cart"></i> Start Shopping
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/profile/index.blade.php ENDPATH**/ ?>