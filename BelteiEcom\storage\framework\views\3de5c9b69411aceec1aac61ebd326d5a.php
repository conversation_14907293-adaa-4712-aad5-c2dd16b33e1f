<?php $__env->startSection('title', 'My Profile'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .profile-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .profile-header > * {
        position: relative;
        z-index: 2;
    }

    .profile-avatar-large {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
        border: 2px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        backdrop-filter: blur(10px);
        flex-shrink: 0;
    }

    .profile-avatar-large img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .profile-info {
        flex: 1;
    }

    .profile-name {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 0.1rem;
    }

    .profile-email {
        font-size: 0.8rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
    }

    .profile-stats {
        display: flex;
        gap: 1rem;
    }

    .stat-item {
        text-align: left;
    }

    .stat-number {
        font-size: 0.9rem;
        font-weight: bold;
        display: block;
        line-height: 1.2;
    }

    .stat-label {
        font-size: 0.7rem;
        opacity: 0.8;
    }

    .profile-content {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .profile-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .profile-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .profile-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .profile-menu li {
        margin-bottom: 0.5rem;
    }

    .profile-menu a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: #333;
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .profile-menu a:hover,
    .profile-menu a.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateX(5px);
    }

    .profile-menu i {
        width: 20px;
        text-align: center;
    }

    .recent-orders {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .recent-orders h3 {
        margin-bottom: 1rem;
        color: #333;
        font-weight: 600;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 1px solid #f0f0f0;
        border-radius: 10px;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
    }

    .order-item:hover {
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }

    .order-info h4 {
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .order-info p {
        margin: 0;
        font-size: 0.8rem;
        color: #666;
    }

    .order-amount {
        font-weight: bold;
        color: #667eea;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.4rem;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 1.5rem;
        color: #666;
    }

    .empty-state i {
        font-size: 2rem;
        margin-bottom: 0.75rem;
        opacity: 0.5;
    }

    .empty-state h4 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .profile-content {
            grid-template-columns: 1fr;
        }

        .profile-header {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
            padding: 1rem;
        }

        .profile-stats {
            gap: 0.75rem;
            justify-content: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 0.85rem;
        }

        .stat-label {
            font-size: 0.65rem;
        }

        .profile-container {
            padding: 1rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-avatar-large">
            <?php
                $profilePictureUrl = App\Http\Controllers\UserProfileController::getProfilePictureUrl($user);
            ?>
            <?php if($profilePictureUrl): ?>
                <img src="<?php echo e($profilePictureUrl); ?>" alt="<?php echo e($user->name); ?>">
            <?php else: ?>
                <?php echo e(App\Http\Controllers\UserProfileController::getUserInitials($user)); ?>

            <?php endif; ?>
        </div>
        <div class="profile-info">
            <h1 class="profile-name"><?php echo e($user->name); ?></h1>
            <p class="profile-email"><?php echo e($user->email); ?></p>

            <div class="profile-stats">
                <div class="stat-item">
                    <span class="stat-number"><?php echo e($totalOrders); ?></span>
                    <span class="stat-label">Total Orders</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">$<?php echo e(number_format($totalSpent, 2)); ?></span>
                    <span class="stat-label">Total Spent</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo e($user->created_at->format('M Y')); ?></span>
                    <span class="stat-label">Member Since</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <!-- Sidebar -->
        <div class="profile-sidebar">
            <div class="profile-card">
                <h3>Account Menu</h3>
                <ul class="profile-menu">
                    <li><a href="<?php echo e(route('profile.index')); ?>" class="active"><i class="fas fa-user"></i> Profile Overview</a></li>
                    <li><a href="<?php echo e(route('profile.edit')); ?>"><i class="fas fa-edit"></i> Edit Profile</a></li>
                    <li><a href="<?php echo e(route('orders.history')); ?>"><i class="fas fa-shopping-bag"></i> Order History</a></li>
                    <li><a href="<?php echo e(route('profile.password')); ?>"><i class="fas fa-lock"></i> Change Password</a></li>
                    <li><a href="#" onclick="openQrScanner()" class="qr-scanner-link"><i class="fas fa-qrcode"></i> Scan QR Code</a></li>
                    <?php if($user->is_admin): ?>
                        <li><a href="<?php echo e(route('admin.dashboard')); ?>"><i class="fas fa-tachometer-alt"></i> Admin Dashboard</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="recent-orders">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3>Recent Orders</h3>
                <a href="<?php echo e(route('orders.history')); ?>" class="btn-primary">
                    <i class="fas fa-history"></i> View All Orders
                </a>
            </div>

            <?php if($recentOrders->count() > 0): ?>
                <?php $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="order-item">
                        <div class="order-info">
                            <h4>Order #<?php echo e($order->id); ?></h4>
                            <p><?php echo e($order->created_at->format('M d, Y')); ?> • <?php echo e(ucfirst($order->status)); ?></p>
                        </div>
                        <div class="order-amount">$<?php echo e(number_format($order->total_amount, 2)); ?></div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-shopping-bag"></i>
                    <h4>No Orders Yet</h4>
                    <p>Start shopping to see your orders here!</p>
                    <a href="<?php echo e(route('products.index')); ?>" class="btn-primary">
                        <i class="fas fa-shopping-cart"></i> Start Shopping
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- QR Scanner Modal -->
<div id="qr-scanner-modal" class="qr-modal" style="display: none;">
    <div class="qr-modal-content">
        <div class="qr-modal-header">
            <h3><i class="fas fa-qrcode"></i> QR Code Scanner</h3>
            <button type="button" class="qr-modal-close" onclick="closeQrScanner()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="qr-modal-body">
            <div class="qr-scanner-container">
                <div id="qr-scanner-video-container">
                    <video id="qr-scanner-video" autoplay muted playsinline></video>
                    <div class="qr-scanner-overlay">
                        <div class="qr-scanner-frame"></div>
                    </div>
                </div>

                <div id="qr-scanner-placeholder" class="qr-scanner-placeholder">
                    <i class="fas fa-camera"></i>
                    <p>Click "Start Camera" to begin scanning</p>
                </div>
            </div>

            <div class="qr-scanner-controls">
                <button type="button" id="start-camera-btn" class="qr-btn qr-btn-primary" onclick="startCamera()">
                    <i class="fas fa-camera"></i> Start Camera
                </button>
                <button type="button" id="stop-camera-btn" class="qr-btn qr-btn-secondary" onclick="stopCamera()" style="display: none;">
                    <i class="fas fa-stop"></i> Stop Camera
                </button>
            </div>

            <div class="qr-scanner-status" id="qr-scanner-status">
                <div class="status-message">
                    <i class="fas fa-info-circle"></i>
                    <span>Position the QR code within the frame to scan</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
/* QR Scanner Modal Styles */
.qr-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.qr-modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.qr-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.qr-modal-header h3 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qr-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.qr-modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.qr-modal-body {
    padding: 2rem;
}

.qr-scanner-container {
    position: relative;
    margin-bottom: 2rem;
}

#qr-scanner-video-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    border-radius: 15px;
    overflow: hidden;
    background: #000;
}

#qr-scanner-video {
    width: 100%;
    height: auto;
    display: block;
}

.qr-scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-scanner-frame {
    width: 200px;
    height: 200px;
    border: 3px solid #4ecdc4;
    border-radius: 15px;
    position: relative;
    animation: pulse 2s infinite;
}

.qr-scanner-frame::before,
.qr-scanner-frame::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #4ecdc4;
}

.qr-scanner-frame::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.qr-scanner-frame::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.qr-scanner-placeholder {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px dashed #e0e0e0;
}

.qr-scanner-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #999;
}

.qr-scanner-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.qr-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qr-btn-primary {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.qr-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
}

.qr-btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e0e0e0;
}

.qr-btn-secondary:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.qr-scanner-status {
    text-align: center;
}

.status-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 15px;
    color: #666;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
}

/* Responsive */
@media (max-width: 768px) {
    .qr-modal {
        padding: 1rem;
    }

    .qr-modal-header,
    .qr-modal-body {
        padding: 1.5rem;
    }

    .qr-scanner-controls {
        flex-direction: column;
    }

    .qr-scanner-frame {
        width: 150px;
        height: 150px;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
let qrScanner = null;
let videoStream = null;

// Open QR scanner modal
function openQrScanner() {
    document.getElementById('qr-scanner-modal').style.display = 'flex';
}

// Close QR scanner modal
function closeQrScanner() {
    stopCamera();
    document.getElementById('qr-scanner-modal').style.display = 'none';
}

// Start camera for QR scanning
async function startCamera() {
    try {
        updateScannerStatus('info', 'Starting camera...');

        // Request camera permission
        videoStream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment', // Use back camera if available
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        });

        const video = document.getElementById('qr-scanner-video');
        video.srcObject = videoStream;

        // Show video container and hide placeholder
        document.getElementById('qr-scanner-video-container').style.display = 'block';
        document.getElementById('qr-scanner-placeholder').style.display = 'none';

        // Update buttons
        document.getElementById('start-camera-btn').style.display = 'none';
        document.getElementById('stop-camera-btn').style.display = 'inline-flex';

        updateScannerStatus('info', 'Position the QR code within the frame to scan');

        // Start QR code detection
        startQrDetection();

    } catch (error) {
        console.error('Camera access error:', error);
        updateScannerStatus('error', 'Camera access denied or not available');
    }
}

// Stop camera
function stopCamera() {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }

    if (qrScanner) {
        clearInterval(qrScanner);
        qrScanner = null;
    }

    // Hide video container and show placeholder
    document.getElementById('qr-scanner-video-container').style.display = 'none';
    document.getElementById('qr-scanner-placeholder').style.display = 'block';

    // Update buttons
    document.getElementById('start-camera-btn').style.display = 'inline-flex';
    document.getElementById('stop-camera-btn').style.display = 'none';

    updateScannerStatus('info', 'Click "Start Camera" to begin scanning');
}

// Start QR code detection (simplified version using canvas)
function startQrDetection() {
    const video = document.getElementById('qr-scanner-video');
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    qrScanner = setInterval(() => {
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            // For a production app, you would use a QR code library like jsQR here
            // For now, we'll simulate QR detection by checking for URL patterns
            // This is a simplified implementation

            // In a real implementation, you would:
            // const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            // const code = jsQR(imageData.data, imageData.width, imageData.height);
            // if (code) { handleQrCodeDetected(code.data); }
        }
    }, 500);
}

// Handle QR code detection
async function handleQrCodeDetected(qrData) {
    // Extract token from QR data URL
    const urlMatch = qrData.match(/\/qr-login\/scan\/([a-zA-Z0-9]+)/);
    if (!urlMatch) {
        updateScannerStatus('error', 'Invalid QR code format');
        return;
    }

    const token = urlMatch[1];

    try {
        updateScannerStatus('info', 'QR code detected! Confirming login...');

        const response = await fetch('<?php echo e(route("qr-login.confirm")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({ token })
        });

        const data = await response.json();

        if (data.success) {
            updateScannerStatus('success', 'Login confirmed successfully!');
            setTimeout(() => {
                closeQrScanner();
            }, 2000);
        } else {
            updateScannerStatus('error', data.message || 'Failed to confirm login');
        }
    } catch (error) {
        updateScannerStatus('error', 'Network error occurred');
    }
}

// Update scanner status
function updateScannerStatus(type, message) {
    const statusElement = document.querySelector('.status-message');
    statusElement.className = `status-message ${type}`;
    statusElement.querySelector('span').textContent = message;

    // Update icon based on type
    const icon = statusElement.querySelector('i');
    icon.className = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-triangle' :
                    'fas fa-info-circle';
}

// For demonstration purposes, add a manual QR code input
function simulateQrScan() {
    const qrUrl = prompt('Enter QR code URL for testing:');
    if (qrUrl) {
        handleQrCodeDetected(qrUrl);
    }
}

// Close modal when clicking outside
document.getElementById('qr-scanner-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQrScanner();
    }
});

// Add a test button for development (remove in production)
document.addEventListener('DOMContentLoaded', function() {
    // Add test button to QR scanner controls for development
    const controls = document.querySelector('.qr-scanner-controls');
    if (controls) {
        const testBtn = document.createElement('button');
        testBtn.type = 'button';
        testBtn.className = 'qr-btn qr-btn-secondary';
        testBtn.innerHTML = '<i class="fas fa-keyboard"></i> Test Input';
        testBtn.onclick = simulateQrScan;
        testBtn.style.fontSize = '0.8rem';
        controls.appendChild(testBtn);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/profile/index.blade.php ENDPATH**/ ?>