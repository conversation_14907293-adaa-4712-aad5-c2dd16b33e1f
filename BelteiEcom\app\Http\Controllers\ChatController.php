<?php

namespace App\Http\Controllers;

use App\Models\ChatConversation;
use App\Models\ChatMessage;
use App\Services\ChatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatController extends Controller
{
    protected $chatService;

    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Display the chat interface.
     */
    public function index()
    {
        $userId = Auth::id();
        $sessionId = session()->getId();

        // Get user's conversations
        $conversations = $this->chatService->getUserConversations($userId, $sessionId);

        return view('chat.index', compact('conversations'));
    }

    /**
     * Start a new chat conversation.
     */
    public function start(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'guest_name' => 'nullable|string|max:100',
            'guest_email' => 'nullable|email|max:100',
            'subject' => 'nullable|string|max:200',
            'priority' => 'nullable|in:low,normal,high,urgent',
        ]);

        try {
            $conversation = $this->chatService->startConversation([
                'message' => $request->message,
                'guest_name' => $request->guest_name,
                'guest_email' => $request->guest_email,
                'subject' => $request->subject,
                'priority' => $request->priority ?? 'normal',
                'session_id' => session()->getId(),
            ]);

            return response()->json([
                'success' => true,
                'conversation' => $conversation,
                'redirect' => route('chat.conversation', $conversation->id),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start conversation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display a specific conversation.
     */
    public function conversation($id)
    {
        $conversation = ChatConversation::with(['customer', 'agent'])->findOrFail($id);

        // Check if user has access to this conversation
        $userId = Auth::id();
        $sessionId = session()->getId();

        if ($conversation->user_id !== $userId && $conversation->session_id !== $sessionId) {
            abort(403, 'Access denied to this conversation.');
        }

        // Get messages
        $messages = $this->chatService->getMessages($id);

        // Mark messages as read
        $this->chatService->markAsRead($id);

        return view('chat.conversation', compact('conversation', 'messages'));
    }

    /**
     * Send a message in a conversation.
     */
    public function sendMessage(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
        ]);

        $conversation = ChatConversation::findOrFail($id);

        // Check if user has access to this conversation
        $userId = Auth::id();
        $sessionId = session()->getId();

        if ($conversation->user_id !== $userId && $conversation->session_id !== $sessionId) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied to this conversation.',
            ], 403);
        }

        try {
            $message = $this->chatService->sendMessage(
                $id,
                $request->message,
                'customer'
            );

            return response()->json([
                'success' => true,
                'message' => [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender_name' => $message->sender_name,
                    'sender_type' => $message->sender_type,
                    'formatted_time' => $message->formatted_time,
                    'created_at' => $message->created_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get new messages for a conversation.
     */
    public function getMessages($id, Request $request)
    {
        $conversation = ChatConversation::findOrFail($id);

        // Check if user has access to this conversation
        $userId = Auth::id();
        $sessionId = session()->getId();

        if ($conversation->user_id !== $userId && $conversation->session_id !== $sessionId) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied to this conversation.',
            ], 403);
        }

        $lastMessageId = $request->get('last_message_id', 0);

        $messages = ChatMessage::where('conversation_id', $id)
                              ->where('id', '>', $lastMessageId)
                              ->with('user')
                              ->orderBy('created_at', 'asc')
                              ->get();

        $formattedMessages = $messages->map(function ($message) {
            return [
                'id' => $message->id,
                'message' => $message->message,
                'sender_name' => $message->sender_name,
                'sender_type' => $message->sender_type,
                'formatted_time' => $message->formatted_time,
                'created_at' => $message->created_at->toISOString(),
            ];
        });

        // Mark new messages as read
        if ($messages->count() > 0) {
            $this->chatService->markAsRead($id);
        }

        return response()->json([
            'success' => true,
            'messages' => $formattedMessages,
            'conversation_status' => $conversation->status,
        ]);
    }

    /**
     * Close a conversation.
     */
    public function close($id, Request $request)
    {
        $request->validate([
            'rating' => 'nullable|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:500',
        ]);

        $conversation = ChatConversation::findOrFail($id);

        // Check if user has access to this conversation
        $userId = Auth::id();
        $sessionId = session()->getId();

        if ($conversation->user_id !== $userId && $conversation->session_id !== $sessionId) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied to this conversation.',
            ], 403);
        }

        try {
            $this->chatService->closeConversation(
                $id,
                'Closed by customer',
                $request->rating,
                $request->feedback
            );

            return response()->json([
                'success' => true,
                'message' => 'Conversation closed successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to close conversation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get chat widget data.
     */
    public function widget()
    {
        $userId = Auth::id();
        $sessionId = session()->getId();

        // Check for existing active conversation
        $activeConversation = ChatConversation::where(function ($query) use ($userId, $sessionId) {
            if ($userId) {
                $query->where('user_id', $userId);
            } else {
                $query->where('session_id', $sessionId);
            }
        })
        ->whereIn('status', ['waiting', 'active'])
        ->first();

        $onlineAgents = $this->chatService->getOnlineAgentsCount();

        return response()->json([
            'success' => true,
            'has_active_conversation' => $activeConversation ? true : false,
            'active_conversation_id' => $activeConversation ? $activeConversation->id : null,
            'online_agents' => $onlineAgents,
            'is_available' => $onlineAgents > 0,
        ]);
    }
}
