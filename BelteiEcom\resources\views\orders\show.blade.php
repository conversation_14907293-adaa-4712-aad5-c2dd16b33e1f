@extends('layouts.app')

@section('title', 'Order #' . $order->id)

@section('content')
<div class="order-detail-page">
    <!-- Back Navigation -->
    <div class="back-navigation">
        <a href="{{ route('orders.history') }}" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            Back to Orders
        </a>
    </div>

    <!-- Order Header -->
    <div class="order-header">
        <div class="order-header-content">
            <div class="order-title-section">
                <h1 class="order-title">
                    <i class="fas fa-receipt"></i>
                    Order #{{ $order->id }}
                </h1>
                <p class="order-date">
                    <i class="fas fa-calendar-alt"></i>
                    Placed on {{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('F j, Y \a\t g:i A') }} (Cambodia Time)
                </p>
            </div>
            <div class="order-status-section">
                <span class="status-badge status-{{ $order->status }}">
                    <i class="fas
                        @if($order->status == 'pending_payment') fa-credit-card
                        @elseif($order->status == 'new') fa-clock
                        @elseif($order->status == 'processing') fa-cog
                        @elseif($order->status == 'shipped') fa-truck
                        @elseif($order->status == 'delivered') fa-check-circle
                        @elseif($order->status == 'cancelled') fa-times-circle
                        @endif
                    "></i>
                    {{ $order->status == 'pending_payment' ? 'Pending Payment' : ucfirst($order->status) }}
                </span>
            </div>
        </div>
    </div>

    <!-- Order Content Grid -->
    <div class="order-content-grid">
        <!-- Order Items Section -->
        <div class="order-section order-items-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-box"></i>
                    Items Ordered
                </h2>
                <span class="items-count">{{ $order->orderItems->count() }} {{ Str::plural('item', $order->orderItems->count()) }}</span>
            </div>

            <div class="order-items-list">
                @foreach($order->orderItems as $item)
                    <div class="order-item">
                        <div class="item-image">
                            @if($item->product->image)
                                <img src="{{ asset('storage/' . $item->product->image) }}"
                                     alt="{{ $item->product->name }}">
                            @else
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                            @endif
                        </div>

                        <div class="item-details">
                            <h3 class="item-name">
                                <a href="{{ route('products.show', $item->product->slug) }}">
                                    {{ $item->product->name }}
                                </a>
                            </h3>
                            <div class="item-pricing">
                                <span class="item-quantity">Qty: {{ $item->quantity }}</span>
                                <span class="item-unit-price">${{ number_format($item->price, 2) }} each</span>
                            </div>
                        </div>

                        <div class="item-total">
                            <span class="total-price">${{ number_format($item->price * $item->quantity, 2) }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Order Summary Section -->
        <div class="order-section order-summary-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-calculator"></i>
                    Order Summary
                </h2>
            </div>

            <div class="summary-content">
                <div class="summary-row">
                    <span class="summary-label">Subtotal:</span>
                    <span class="summary-value">${{ number_format($order->total_amount, 2) }}</span>
                </div>
                <div class="summary-row">
                    <span class="summary-label">Shipping:</span>
                    <span class="summary-value free">Free</span>
                </div>
                <div class="summary-row">
                    <span class="summary-label">Tax:</span>
                    <span class="summary-value">$0.00</span>
                </div>
                <div class="summary-divider"></div>
                <div class="summary-row total-row">
                    <span class="summary-label">Total:</span>
                    <span class="summary-value">${{ number_format($order->total_amount, 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Shipping Information Section -->
        <div class="order-section shipping-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-shipping-fast"></i>
                    Shipping Information
                </h2>
            </div>

            <div class="shipping-content">
                <div class="shipping-detail">
                    <div class="detail-label">
                        <i class="fas fa-phone"></i>
                        Phone Number
                    </div>
                    <div class="detail-value">{{ $order->phone_number }}</div>
                </div>

                <div class="shipping-detail">
                    <div class="detail-label">
                        <i class="fas fa-map-marker-alt"></i>
                        Delivery Address
                    </div>
                    <div class="detail-value address">{{ $order->shipping_address }}</div>
                </div>
            </div>
        </div>

        <!-- Order Status Timeline -->
        <div class="order-section status-timeline-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-route"></i>
                    Order Timeline
                </h2>
            </div>

            <div class="status-timeline">
                <div class="timeline-step {{ in_array($order->status, ['new', 'processing', 'shipped', 'delivered']) ? 'completed' : ($order->status == 'cancelled' ? 'cancelled' : 'pending') }}">
                    <div class="step-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="step-content">
                        <h4 class="step-title">Order Placed</h4>
                        <p class="step-description">Your order has been received and confirmed</p>
                        <span class="step-time">{{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('M j, Y \a\t g:i A') }} (Cambodia Time)</span>
                    </div>
                </div>

                @if($order->status == 'pending_payment')
                    <div class="timeline-connector"></div>
                    <div class="timeline-step pending">
                        <div class="step-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="step-content">
                            <h4 class="step-title">Awaiting Payment</h4>
                            <p class="step-description">Please complete your payment to proceed</p>
                            <span class="step-time">Payment pending</span>
                        </div>
                    </div>
                @endif

                <div class="timeline-connector {{ in_array($order->status, ['processing', 'shipped', 'delivered']) ? 'active' : '' }}"></div>

                <div class="timeline-step {{ in_array($order->status, ['processing', 'shipped', 'delivered']) ? 'completed' : ($order->status == 'cancelled' ? 'cancelled' : 'pending') }}">
                    <div class="step-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="step-content">
                        <h4 class="step-title">Processing</h4>
                        <p class="step-description">Your order is being prepared for shipment</p>
                        @if(in_array($order->status, ['processing', 'shipped', 'delivered']))
                            <span class="step-time">Processing started</span>
                        @endif
                    </div>
                </div>

                <div class="timeline-connector {{ in_array($order->status, ['shipped', 'delivered']) ? 'active' : '' }}"></div>

                <div class="timeline-step {{ in_array($order->status, ['shipped', 'delivered']) ? 'completed' : ($order->status == 'cancelled' ? 'cancelled' : 'pending') }}">
                    <div class="step-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="step-content">
                        <h4 class="step-title">Shipped</h4>
                        <p class="step-description">Your order is on its way to you</p>
                        @if(in_array($order->status, ['shipped', 'delivered']))
                            <span class="step-time">Shipped</span>
                        @endif
                    </div>
                </div>

                <div class="timeline-connector {{ $order->status == 'delivered' ? 'active' : '' }}"></div>

                <div class="timeline-step {{ $order->status == 'delivered' ? 'completed' : ($order->status == 'cancelled' ? 'cancelled' : 'pending') }}">
                    <div class="step-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="step-content">
                        <h4 class="step-title">Delivered</h4>
                        <p class="step-description">Your order has been delivered successfully</p>
                        @if($order->status == 'delivered')
                            <span class="step-time">Delivered</span>
                        @endif
                    </div>
                </div>
            </div>

            @if($order->status == 'cancelled')
                <div class="cancelled-notice">
                    <div class="cancelled-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="cancelled-content">
                        <h4>Order Cancelled</h4>
                        <p>This order has been cancelled. If you have any questions, please contact our support team.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Order Actions -->
    <div class="order-actions">
        @if($order->status == 'delivered')
            <button class="btn btn-primary" onclick="reorderItems({{ $order->id }})">
                <i class="fas fa-redo"></i>
                Reorder Items
            </button>
        @endif

        <a href="{{ route('orders.history') }}" class="btn btn-secondary">
            <i class="fas fa-list"></i>
            View All Orders
        </a>

        <a href="{{ route('orders.receipt.download', $order->pdf_token) }}" class="btn btn-pdf">
            <i class="fas fa-file-pdf"></i>
            Download PDF
        </a>
    </div>
</div>
@endsection

@section('styles')
<style>
    /* Order Detail Page Styles */
    .order-detail-page {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    /* Back Navigation */
    .back-navigation {
        margin-bottom: 2rem;
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
    }

    /* Order Header */
    .order-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 2.5rem;
        margin-bottom: 3rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .order-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateY(0px) rotate(0deg); }
        100% { transform: translateY(-20px) rotate(360deg); }
    }

    .order-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .order-title {
        font-size: 2.2rem;
        font-weight: 800;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .order-title i {
        font-size: 1.8rem;
        opacity: 0.9;
    }

    .order-date {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
    }

    .status-pending_payment {
        background: rgba(253, 126, 20, 0.9);
        color: white;
    }

    .status-new {
        background: rgba(23, 162, 184, 0.9);
        color: white;
    }

    .status-processing {
        background: rgba(255, 193, 7, 0.9);
        color: #212529;
    }

    .status-shipped {
        background: rgba(0, 123, 255, 0.9);
        color: white;
    }

    .status-delivered {
        background: rgba(40, 167, 69, 0.9);
        color: white;
    }

    .status-cancelled {
        background: rgba(220, 53, 69, 0.9);
        color: white;
    }

    /* Order Content Grid */
    .order-content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }

    /* Order Sections */
    .order-section {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 1px solid rgba(255,255,255,0.2);
    }

    .section-header {
        padding: 1.5rem 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .section-title {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: #667eea;
        font-size: 1.1rem;
    }

    .items-count {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    /* Order Items */
    .order-items-list {
        padding: 0;
    }

    .order-item {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #f1f3f4;
        transition: background 0.3s ease;
    }

    .order-item:last-child {
        border-bottom: none;
    }

    .order-item:hover {
        background: #f8f9fa;
    }

    .item-image {
        width: 80px;
        height: 80px;
        border-radius: 15px;
        overflow: hidden;
        border: 2px solid #f1f3f4;
        flex-shrink: 0;
    }

    .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #636e72;
        font-size: 1.5rem;
    }

    .item-details {
        flex: 1;
    }

    .item-name {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .item-name a {
        color: #2d3436;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .item-name a:hover {
        color: #667eea;
    }

    .item-pricing {
        display: flex;
        gap: 1rem;
        font-size: 0.9rem;
        color: #636e72;
    }

    .item-total {
        text-align: right;
    }

    .total-price {
        font-size: 1.2rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Order Summary */
    .summary-content {
        padding: 2rem;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .summary-label {
        color: #636e72;
        font-weight: 500;
    }

    .summary-value {
        font-weight: 600;
        color: #2d3436;
    }

    .summary-value.free {
        color: #28a745;
        font-weight: 700;
    }

    .summary-divider {
        height: 1px;
        background: #e9ecef;
        margin: 1.5rem 0;
    }

    .total-row {
        margin-bottom: 0;
        padding-top: 1rem;
        border-top: 2px solid #e9ecef;
    }

    .total-row .summary-label,
    .total-row .summary-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3436;
    }

    /* Shipping Information */
    .shipping-content {
        padding: 2rem;
    }

    .shipping-detail {
        margin-bottom: 1.5rem;
    }

    .shipping-detail:last-child {
        margin-bottom: 0;
    }

    .detail-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #636e72;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .detail-label i {
        color: #667eea;
        width: 16px;
    }

    .detail-value {
        color: #2d3436;
        font-weight: 500;
        padding-left: 1.5rem;
    }

    .detail-value.address {
        white-space: pre-line;
        line-height: 1.5;
    }

    /* Status Timeline */
    .status-timeline {
        padding: 2rem;
    }

    .timeline-step {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        position: relative;
    }

    .timeline-step:last-child {
        margin-bottom: 0;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        flex-shrink: 0;
        position: relative;
        z-index: 2;
    }

    .timeline-step.completed .step-icon {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .timeline-step.pending .step-icon {
        background: #f8f9fa;
        color: #636e72;
        border: 2px solid #e9ecef;
    }

    .timeline-step.cancelled .step-icon {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }

    .step-content {
        flex: 1;
        padding-top: 0.5rem;
    }

    .step-title {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3436;
    }

    .step-description {
        margin: 0 0 0.5rem 0;
        color: #636e72;
        font-size: 0.9rem;
    }

    .step-time {
        font-size: 0.8rem;
        color: #667eea;
        font-weight: 500;
    }

    .timeline-connector {
        position: absolute;
        left: 24px;
        top: 50px;
        width: 2px;
        height: 40px;
        background: #e9ecef;
        z-index: 1;
    }

    .timeline-connector.active {
        background: linear-gradient(to bottom, #28a745, #20c997);
    }

    /* Cancelled Notice */
    .cancelled-notice {
        margin-top: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .cancelled-icon {
        font-size: 2rem;
        color: #dc3545;
    }

    .cancelled-content h4 {
        margin: 0 0 0.5rem 0;
        color: #721c24;
        font-weight: 700;
    }

    .cancelled-content p {
        margin: 0;
        color: #721c24;
    }

    /* Order Actions */
    .order-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        border: 2px solid transparent;
        cursor: pointer;
        font-size: 0.95rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        color: white;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .btn-pdf {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        color: white;
    }

    .btn-pdf:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .order-detail-page {
            padding: 1rem;
        }

        .order-header {
            padding: 2rem 1.5rem;
            margin-bottom: 2rem;
        }

        .order-header-content {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .order-title {
            font-size: 1.8rem;
        }

        .order-content-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .section-header {
            padding: 1rem 1.5rem;
        }

        .order-item {
            padding: 1rem 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .item-image {
            width: 60px;
            height: 60px;
        }

        .summary-content,
        .shipping-content,
        .status-timeline {
            padding: 1.5rem;
        }

        .order-actions {
            flex-direction: column;
        }

        .order-actions .btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .order-title {
            font-size: 1.6rem;
        }

        .section-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .order-item {
            padding: 1rem;
        }

        .timeline-step {
            gap: 0.75rem;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .timeline-connector {
            left: 19px;
            top: 40px;
            height: 35px;
        }
    }
</style>

<script>
function reorderItems(orderId) {
    // Add reorder functionality here
    alert('Reorder functionality will be implemented soon!');
}
</script>
@endsection
