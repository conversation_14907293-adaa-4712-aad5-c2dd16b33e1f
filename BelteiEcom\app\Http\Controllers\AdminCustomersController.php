<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class AdminCustomersController extends Controller
{
    /**
     * Display a listing of the customers.
     */
    public function index(Request $request)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $query = User::where('is_admin', false);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by registration date
        if ($request->has('date_filter') && $request->date_filter) {
            switch ($request->date_filter) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', Carbon::now()->month)
                          ->whereYear('created_at', Carbon::now()->year);
                    break;
                case 'year':
                    $query->whereYear('created_at', Carbon::now()->year);
                    break;
            }
        }

        // Email verification filter
        if ($request->has('email_verified') && $request->email_verified !== '') {
            if ($request->email_verified == '1') {
                $query->whereNotNull('email_verified_at');
            } else {
                $query->whereNull('email_verified_at');
            }
        }

        $customers = $query->withCount('orders')
                          ->withSum('orders', 'total_amount')
                          ->orderBy('created_at', 'desc')
                          ->paginate(12);

        $customersPaginator = $customers;

        // Get statistics
        $totalCustomers = User::where('is_admin', false)->count();
        $newCustomersToday = User::where('is_admin', false)->whereDate('created_at', Carbon::today())->count();
        $newCustomersThisMonth = User::where('is_admin', false)
                                   ->whereMonth('created_at', Carbon::now()->month)
                                   ->whereYear('created_at', Carbon::now()->year)
                                   ->count();
        $activeCustomers = User::where('is_admin', false)
                              ->whereHas('orders', function($q) {
                                  $q->where('created_at', '>=', Carbon::now()->subDays(30));
                              })
                              ->count();
        $unverifiedCustomers = User::where('is_admin', false)
                                  ->whereNull('email_verified_at')
                                  ->count();

        return view('admin.customers.index', compact(
            'customers',
            'customersPaginator',
            'totalCustomers',
            'newCustomersToday',
            'newCustomersThisMonth',
            'activeCustomers',
            'unverifiedCustomers'
        ));
    }

    /**
     * Display the specified customer.
     */
    public function show($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $customer = User::where('is_admin', false)
                       ->with(['orders' => function($query) {
                           $query->orderBy('created_at', 'desc');
                       }])
                       ->findOrFail($id);

        $customerStats = [
            'total_orders' => $customer->orders->count(),
            'total_spent' => $customer->orders->sum('total_amount'),
            'average_order' => $customer->orders->count() > 0 ? $customer->orders->avg('total_amount') : 0,
            'first_order' => $customer->orders->sortBy('created_at')->first(),
            'last_order' => $customer->orders->sortByDesc('created_at')->first(),
        ];

        return view('admin.customers.show', compact('customer', 'customerStats'));
    }

    /**
     * Show the form for editing the specified customer.
     */
    public function edit($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $customer = User::where('is_admin', false)->findOrFail($id);
        return view('admin.customers.edit', compact('customer'));
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, $id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $customer = User::where('is_admin', false)->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $data = $request->only(['name', 'email', 'phone', 'address']);

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $customer->update($data);

        return redirect()->route('admin.customers.index')->with('success', 'Customer updated successfully.');
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $customer = User::where('is_admin', false)->findOrFail($id);

        // Check if customer has orders
        if ($customer->orders()->count() > 0) {
            return redirect()->route('admin.customers.index')
                           ->with('error', 'Cannot delete customer with existing orders.');
        }

        $customer->delete();

        return redirect()->route('admin.customers.index')->with('success', 'Customer deleted successfully.');
    }

    /**
     * Show the form for creating a new customer.
     */
    public function create()
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        return view('admin.customers.create');
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'password' => 'required|string|min:8|confirmed',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'password' => Hash::make($request->password),
            'is_admin' => false,
        ]);

        return redirect()->route('admin.customers.index')->with('success', 'Customer created successfully.');
    }

    /**
     * Manually verify a customer's email
     */
    public function verifyEmail($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $customer = User::where('is_admin', false)->findOrFail($id);

        // Check if email is already verified
        if ($customer->hasVerifiedEmail()) {
            return redirect()->route('admin.customers.index')
                ->with('info', 'Customer email is already verified.');
        }

        // Mark email as verified
        $customer->markEmailAsVerified();

        return redirect()->route('admin.customers.index')
            ->with('success', 'Customer email has been verified successfully.');
    }
}
