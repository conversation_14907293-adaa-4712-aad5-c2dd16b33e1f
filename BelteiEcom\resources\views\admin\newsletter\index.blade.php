@extends('admin.layouts.app')

@section('title', 'Newsletter Subscribers')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📧 Newsletter Subscribers</h1>
            <p class="text-muted">Manage your newsletter subscribers and send broadcasts</p>
        </div>
        <div>
            <a href="{{ route('admin.newsletter.create') }}" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i> Send Newsletter
            </a>
            <a href="{{ route('admin.newsletter.export') }}" class="btn btn-success">
                <i class="fas fa-download"></i> Export CSV
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Subscribers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_subscribers']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Recent Subscribers (7 days)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['recent_subscribers']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Unsubscribed
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_unsubscribed']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-minus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Growth Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @if($stats['total_subscribers'] > 0)
                                    {{ number_format(($stats['recent_subscribers'] / $stats['total_subscribers']) * 100, 1) }}%
                                @else
                                    0%
                                @endif
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscribers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> Newsletter Subscribers
            </h6>
        </div>
        <div class="card-body">
            @if($subscribers->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Email Address</th>
                                <th>Name</th>
                                <th>Subscribed Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($subscribers as $subscriber)
                            <tr>
                                <td>
                                    <i class="fas fa-envelope text-primary"></i>
                                    {{ $subscriber->email }}
                                </td>
                                <td>
                                    @if($subscriber->name)
                                        <i class="fas fa-user text-success"></i>
                                        {{ $subscriber->name }}
                                    @else
                                        <i class="fas fa-user-slash text-muted"></i>
                                        <span class="text-muted font-italic">No name provided</span>
                                    @endif
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $subscriber->subscribed_at->format('M d, Y H:i') }}
                                    </small>
                                </td>
                                <td>
                                    @if($subscriber->is_active)
                                        <span class="badge badge-success">
                                            <i class="fas fa-check"></i> Active
                                        </span>
                                    @else
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-times"></i> Inactive
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="copyEmail('{{ $subscriber->email }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        @if($subscriber->is_active)
                                            <a href="{{ route('newsletter.unsubscribe', $subscriber->subscription_token) }}" 
                                               class="btn btn-sm btn-outline-warning"
                                               onclick="return confirm('Are you sure you want to unsubscribe this user?')">
                                                <i class="fas fa-user-minus"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $subscribers->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No Subscribers Yet</h5>
                    <p class="text-muted">When users subscribe to your newsletter, they'll appear here.</p>
                    <a href="{{ route('home') }}" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> View Website
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function copyEmail(email) {
    navigator.clipboard.writeText(email).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-check-circle"></i> Email copied to clipboard!
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        document.body.appendChild(toast);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            toast.remove();
        }, 3000);
    });
}
</script>
@endsection
