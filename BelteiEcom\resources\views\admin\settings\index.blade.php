@extends('admin.layouts.app')

@section('styles')
<style>
    .settings-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .settings-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
    }
    
    .settings-header:hover::before {
        right: -30%;
    }
    
    .settings-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 2rem;
        border: none;
        position: relative;
        overflow: hidden;
    }
    
    .settings-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: width 0.3s ease;
    }
    
    .settings-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .settings-card:hover::before {
        width: 8px;
    }
    
    .settings-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .settings-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .settings-description {
        color: #6c757d;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }
    
    .settings-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .settings-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .settings-btn i {
        margin-right: 0.5rem;
    }
    
    .admin-profile-card {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .admin-profile-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
    }
    
    .admin-profile-card:hover::before {
        right: -30%;
    }
    
    .admin-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        border: 3px solid rgba(255,255,255,0.3);
        background-size: cover;
        background-position: center;
    }
    
    .admin-info h4 {
        margin-bottom: 0.5rem;
        font-weight: 700;
    }
    
    .admin-meta {
        opacity: 0.9;
        font-size: 0.9rem;
    }
    
    .admin-meta div {
        margin-bottom: 0.3rem;
    }
    
    .admin-meta i {
        margin-right: 0.5rem;
        width: 16px;
    }
</style>
@endsection

@section('content')
    <!-- Settings Header -->
    <div class="settings-header fade-in">
        <div class="d-flex align-items-center">
            <div class="settings-icon" style="background: rgba(255,255,255,0.2); margin-right: 1.5rem; margin-bottom: 0;">
                <i class="fas fa-cog"></i>
            </div>
            <div>
                <h1 class="h2 mb-2">⚙️ Admin Settings</h1>
                <p class="mb-0 opacity-75">Manage your account settings and preferences</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Admin Profile Card -->
        <div class="col-md-4">
            <div class="admin-profile-card slide-up" style="animation-delay: 0.1s;">
                <div class="admin-avatar" @if($admin->profile_picture) style="background-image: url('{{ asset('storage/' . $admin->profile_picture) }}')" @endif>
                    @if(!$admin->profile_picture)
                        {{ substr($admin->name, 0, 1) }}
                    @endif
                </div>
                <div class="admin-info">
                    <h4>{{ $admin->name }}</h4>
                    <div class="admin-meta">
                        <div><i class="fas fa-envelope"></i> {{ $admin->email }}</div>
                        @if($admin->phone)
                            <div><i class="fas fa-phone"></i> {{ $admin->phone }}</div>
                        @endif
                        <div><i class="fas fa-shield-alt"></i> Administrator</div>
                        <div><i class="fas fa-calendar"></i> Since {{ $admin->created_at->format('F j, Y') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Options -->
        <div class="col-md-8">
            <div class="row">
                <!-- Profile Settings -->
                <div class="col-md-6">
                    <div class="settings-card slide-up" style="animation-delay: 0.2s;">
                        <div class="settings-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="settings-title">Profile Settings</div>
                        <div class="settings-description">
                            Update your personal information, profile picture, and contact details.
                        </div>
                        <a href="{{ route('admin.settings.profile') }}" class="settings-btn">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                    </div>
                </div>

                <!-- Password Settings -->
                <div class="col-md-6">
                    <div class="settings-card slide-up" style="animation-delay: 0.3s;">
                        <div class="settings-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="settings-title">Password & Security</div>
                        <div class="settings-description">
                            Change your password and manage security settings for your account.
                        </div>
                        <a href="{{ route('admin.settings.password') }}" class="settings-btn">
                            <i class="fas fa-key"></i> Change Password
                        </a>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="col-md-6">
                    <div class="settings-card slide-up" style="animation-delay: 0.4s;">
                        <div class="settings-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="settings-title">System Settings</div>
                        <div class="settings-description">
                            Configure system-wide settings, preferences, and application behavior.
                        </div>
                        <a href="#" class="settings-btn" onclick="alert('Coming Soon!')">
                            <i class="fas fa-tools"></i> System Config
                        </a>
                    </div>
                </div>

                <!-- Reports & Analytics -->
                <div class="col-md-6">
                    <div class="settings-card slide-up" style="animation-delay: 0.5s;">
                        <div class="settings-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="settings-title">Reports & Analytics</div>
                        <div class="settings-description">
                            View detailed reports, analytics, and insights about your store performance.
                        </div>
                        <a href="#" class="settings-btn" onclick="alert('Coming Soon!')">
                            <i class="fas fa-chart-bar"></i> View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
