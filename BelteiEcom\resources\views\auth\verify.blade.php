<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - {{ config('app.name') }}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .verification-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .verification-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
        }

        .verification-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .verification-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .verification-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .verification-body {
            padding: 40px 30px;
        }

        .verification-message {
            font-size: 1.1rem;
            color: #555;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .email-display {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-weight: 600;
            color: #495057;
        }

        .success-alert {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .resend-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .resend-text {
            color: #6c757d;
            margin-bottom: 15px;
        }

        .resend-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .resend-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            margin-top: 20px;
            display: inline-block;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #764ba2;
            text-decoration: none;
        }

        .verification-steps {
            text-align: left;
            margin: 20px 0;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            color: #6c757d;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 15px;
            flex-shrink: 0;
        }

        @media (max-width: 480px) {
            .verification-container {
                margin: 10px;
                border-radius: 15px;
            }

            .verification-header {
                padding: 30px 20px;
            }

            .verification-body {
                padding: 30px 20px;
            }

            .verification-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <div class="verification-icon">
                <i class="fas fa-envelope-circle-check"></i>
            </div>
            <h1 class="verification-title">Check Your Email</h1>
            <p class="verification-subtitle">We've sent you a verification link</p>
        </div>

        <div class="verification-body">
            @if (session('resent'))
                <div class="success-alert">
                    <i class="fas fa-check-circle"></i>
                    <span>A fresh verification link has been sent to your email address!</span>
                </div>
            @endif

            <p class="verification-message">
                We've sent a verification email to:
            </p>

            <div class="email-display">
                <i class="fas fa-envelope"></i> {{ Auth::user()->email }}
            </div>

            <div class="verification-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <span>Check your email inbox (and spam folder)</span>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <span>Click the "Verify Email Address" button</span>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <span>Start shopping on {{ config('app.name') }}!</span>
                </div>
            </div>

            <div class="resend-section">
                <p class="resend-text">
                    <i class="fas fa-clock"></i> Didn't receive the email?
                </p>
                <form method="POST" action="{{ route('verification.resend') }}" style="display: inline;">
                    @csrf
                    <button type="submit" class="resend-btn">
                        <i class="fas fa-paper-plane"></i> Resend Verification Email
                    </button>
                </form>
            </div>

            <a href="{{ route('home') }}" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
    </div>
</body>
</html>
