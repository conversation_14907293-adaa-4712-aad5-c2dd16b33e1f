@extends('admin.layouts.app')

@section('styles')
<style>
    .order-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .order-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .order-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .order-number {
        font-size: 1.2rem;
        font-weight: 700;
        color: #667eea;
    }

    .order-date {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .customer-info {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 1rem;
    }

    .customer-details {
        flex: 1;
    }

    .customer-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.2rem;
    }

    .customer-email {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .order-status {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.8rem;
        margin-bottom: 1rem;
    }

    .status-new {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .status-processing {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: #856404;
    }

    .status-shipped {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .status-delivered {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .order-total {
        font-size: 1.3rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 1rem;
    }

    .order-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn-view {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-view:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-edit-order {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-edit-order:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-cancel {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        color: white;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .filter-tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .filter-tab {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        border: 2px solid #e3e6f0;
        background: white;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .filter-tab:hover, .filter-tab.active {
        background: var(--primary-gradient);
        border-color: transparent;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">🛒 Orders Management</h1>
                <p class="mb-0 opacity-75">Track and manage customer orders</p>
            </div>
            <div>
                <div class="text-right">
                    <div class="h4 mb-1">{{ $orders->total() }} Orders</div>
                    <div class="opacity-75">Total in system</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-cards slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Order::where('status', 'new')->count() }}</div>
            <div class="stat-label">New Orders</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Order::where('status', 'processing')->count() }}</div>
            <div class="stat-label">Processing</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ \App\Models\Order::where('status', 'shipped')->count() }}</div>
            <div class="stat-label">Shipped</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${{ number_format(\App\Models\Order::sum('total_amount'), 2) }}</div>
            <div class="stat-label">Total Revenue</div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs slide-up" style="animation-delay: 0.3s;">
        <a href="{{ route('admin.orders.index') }}" class="filter-tab {{ !request('status') ? 'active' : '' }}">
            <i class="fas fa-list"></i> All Orders
        </a>
        <a href="{{ route('admin.orders.index') }}?status=new" class="filter-tab {{ request('status') == 'new' ? 'active' : '' }}">
            <i class="fas fa-star"></i> New
        </a>
        <a href="{{ route('admin.orders.index') }}?status=processing" class="filter-tab {{ request('status') == 'processing' ? 'active' : '' }}">
            <i class="fas fa-cog"></i> Processing
        </a>
        <a href="{{ route('admin.orders.index') }}?status=shipped" class="filter-tab {{ request('status') == 'shipped' ? 'active' : '' }}">
            <i class="fas fa-truck"></i> Shipped
        </a>
        <a href="{{ route('admin.orders.index') }}?status=delivered" class="filter-tab {{ request('status') == 'delivered' ? 'active' : '' }}">
            <i class="fas fa-check"></i> Delivered
        </a>
    </div>

    <!-- Orders List -->
    <div class="slide-up" style="animation-delay: 0.4s;">
        @if($orders->count() > 0)
            @foreach($orders as $order)
                <div class="order-card">
                    <div class="order-header">
                        <div class="order-number">Order #{{ $order->id }}</div>
                        <div class="order-date">{{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('M j, Y g:i A') }} (Cambodia Time)</div>
                    </div>

                    <div class="customer-info">
                        <div class="customer-avatar">
                            {{ substr($order->user->name, 0, 1) }}
                        </div>
                        <div class="customer-details">
                            <div class="customer-name">{{ $order->user->name }}</div>
                            <div class="customer-email">{{ $order->user->email }}</div>
                        </div>
                        <div class="order-status status-{{ $order->status }}">
                            @switch($order->status)
                                @case('new')
                                    <i class="fas fa-star"></i> New
                                    @break
                                @case('processing')
                                    <i class="fas fa-cog fa-spin"></i> Processing
                                    @break
                                @case('shipped')
                                    <i class="fas fa-truck"></i> Shipped
                                    @break
                                @case('delivered')
                                    <i class="fas fa-check"></i> Delivered
                                    @break
                                @case('cancelled')
                                    <i class="fas fa-times"></i> Cancelled
                                    @break
                            @endswitch
                        </div>
                    </div>

                    <div class="order-total">
                        <i class="fas fa-dollar-sign"></i> {{ number_format($order->total_amount, 2) }}
                    </div>

                    <div class="order-actions">
                        <a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-view btn-sm" title="View Order">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="{{ route('admin.orders.edit', $order->id) }}" class="btn btn-edit-order btn-sm" title="Edit Order">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        @if(!in_array($order->status, ['delivered', 'cancelled']))
                            <form action="{{ route('admin.orders.cancel', $order->id) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-cancel btn-sm" onclick="return confirm('Are you sure you want to cancel this order?')" title="Cancel Order">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            @endforeach

            <div class="mt-4 d-flex justify-content-center">
                {{ $orders->links() }}
            </div>
        @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">No orders found</h5>
                    <p class="text-gray-400">Orders will appear here when customers make purchases.</p>
                </div>
            </div>
        @endif
    </div>
@endsection
