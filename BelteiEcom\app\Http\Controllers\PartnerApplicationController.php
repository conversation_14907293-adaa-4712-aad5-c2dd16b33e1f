<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use App\Models\ApiPartner;
use App\Mail\PartnerApplicationReceived;

class PartnerApplicationController extends Controller
{
    /**
     * Show partner application form
     */
    public function showForm()
    {
        return view('api.partner-application');
    }

    /**
     * Submit partner application
     */
    public function submitApplication(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'contact_name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:api_partners,email',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'business_description' => 'required|string|min:50',
            'expected_volume' => 'nullable|string',
            'integration_timeline' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Generate API credentials (but keep them inactive until approved)
            $credentials = ApiPartner::generateCredentials();

            // Create partner application
            $partner = ApiPartner::create([
                'company_name' => $request->company_name,
                'contact_name' => $request->contact_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'website' => $request->website,
                'business_description' => $request->business_description,
                'api_key' => $credentials['api_key'],
                'api_secret' => $credentials['api_secret'],
                'status' => 'pending',
                'tier' => 'basic',
                'commission_rate' => 10.00, // Default commission
                'settings' => [
                    'expected_volume' => $request->expected_volume,
                    'integration_timeline' => $request->integration_timeline,
                    'application_date' => now()->toDateString(),
                ],
            ]);

            // Send confirmation email to applicant
            Mail::to($partner->email)->send(new PartnerApplicationReceived($partner));

            // Notify admins (you can implement this)
            // Mail::to('<EMAIL>')->send(new NewPartnerApplication($partner));

            return redirect()->route('partner.application.success')
                ->with('success', 'Your partner application has been submitted successfully!');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to submit application. Please try again.')
                ->withInput();
        }
    }

    /**
     * Show application success page
     */
    public function applicationSuccess()
    {
        return view('api.application-success');
    }

    /**
     * Show partner dashboard (for approved partners)
     */
    public function dashboard()
    {
        $user = Auth::user();

        if (!$user->isPartner()) {
            if ($user->hasPendingPartnerApplication()) {
                return view('api.application-pending');
            }

            return redirect()->route('api.partner-application')
                ->with('info', 'You need to apply for partner access first.');
        }

        $partner = $user->partner;

        // Get partner statistics
        $stats = [
            'total_orders' => $partner->total_orders,
            'total_sales' => $partner->total_sales,
            'total_commission' => $partner->total_commission,
            'commission_rate' => $partner->commission_rate,
            'api_calls_today' => 0, // You can implement this
            'recent_orders' => $partner->orders()->latest()->limit(5)->get(),
        ];

        return view('api.partner-dashboard', compact('partner', 'stats'));
    }
}
