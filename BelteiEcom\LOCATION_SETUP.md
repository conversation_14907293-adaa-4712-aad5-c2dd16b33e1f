# 🌍 Location Services Setup Guide

## Overview
The checkout page now includes advanced location services for shipping addresses with multiple input methods:

- **🔍 Address Search**: Autocomplete search with suggestions
- **📍 Current Location**: GPS-based location detection
- **🗺️ Pin on Map**: Interactive map for precise location selection
- **✏️ Manual Entry**: Traditional text input as fallback

## 🚀 Quick Setup

### 1. Get Free Mapbox API Key (Recommended)

1. **Sign up** at [https://www.mapbox.com/](https://www.mapbox.com/)
2. **Go to Account** → **Access Tokens**
3. **Copy your default public token** (starts with `pk.`)
4. **Update the token** in `/resources/views/orders/checkout.blade.php`:

```javascript
const MAPBOX_ACCESS_TOKEN = 'pk.your_actual_token_here';
```

**Free Tier**: 100,000 requests/month (very generous!)

### 2. Alternative: OpenStreetMap (Completely Free)

If you prefer a completely free solution, the system automatically falls back to OpenStreetMap's Nominatim service when Mapbox fails.

## 🎯 Features

### Address Search
- **Real-time suggestions** as user types
- **Fallback to OpenStreetMap** if Mapbox fails
- **Cambodia-focused results** for better accuracy

### Current Location
- **GPS detection** with user permission
- **Automatic address lookup** from coordinates
- **Error handling** for permission denied/unavailable

### Interactive Map
- **Click to pin location** on map
- **Automatic address resolution** from coordinates
- **Visual marker** for selected location

### Manual Entry
- **Traditional textarea** for manual address input
- **Always available** as fallback option

## 🔧 Technical Details

### Database Changes
- Added `latitude` and `longitude` fields to orders table
- Coordinates are optional and stored as decimal(10,8) and decimal(11,8)

### API Endpoints Used
1. **Mapbox Geocoding**: `https://api.mapbox.com/geocoding/v5/mapbox.places/`
2. **OpenStreetMap Nominatim**: `https://nominatim.openstreetmap.org/`

### Browser Permissions
- **Geolocation API**: Requires user permission for current location
- **HTTPS Required**: Geolocation only works on secure connections

## 🛡️ Privacy & Security

- **No tracking**: Location data only used for shipping
- **User consent**: GPS location requires explicit permission
- **Optional coordinates**: System works without location data
- **Secure storage**: Coordinates stored securely in database

## 🌐 Supported Countries

- **Primary**: Cambodia (KH country code)
- **Fallback**: Worldwide through OpenStreetMap

## 📱 Mobile Support

- **Responsive design**: Works on all device sizes
- **Touch-friendly**: Optimized for mobile interaction
- **GPS integration**: Native mobile GPS support

## 🔄 Fallback Strategy

1. **Mapbox** (primary, 100k requests/month free)
2. **OpenStreetMap** (secondary, unlimited free)
3. **Manual entry** (always available)

## 🚨 Troubleshooting

### Common Issues

1. **"Map service not configured"**
   - Update MAPBOX_ACCESS_TOKEN in checkout.blade.php

2. **"Location access denied"**
   - User denied GPS permission
   - Use search or manual entry instead

3. **"Search failed"**
   - Network issue or API limit reached
   - System automatically tries OpenStreetMap fallback

### Testing

1. **Test address search**: Type "Phnom Penh" in search box
2. **Test current location**: Click "Use Current Location" (requires HTTPS)
3. **Test map pin**: Click "Pin on Map" and click anywhere on map
4. **Test manual entry**: Click "Manual Entry" and type address

## 📊 Usage Analytics

The system logs location method usage:
- Search queries and results
- GPS location requests
- Map pin selections
- Manual entries

This helps optimize the user experience based on actual usage patterns.
