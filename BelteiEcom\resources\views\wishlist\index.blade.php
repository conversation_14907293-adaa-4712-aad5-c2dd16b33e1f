@extends('layouts.app')

@section('title', 'My Wishlist')

@section('content')
<div class="wishlist-page">
    <!-- Wishlist Header -->
    <div class="wishlist-header">
        <div class="container">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">
                        <i class="fas fa-heart"></i>
                        My Wishlist
                    </h1>
                    <p class="page-subtitle">
                        Save your favorite products and never lose track of what you love
                    </p>
                </div>
                <div class="header-stats">
                    <div class="stat-card">
                        <div class="stat-number">{{ $wishlistItems->total() }}</div>
                        <div class="stat-label">{{ Str::plural('Item', $wishlistItems->total()) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wishlist Content -->
    <div class="wishlist-content">
        <div class="container">
            @if($wishlistItems->count() > 0)
                <!-- Wishlist Actions -->
                <div class="wishlist-actions">
                    <div class="actions-left">
                        <span class="items-count">{{ $wishlistItems->total() }} {{ Str::plural('item', $wishlistItems->total()) }} in your wishlist</span>
                    </div>
                    <div class="actions-right">
                        <form action="{{ route('wishlist.clear') }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to clear your entire wishlist?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn-clear">
                                <i class="fas fa-trash"></i> Clear All
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Wishlist Grid -->
                <div class="wishlist-grid">
                    @foreach($wishlistItems as $item)
                        <div class="wishlist-item" data-product-id="{{ $item->product->id }}">
                            <div class="product-card">
                                <!-- Product Image -->
                                <div class="product-image">
                                    <a href="{{ route('products.show', $item->product->slug) }}">
                                        @if($item->product->image)
                                            <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product->name }}">
                                        @else
                                            <div class="no-image">
                                                <i class="fas fa-image"></i>
                                                <span>No Image</span>
                                            </div>
                                        @endif
                                    </a>
                                    
                                    <!-- Remove from Wishlist -->
                                    <button class="remove-wishlist" onclick="removeFromWishlist({{ $item->product->id }})">
                                        <i class="fas fa-times"></i>
                                    </button>

                                    <!-- Product Badge -->
                                    @if($item->product->stock_quantity <= 5 && $item->product->stock_quantity > 0)
                                        <div class="product-badge low-stock">Low Stock</div>
                                    @elseif($item->product->stock_quantity == 0)
                                        <div class="product-badge out-of-stock">Out of Stock</div>
                                    @endif
                                </div>

                                <!-- Product Info -->
                                <div class="product-info">
                                    <div class="product-category">{{ $item->product->category->name ?? 'Uncategorized' }}</div>
                                    <h3 class="product-name">
                                        <a href="{{ route('products.show', $item->product->slug) }}">{{ $item->product->name }}</a>
                                    </h3>
                                    
                                    <!-- Product Rating -->
                                    @if($item->product->review_count > 0)
                                        <div class="product-rating">
                                            {!! $item->product->stars_html !!}
                                            <span class="rating-count">({{ $item->product->review_count }})</span>
                                        </div>
                                    @endif

                                    <!-- Product Price -->
                                    <div class="product-price">
                                        <span class="current-price">${{ number_format($item->product->price, 2) }}</span>
                                        @if($item->product->original_price && $item->product->original_price > $item->product->price)
                                            <span class="original-price">${{ number_format($item->product->original_price, 2) }}</span>
                                            <span class="discount">
                                                {{ round((($item->product->original_price - $item->product->price) / $item->product->original_price) * 100) }}% OFF
                                            </span>
                                        @endif
                                    </div>

                                    <!-- Added Date -->
                                    <div class="added-date">
                                        <i class="fas fa-heart"></i>
                                        Added {{ $item->added_at->diffForHumans() }}
                                    </div>
                                </div>

                                <!-- Product Actions -->
                                <div class="product-actions">
                                    @if($item->product->stock_quantity > 0)
                                        <button class="btn-add-cart" onclick="addToCart({{ $item->product->id }})">
                                            <i class="fas fa-shopping-cart"></i>
                                            Add to Cart
                                        </button>
                                    @else
                                        <button class="btn-notify" onclick="notifyWhenAvailable({{ $item->product->id }})">
                                            <i class="fas fa-bell"></i>
                                            Notify When Available
                                        </button>
                                    @endif
                                    
                                    <a href="{{ route('products.show', $item->product->slug) }}" class="btn-view">
                                        <i class="fas fa-eye"></i>
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($wishlistItems->hasPages())
                    <div class="wishlist-pagination">
                        {{ $wishlistItems->links() }}
                    </div>
                @endif

            @else
                <!-- Empty Wishlist -->
                <div class="empty-wishlist">
                    <div class="empty-icon">
                        <i class="fas fa-heart-broken"></i>
                    </div>
                    <h2 class="empty-title">Your wishlist is empty</h2>
                    <p class="empty-text">
                        Start adding products you love to your wishlist. It's a great way to keep track of items you want to buy later.
                    </p>
                    <a href="{{ route('products.index') }}" class="btn-shop">
                        <i class="fas fa-shopping-bag"></i>
                        Start Shopping
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Toast Notification -->
<div id="toast" class="toast"></div>

<style>
/* Professional Wishlist Styling */
.wishlist-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.wishlist-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 3rem;
    position: relative;
    overflow: hidden;
}

.wishlist-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,6 C10,3 7,1 4,3 C1,1 -2,3 -2,6 C-2,10 10,18 10,18 C10,18 22,10 22,6 C22,3 19,1 16,3 C13,1 10,3 10,6 Z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>') repeat;
    opacity: 0.1;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0;
}

.stat-card {
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.3);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    margin-top: 0.5rem;
}

.wishlist-content {
    padding: 4rem 0;
}

.wishlist-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
}

.items-count {
    font-weight: 600;
    color: #2d3436;
}

.btn-clear {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-clear:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.wishlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.wishlist-item {
    transition: all 0.3s ease;
}

.product-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.no-image {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #636e72;
}

.remove-wishlist {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.remove-wishlist:hover {
    background: #dc3545;
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.low-stock {
    background: #ffc107;
    color: #2d3436;
}

.out-of-stock {
    background: #dc3545;
    color: white;
}

.product-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-category {
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.product-name a {
    color: #2d3436;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-name a:hover {
    color: #667eea;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-count {
    color: #636e72;
    font-size: 0.9rem;
}

.product-price {
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: #667eea;
}

.original-price {
    font-size: 1rem;
    color: #636e72;
    text-decoration: line-through;
    margin-left: 0.5rem;
}

.discount {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.added-date {
    color: #636e72;
    font-size: 0.9rem;
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.added-date i {
    color: #dc3545;
}

.product-actions {
    padding: 1.5rem;
    border-top: 1px solid #f1f3f4;
    display: flex;
    gap: 1rem;
}

.btn-add-cart, .btn-notify, .btn-view {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-add-cart {
    background: #667eea;
    color: white;
}

.btn-add-cart:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-notify {
    background: #ffc107;
    color: #2d3436;
}

.btn-notify:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.btn-view {
    background: #f8f9fa;
    color: #2d3436;
    border: 1px solid #e9ecef;
}

.btn-view:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    text-decoration: none;
    color: #2d3436;
}

/* Empty Wishlist */
.empty-wishlist {
    text-align: center;
    padding: 5rem 2rem;
}

.empty-icon {
    font-size: 5rem;
    color: #e9ecef;
    margin-bottom: 2rem;
}

.empty-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3436;
    margin-bottom: 1rem;
}

.empty-text {
    font-size: 1.1rem;
    color: #636e72;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.btn-shop {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-shop:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    text-decoration: none;
    color: white;
}

/* Toast Notification */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 1000;
    font-weight: 600;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wishlist-header {
        padding: 2rem 0;
    }
    
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .wishlist-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .wishlist-actions {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .product-actions {
        flex-direction: column;
    }
}
</style>

<script>
// Wishlist JavaScript Functions
function removeFromWishlist(productId) {
    fetch('{{ route("wishlist.toggle") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the item from the page
            const item = document.querySelector(`[data-product-id="${productId}"]`);
            if (item) {
                item.style.transform = 'scale(0)';
                item.style.opacity = '0';
                setTimeout(() => {
                    item.remove();
                    // Check if wishlist is empty
                    if (document.querySelectorAll('.wishlist-item').length === 0) {
                        location.reload();
                    }
                }, 300);
            }
            
            showToast(data.message, 'success');
            updateWishlistCount(data.wishlist_count);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    });
}

function addToCart(productId) {
    // This would integrate with your cart system
    showToast('Added to cart! (Cart integration needed)', 'success');
}

function notifyWhenAvailable(productId) {
    // This would set up notifications for when product is back in stock
    showToast('You will be notified when this product is available!', 'success');
}

function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type} show`;
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

function updateWishlistCount(count) {
    // Update wishlist count in header if it exists
    const wishlistCounters = document.querySelectorAll('.wishlist-count');
    wishlistCounters.forEach(counter => {
        counter.textContent = count;
    });
}
</script>
@endsection
