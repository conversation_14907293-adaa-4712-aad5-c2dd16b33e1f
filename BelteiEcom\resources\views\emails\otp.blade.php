<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Login Code</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
            line-height: 1.6;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .email-header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .email-header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .email-body {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }

        .otp-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
            border: 2px solid #e9ecef;
        }

        .otp-label {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .otp-code {
            font-size: 36px;
            font-weight: 800;
            color: #667eea;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 20px 30px;
            border-radius: 10px;
            border: 3px solid #667eea;
            display: inline-block;
            margin-bottom: 15px;
        }

        .otp-expiry {
            font-size: 14px;
            color: #dc3545;
            font-weight: 600;
        }

        .message-text {
            font-size: 16px;
            color: #555;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }

        .security-notice h3 {
            color: #856404;
            font-size: 16px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .security-notice p {
            color: #856404;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .email-footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .email-footer p {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .company-info {
            color: #999;
            font-size: 12px;
        }

        .icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            vertical-align: middle;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px;
            }
            
            .otp-section {
                padding: 20px;
            }
            
            .otp-code {
                font-size: 28px;
                letter-spacing: 4px;
                padding: 15px 20px;
            }
            
            .email-header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1>🔐 Login Code</h1>
            <p>{{ config('app.name') }} - Secure Access</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div class="greeting">
                Hello{{ $userName ? ', ' . $userName : '' }}!
            </div>

            <p class="message-text">
                You requested a one-time code to log in to your {{ config('app.name') }} account. 
                Use the code below to complete your login:
            </p>

            <!-- OTP Section -->
            <div class="otp-section">
                <div class="otp-label">Your Login Code</div>
                <div class="otp-code">{{ $code }}</div>
                <div class="otp-expiry">⏰ Expires in 1 minute</div>
            </div>

            <p class="message-text">
                Enter this code on the login page to access your account. 
                If you didn't request this code, please ignore this email.
            </p>

            <!-- Security Notice -->
            <div class="security-notice">
                <h3>🛡️ Security Notice</h3>
                <p>• This code is valid for only 1 minute</p>
                <p>• Never share this code with anyone</p>
                <p>• {{ config('app.name') }} will never ask for this code via phone or email</p>
                <p>• If you didn't request this, someone may be trying to access your account</p>
            </div>

            <p class="message-text">
                <strong>Need help?</strong> Contact our support team if you have any questions or concerns.
            </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>This is an automated message from {{ config('app.name') }}.</p>
            <p>Please do not reply to this email.</p>
            
            <div class="company-info">
                <p>{{ config('app.name') }} &copy; {{ date('Y') }}</p>
                <p>Sent at {{ $expiresAt->subMinute()->format('M d, Y \a\t g:i A') }}</p>
            </div>
        </div>
    </div>
</body>
</html>
