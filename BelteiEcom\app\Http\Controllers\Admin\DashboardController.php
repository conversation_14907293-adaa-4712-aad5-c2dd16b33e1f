<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get basic stats
        $totalSales = Order::sum('total_amount');
        $totalOrders = Order::count();
        $totalProducts = Product::count();
        $totalCustomers = User::where('is_admin', false)->count();

        // Get recent orders
        $recentOrders = Order::with('user')
                            ->orderBy('created_at', 'desc')
                            ->limit(5)
                            ->get();

        // Get popular products (most ordered)
        $popularProducts = DB::table('order_items')
                            ->select('product_id', DB::raw('SUM(quantity) as total_quantity'))
                            ->groupBy('product_id')
                            ->orderBy('total_quantity', 'desc')
                            ->limit(5)
                            ->get()
                            ->map(function ($item) {
                                $product = Product::find($item->product_id);
                                if ($product) {
                                    $product->total_quantity = $item->total_quantity;
                                    return $product;
                                }
                                return null;
                            })
                            ->filter();

        // Chart Data: Sales Trend (Last 7 days)
        $salesTrend = $this->getSalesTrendData();

        // Chart Data: Order Status Distribution
        $orderStatusData = $this->getOrderStatusData();

        // Chart Data: Monthly Revenue (Last 6 months)
        $monthlyRevenue = $this->getMonthlyRevenueData();

        // Chart Data: Product Categories Performance
        $categoryPerformance = $this->getCategoryPerformanceData();

        // Chart Data: Customer Growth (Last 12 months)
        $customerGrowth = $this->getCustomerGrowthData();

        // Additional metrics
        $todaySales = Order::whereDate('created_at', Carbon::today())->sum('total_amount');
        $todayOrders = Order::whereDate('created_at', Carbon::today())->count();
        $monthSales = Order::whereMonth('created_at', Carbon::now()->month)
                          ->whereYear('created_at', Carbon::now()->year)
                          ->sum('total_amount');
        $lowStockProducts = Product::where('stock', '<=', 10)->count();

        return view('admin.dashboard.index', compact(
            'totalSales',
            'totalOrders',
            'totalProducts',
            'totalCustomers',
            'recentOrders',
            'popularProducts',
            'salesTrend',
            'orderStatusData',
            'monthlyRevenue',
            'categoryPerformance',
            'customerGrowth',
            'todaySales',
            'todayOrders',
            'monthSales',
            'lowStockProducts'
        ));
    }

    /**
     * Get sales trend data for the last 7 days
     */
    private function getSalesTrendData()
    {
        $dates = [];
        $sales = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dates[] = $date->format('M d');
            $sales[] = Order::whereDate('created_at', $date)->sum('total_amount');
        }

        return [
            'labels' => $dates,
            'data' => $sales
        ];
    }

    /**
     * Get order status distribution data
     */
    private function getOrderStatusData()
    {
        $statusData = Order::select('status', DB::raw('count(*) as count'))
                          ->groupBy('status')
                          ->get();

        $labels = [];
        $data = [];
        $colors = [
            'new' => '#17a2b8',
            'processing' => '#ffc107',
            'shipped' => '#007bff',
            'delivered' => '#28a745',
            'cancelled' => '#dc3545'
        ];
        $backgroundColors = [];

        foreach ($statusData as $status) {
            $labels[] = ucfirst($status->status);
            $data[] = $status->count;
            $backgroundColors[] = $colors[$status->status] ?? '#6c757d';
        }

        return [
            'labels' => $labels,
            'data' => $data,
            'colors' => $backgroundColors
        ];
    }

    /**
     * Get monthly revenue data for the last 6 months
     */
    private function getMonthlyRevenueData()
    {
        $months = [];
        $revenue = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
            $revenue[] = Order::whereMonth('created_at', $date->month)
                             ->whereYear('created_at', $date->year)
                             ->sum('total_amount');
        }

        return [
            'labels' => $months,
            'data' => $revenue
        ];
    }

    /**
     * Get category performance data
     */
    private function getCategoryPerformanceData()
    {
        $categoryData = DB::table('order_items')
                         ->join('products', 'order_items.product_id', '=', 'products.id')
                         ->join('categories', 'products.category_id', '=', 'categories.id')
                         ->select('categories.name', DB::raw('SUM(order_items.quantity * order_items.price) as total_sales'))
                         ->groupBy('categories.id', 'categories.name')
                         ->orderBy('total_sales', 'desc')
                         ->get();

        $labels = [];
        $data = [];
        $colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];

        foreach ($categoryData as $index => $category) {
            $labels[] = $category->name;
            $data[] = $category->total_sales;
        }

        return [
            'labels' => $labels,
            'data' => $data,
            'colors' => array_slice($colors, 0, count($labels))
        ];
    }

    /**
     * Get customer growth data for the last 12 months
     */
    private function getCustomerGrowthData()
    {
        $months = [];
        $customers = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
            $customers[] = User::where('is_admin', false)
                              ->whereMonth('created_at', $date->month)
                              ->whereYear('created_at', $date->year)
                              ->count();
        }

        return [
            'labels' => $months,
            'data' => $customers
        ];
    }
}
