<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Models\ApiPartner;
use App\Mail\PartnerApplicationApproved;
use App\Mail\PartnerApplicationRejected;

class PartnerManagementController extends Controller
{
    /**
     * Display partner applications
     */
    public function index(Request $request)
    {
        $query = ApiPartner::query();

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('contact_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $partners = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.partners.index', compact('partners'));
    }

    /**
     * Show partner details
     */
    public function show(ApiPartner $partner)
    {
        return view('admin.partners.show', compact('partner'));
    }

    /**
     * Approve partner application
     */
    public function approve(Request $request, ApiPartner $partner)
    {
        $request->validate([
            'commission_rate' => 'required|numeric|min:0|max:50',
            'tier' => 'required|in:basic,premium,enterprise',
            'allowed_categories' => 'nullable|array',
            'minimum_order' => 'nullable|numeric|min:0',
        ]);

        $partner->update([
            'status' => 'approved',
            'tier' => $request->tier,
            'commission_rate' => $request->commission_rate,
            'minimum_order' => $request->minimum_order ?? 0,
            'allowed_categories' => $request->allowed_categories,
            'approved_at' => now(),
        ]);

        // Send approval email
        Mail::to($partner->email)->send(new PartnerApplicationApproved($partner));

        return redirect()->route('admin.partners.index')
            ->with('success', "Partner application for {$partner->company_name} has been approved!");
    }

    /**
     * Reject partner application
     */
    public function reject(Request $request, ApiPartner $partner)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $partner->update([
            'status' => 'rejected',
            'settings' => array_merge($partner->settings ?? [], [
                'rejection_reason' => $request->rejection_reason,
                'rejected_at' => now()->toDateTimeString(),
            ]),
        ]);

        // Send rejection email
        Mail::to($partner->email)->send(new PartnerApplicationRejected($partner, $request->rejection_reason));

        return redirect()->route('admin.partners.index')
            ->with('success', "Partner application for {$partner->company_name} has been rejected.");
    }

    /**
     * Suspend partner
     */
    public function suspend(Request $request, ApiPartner $partner)
    {
        $request->validate([
            'suspension_reason' => 'required|string|max:1000',
        ]);

        $partner->update([
            'status' => 'suspended',
            'settings' => array_merge($partner->settings ?? [], [
                'suspension_reason' => $request->suspension_reason,
                'suspended_at' => now()->toDateTimeString(),
            ]),
        ]);

        return redirect()->route('admin.partners.index')
            ->with('success', "Partner {$partner->company_name} has been suspended.");
    }

    /**
     * Reactivate partner
     */
    public function reactivate(ApiPartner $partner)
    {
        $partner->update([
            'status' => 'approved',
            'settings' => array_merge($partner->settings ?? [], [
                'reactivated_at' => now()->toDateTimeString(),
            ]),
        ]);

        return redirect()->route('admin.partners.index')
            ->with('success', "Partner {$partner->company_name} has been reactivated.");
    }

    /**
     * Delete partner
     */
    public function destroy(ApiPartner $partner)
    {
        $companyName = $partner->company_name;
        $partner->delete();

        return redirect()->route('admin.partners.index')
            ->with('success', "Partner {$companyName} has been deleted.");
    }

    /**
     * Regenerate API credentials
     */
    public function regenerateCredentials(ApiPartner $partner)
    {
        $credentials = ApiPartner::generateCredentials();

        $partner->update([
            'api_key' => $credentials['api_key'],
            'api_secret' => $credentials['api_secret'],
        ]);

        return redirect()->route('admin.partners.show', $partner)
            ->with('success', 'API credentials have been regenerated.');
    }
}
