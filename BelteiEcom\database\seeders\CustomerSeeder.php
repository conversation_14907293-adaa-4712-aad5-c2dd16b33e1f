<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0101',
                'address' => '123 Main Street, New York, NY 10001',
                'is_admin' => false,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0102',
                'address' => '456 Oak Avenue, Los Angeles, CA 90210',
                'is_admin' => false,
            ],
            [
                'name' => '<PERSON>',
                'email' => 'micha<PERSON>.<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0103',
                'address' => '789 Pine Road, Chicago, IL 60601',
                'is_admin' => false,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0104',
                'address' => '321 Elm Street, Houston, TX 77001',
                'is_admin' => false,
            ],
            [
                'name' => 'David Wilson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0105',
                'address' => '654 Maple Drive, Phoenix, AZ 85001',
                'is_admin' => false,
            ],
            [
                'name' => 'Jessica Miller',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0106',
                'address' => '987 Cedar Lane, Philadelphia, PA 19101',
                'is_admin' => false,
            ],
            [
                'name' => 'Christopher Garcia',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0107',
                'address' => '147 Birch Boulevard, San Antonio, TX 78201',
                'is_admin' => false,
            ],
            [
                'name' => 'Amanda Rodriguez',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0108',
                'address' => '258 Spruce Street, San Diego, CA 92101',
                'is_admin' => false,
            ],
            [
                'name' => 'Matthew Martinez',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0109',
                'address' => '369 Willow Way, Dallas, TX 75201',
                'is_admin' => false,
            ],
            [
                'name' => 'Ashley Anderson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0110',
                'address' => '741 Poplar Place, San Jose, CA 95101',
                'is_admin' => false,
            ],
            [
                'name' => 'James Taylor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0111',
                'address' => '852 Hickory Hill, Austin, TX 73301',
                'is_admin' => false,
            ],
            [
                'name' => 'Stephanie Thomas',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0112',
                'address' => '963 Walnut Walk, Jacksonville, FL 32099',
                'is_admin' => false,
            ],
            [
                'name' => 'Daniel Jackson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0113',
                'address' => '159 Chestnut Circle, Fort Worth, TX 76101',
                'is_admin' => false,
            ],
            [
                'name' => 'Nicole White',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0114',
                'address' => '357 Sycamore Square, Columbus, OH 43085',
                'is_admin' => false,
            ],
            [
                'name' => 'Ryan Harris',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '******-0115',
                'address' => '486 Dogwood Drive, Charlotte, NC 28201',
                'is_admin' => false,
            ],
        ];

        foreach ($customers as $customer) {
            User::create($customer);
        }
    }
}
