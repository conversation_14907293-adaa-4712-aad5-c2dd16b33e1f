@extends('layouts.app')

@section('title', 'Confirmation Link Expired')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0" style="border-radius: 20px; overflow: hidden;">
                <!-- Header -->
                <div class="card-header text-center" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%); color: white; padding: 3rem 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">⏰</div>
                    <h1 style="font-size: 2.5rem; font-weight: 700; margin: 0;">Link Expired</h1>
                    <p style="font-size: 1.2rem; margin: 1rem 0 0 0; opacity: 0.9;">Confirmation time limit exceeded</p>
                </div>

                <!-- Content -->
                <div class="card-body text-center" style="padding: 3rem 2rem;">
                    <div style="margin-bottom: 2rem;">
                        <div style="background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-clock" style="font-size: 2rem; color: white;"></i>
                        </div>
                        
                        <h3 style="color: #2c3e50; margin-bottom: 1rem; font-weight: 600;">Confirmation Link Has Expired</h3>
                        
                        <p style="color: #6c757d; font-size: 1.1rem; line-height: 1.6; margin-bottom: 2rem;">
                            The confirmation link for <strong>{{ $newsletter->email }}</strong> has expired.
                            <br><br>
                            For security reasons, confirmation links are only valid for 24 hours after subscription.
                            Don't worry - you can easily get a new confirmation email!
                        </p>
                    </div>

                    <!-- Resend Section -->
                    <div style="background: linear-gradient(135deg, rgba(255, 154, 86, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%); padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                        <h5 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-paper-plane"></i> Get a New Confirmation Email
                        </h5>
                        <p style="color: #6c757d; margin-bottom: 1.5rem;">
                            Click the button below to receive a fresh confirmation email with a new 24-hour link.
                        </p>
                        
                        <button onclick="resendConfirmation()" class="btn btn-warning" style="background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600; color: white;" id="resendBtn">
                            <i class="fas fa-envelope"></i> Resend Confirmation Email
                        </button>
                    </div>

                    <!-- Alternative Subscription -->
                    <div style="background: #f8f9fc; padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                        <h6 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-redo"></i> Or Subscribe Again
                        </h6>
                        <p style="color: #6c757d; margin-bottom: 1.5rem; font-size: 0.9rem;">
                            If you prefer, you can start fresh with a new subscription:
                        </p>
                        
                        <form id="resubscribeForm" style="max-width: 400px; margin: 0 auto; display: flex; gap: 1rem;">
                            @csrf
                            <input type="email" name="email" value="{{ $newsletter->email }}" class="form-control" required style="border-radius: 25px; border: 2px solid #e9ecef; padding: 0.75rem 1.5rem;">
                            <button type="submit" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 25px; padding: 0.75rem 1.5rem; white-space: nowrap;">
                                <i class="fas fa-plus"></i> Subscribe
                            </button>
                        </form>
                    </div>

                    <!-- Action Buttons -->
                    <div style="margin-top: 2rem;">
                        <a href="{{ route('home') }}" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600; margin-right: 1rem; text-decoration: none;">
                            <i class="fas fa-home"></i> Back to Store
                        </a>
                        
                        <a href="{{ route('products.index') }}" class="btn btn-outline-primary" style="border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600;">
                            <i class="fas fa-shopping-bag"></i> Browse Products
                        </a>
                    </div>

                    <!-- Info Box -->
                    <div style="margin-top: 3rem; padding: 1.5rem; background: rgba(102, 126, 234, 0.1); border-radius: 15px;">
                        <h6 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-info-circle"></i> Why Do Links Expire?
                        </h6>
                        <p style="color: #6c757d; font-size: 0.9rem; margin: 0; text-align: left;">
                            We use time-limited confirmation links to protect your email address from unauthorized subscriptions. 
                            This security measure ensures that only you can confirm your newsletter subscription.
                        </p>
                    </div>

                    <!-- Contact Info -->
                    <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <p style="color: #6c757d; font-size: 0.9rem; margin: 0;">
                            Need assistance? Contact our support team at 
                            <a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Resend confirmation email
function resendConfirmation() {
    const button = document.getElementById('resendBtn');
    const originalText = button.innerHTML;
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    button.disabled = true;
    
    // Send request to resend confirmation
    fetch('{{ route("newsletter.subscribe") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            email: '{{ $newsletter->email }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ New confirmation email sent! Please check your inbox.');
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Something went wrong. Please try again.');
    })
    .finally(() => {
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Handle resubscribe form
document.getElementById('resubscribeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const email = form.querySelector('input[name="email"]').value;
    const button = form.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
    button.disabled = true;
    
    // Send subscription request
    fetch('{{ route("newsletter.subscribe") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': form.querySelector('input[name="_token"]').value
        },
        body: JSON.stringify({
            email: email
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Something went wrong. Please try again.');
    })
    .finally(() => {
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
});
</script>
@endsection
