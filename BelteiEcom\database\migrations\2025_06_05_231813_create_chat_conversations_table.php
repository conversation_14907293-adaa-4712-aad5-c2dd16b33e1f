<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_conversations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade'); // Customer
            $table->string('guest_name')->nullable(); // For guest users
            $table->string('guest_email')->nullable(); // For guest users
            $table->string('session_id')->nullable(); // For guest identification
            $table->foreignId('agent_id')->nullable()->constrained('users')->onDelete('set null'); // Support agent
            $table->enum('status', ['waiting', 'active', 'closed', 'transferred'])->default('waiting');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->string('subject')->nullable();
            $table->text('initial_message')->nullable();
            $table->json('customer_info')->nullable(); // Browser, IP, etc.
            $table->timestamp('started_at')->nullable();
            $table->timestamp('ended_at')->nullable();
            $table->timestamp('last_activity_at')->useCurrent();
            $table->integer('rating')->nullable(); // 1-5 customer satisfaction
            $table->text('feedback')->nullable(); // Customer feedback
            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['agent_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index('session_id');
            $table->index('last_activity_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_conversations');
    }
};
