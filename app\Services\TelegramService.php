<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class TelegramService
{
    protected $botToken;
    protected $chatId;

    public function __construct()
    {
        // These would be set in the .env file in a real application
        $this->botToken = env('TELEGRAM_BOT_TOKEN', '');
        $this->chatId = env('TELEGRAM_CHAT_ID', '');
    }

    /**
     * Set the bot token
     *
     * @param string $botToken The bot token
     * @return void
     */
    public function setBotToken($botToken)
    {
        $this->botToken = $botToken;
    }

    /**
     * Set the chat ID
     *
     * @param string $chatId The chat ID
     * @return void
     */
    public function setChatId($chatId)
    {
        $this->chatId = $chatId;
    }

    /**
     * Send a message to Telegram
     *
     * @param string $message The message to send
     * @return bool Whether the message was sent successfully
     */
    public function sendMessage($message)
    {
        try {
            // Check if bot token and chat ID are set
            if (empty($this->botToken) || empty($this->chatId)) {
                Log::warning('Telegram bot token or chat ID not set');
                return false;
            }

            // Send the message
            $response = Http::post("https://api.telegram.org/bot{$this->botToken}/sendMessage", [
                'chat_id' => $this->chatId,
                'text' => $message,
                'parse_mode' => 'Markdown'
            ]);

            // Check if the request was successful
            if ($response->successful()) {
                Log::info('Telegram message sent successfully');
                return true;
            } else {
                Log::error('Failed to send Telegram message: ' . $response->body());
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Telegram Service Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send order notification to Telegram
     *
     * @param \App\Models\Order $order The order to send notification for
     * @return bool Whether the notification was sent successfully
     */
    public function sendOrderNotification($order)
    {
        try {
            // Load order relationships
            $order->load(['user', 'orderItems.product']);

            // Prepare message
            $message = "🛒 *New Order #{$order->id}*\n\n";
            $message .= "👤 Customer: {$order->user->name}\n";
            $message .= "📱 Phone: {$order->phone_number}\n";
            $message .= "📍 Address: {$order->shipping_address}\n";
            $message .= "💳 Payment: " . ucfirst($order->payment_method) . "\n\n";

            $message .= "🛍️ *Products:*\n";
            foreach ($order->orderItems as $item) {
                $message .= "- {$item->product->name} x{$item->quantity} (${$item->price})\n";
            }

            $message .= "\n💰 *Total:* $" . number_format($order->total_amount, 2);

            // Send the message
            return $this->sendMessage($message);
        } catch (\Exception $e) {
            Log::error('Telegram Order Notification Error: ' . $e->getMessage());
            return false;
        }
    }
}
