<!DOCTYPE html>
<html lang="en">

<head>
  <title>qrcode - KHQR</title>
  <meta property="og:title" content="qrcode - KHQR" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta charset="utf-8" />
  <meta property="twitter:card" content="summary_large_image" />

  <style data-tag="reset-style-sheet">
    html {
      line-height: 1.15;
    }

    body {
      margin: 0;
    }

    * {
      box-sizing: border-box;
      border-width: 0;
      border-style: solid;
    }

    p,
    li,
    ul,
    pre,
    div,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    figure,
    blockquote,
    figcaption {
      margin: 0;
      padding: 0;
    }

    button {
      background-color: transparent;
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font-family: inherit;
      font-size: 100%;
      line-height: 1.15;
      margin: 0;
    }

    button,
    select {
      text-transform: none;
    }

    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
      -webkit-appearance: button;
    }

    button::-moz-focus-inner,
    [type="button"]::-moz-focus-inner,
    [type="reset"]::-moz-focus-inner,
    [type="submit"]::-moz-focus-inner {
      border-style: none;
      padding: 0;
    }

    button:-moz-focus,
    [type="button"]:-moz-focus,
    [type="reset"]:-moz-focus,
    [type="submit"]:-moz-focus {
      outline: 1px dotted ButtonText;
    }

    a {
      color: inherit;
      text-decoration: inherit;
    }

    input {
      padding: 2px 4px;
    }

    img {
      display: block;
    }

    html {
      scroll-behavior: smooth
    }
  </style>
  <style data-tag="default-style-sheet">
    html {
      font-family: Inter;
      font-size: 16px;
    }

    body {
      font-weight: 400;
      font-style: normal;
      text-decoration: none;
      text-transform: none;
      letter-spacing: normal;
      line-height: 1.15;
      color: var(--dl-color-gray-black);
      background-color: var(--dl-color-gray-white);
    }

    .qrcode-loadingpic {
      /* Set initial rotation */
      transform: rotate(0deg);

      /* Define the animation */
      animation: rotateAnimation 1s linear infinite;
    }

    /* Define the keyframes for the rotation animation */
    @keyframes rotateAnimation {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  </style>
  <link rel="shortcut icon" href="{{ asset('bakong/img/unnamed.png') }}" type="icon/png" sizes="32x32" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Dangrek:wght@400&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://unpkg.com/@teleporthq/teleport-custom-scripts/dist/style.css" />
</head>

<body>
  <link rel="stylesheet" href="{{ asset('bakong/css/style.css') }}" />
  <div>
    <link href="{{ asset('bakong/css/qrcode.css') }}" rel="stylesheet" />

    <div class="qrcode-container">
      <div class="qrcode-body body">
        <img src="https://link.payway.com.kh/images/loading.svg" alt="image" class="qrcode-loadingpic" />
        <span class="qrcode-minutes">
          <span id="countdown">3:00</span>
          <br />
          <br />
        </span>
        <span id="name" class="qrcode-name"><b style="font-size:13px;">BelteiEcom</b></span>
        <span id="currency" class="qrcode-currency">$</span>
        <span id="amount" class="qrcode-amount">
          {{ $amount ?? '0.00' }}
        </span>
        <div class="qrcode-head">
          <div class="qrcode-header">
            <div class="qrcode-container1">
              <div class="qrcode-container2"></div>
              <div class="qrcode-container3">
                <div class="qrcode-container4 qrhrader"></div>
                <img alt="image" src="{{ asset('bakong/img/khqr logo-200h.png') }}" class="qrcode-image logo" />
              </div>
            </div>
          </div>
        </div>
        <div class="qrcode-line line">
          <div class="qrcode-qrcode qrcode">
           <img id="qr-image" alt="image" src="" class="qrcode-qr" />
            <img id="logo" alt="image" src="https://checkout.payway.com.kh/images/usd-khqr-logo.svg"
              class="qrcode-logo" /> 
          </div>
        </div>
        <img alt="image"
          src="{{ asset('bakong/img/payment_icons-cd5e952dde3b886dea1fd1b983d43ce372f1692dec253808ec654096d2feb701-200h.png') }}"
          class="qrcode-banklogo" />
      </div>
    </div>
  </div>

  <!-- Success Modal -->
  <div id="successModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>🎉 Payment Successful!</h2>
      </div>
      <div class="modal-body">
        <div class="success-icon">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" fill="#4CAF50"/>
            <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p class="success-message">Your payment has been confirmed by Bakong!</p>
        <p class="order-info">Processing your order...</p>
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .modal {
      position: fixed;
      z-index: 9999;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .modal-content {
      background-color: white;
      padding: 2rem;
      border-radius: 15px;
      text-align: center;
      max-width: 400px;
      width: 90%;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
      from {
        transform: translateY(-50px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .modal-header h2 {
      color: #4CAF50;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .success-icon {
      margin: 1rem 0;
    }

    .success-message {
      font-size: 1.1rem;
      color: #333;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .order-info {
      color: #666;
      margin-bottom: 1.5rem;
    }

    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #4CAF50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Function to check transaction status directly with Bakong API
    async function checkTransactionStatus() {
      const md5FromUrl = getParameterByName("md5");
      console.log("Checking transaction status with Bakong API for MD5:", md5FromUrl);

      try {
        // Call Bakong API directly like the original qrcode.php
        const response = await fetch('https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiMDYwYjNkYTFjYTJiNDNhNiJ9LCJpYXQiOjE3NDc3NDgzMzcsImV4cCI6MTc1NTUyNDMzN30.nv8PasXDB64Mos3WPu3L62Tfo2_LurtjAcvN4AuFlsY',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            md5: md5FromUrl
          })
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log("Bakong API response:", responseData);

          // Check if Bakong returned success (responseCode === 0)
          if (responseData.responseCode === 0) {
            // Check if payment is to the correct account
            if (responseData.data.toAccountId === "chhunlichhean_kun@wing") {
              console.log("Payment successful! Bakong confirmed transaction.");

              // Show success modal
              showSuccessModal();

              // Now call our backend to complete the order
              try {
                const orderResponse = await fetch('/bakong/check-transaction-by-order', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                  },
                  body: JSON.stringify({
                    order: md5FromUrl,
                    bakong_response: responseData  // Pass the real Bakong response
                  })
                });

                if (orderResponse.ok) {
                  const orderData = await orderResponse.json();
                  console.log("Order creation response:", orderData);

                  // Update modal content to show success
                  updateModalContent("Order created successfully!", "Your order has been processed and confirmed.");

                  // Wait 3 seconds then redirect to success page
                  setTimeout(() => {
                    if (orderData.order_details && orderData.order_details.success_token) {
                      window.location.href = '/orders/success/' + orderData.order_details.success_token;
                    } else {
                      window.location.href = '/orders/history';
                    }
                  }, 3000);
                } else {
                  console.error('Failed to create order:', orderResponse.status);
                  updateModalContent("Order Processing Failed", "Payment confirmed but order creation failed. Please contact support.");
                }
              } catch (orderError) {
                console.error('Error creating order:', orderError);
                updateModalContent("Order Processing Failed", "Payment confirmed but order creation failed. Please contact support.");
              }
            } else {
              console.error('Payment to wrong account:', responseData.data.toAccountId);
            }
          } else {
            // Payment not yet completed, check again
            console.log("Payment not yet completed. Bakong responseCode:", responseData.responseCode);
            setTimeout(checkTransactionStatus, 2000);
          }
        } else {
          console.error('Failed to fetch from Bakong API:', response.status);
          setTimeout(checkTransactionStatus, 2000);
        }
      } catch (error) {
        console.error('Error during Bakong API call:', error);
        setTimeout(checkTransactionStatus, 2000);
      }
    }

    // Start checking for payment every 2 seconds
    checkTransactionStatus();

    // Modal functions
    function showSuccessModal() {
      const modal = document.getElementById('successModal');
      modal.style.display = 'flex';
    }

    function hideSuccessModal() {
      const modal = document.getElementById('successModal');
      modal.style.display = 'none';
    }

    function updateModalContent(title, message) {
      const modalTitle = document.querySelector('.modal-header h2');
      const modalMessage = document.querySelector('.success-message');
      const orderInfo = document.querySelector('.order-info');
      const spinner = document.querySelector('.loading-spinner');

      modalTitle.textContent = title;
      modalMessage.textContent = message;

      if (title.includes('successful')) {
        orderInfo.textContent = 'Redirecting to order details...';
        spinner.style.display = 'flex';
      } else {
        orderInfo.textContent = '';
        spinner.style.display = 'none';
      }
    }
  });

  // Function to get the value of a URL parameter
  function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
      results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return "";
    return decodeURIComponent(results[2].replace(/\+/g, " "));
  }

  // Get the "amount" and "qr" parameters from the URL
  const amountFromUrl = getParameterByName("amount");
  const qrFromUrl = getParameterByName("qr");

  // Set the obtained amount to an HTML element with ID "amount"
  const amountElement = document.getElementById("amount");
  if (amountElement) {
    amountElement.textContent = amountFromUrl || "{{ $amount ?? '0.00' }}";
  }

  // Set the obtained qr to an HTML element with ID "qr-code"
  const qrCodeElement = document.getElementById("qr-image");
  if (qrCodeElement) {
    // Generate the QR code image and set it as the source for an <img> tag
    const qrImage = document.getElementById("qr-image");
    if (qrImage) {
      qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=190x190&data=${qrFromUrl || '{{ $qrData ?? "" }}'}`;
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Set the countdown duration in seconds
    let countdownDuration = 180; // 3 minutes * 60 seconds

    // Get the countdown element
    const countdownElement = document.getElementById("countdown");

    // Update the countdown every second
    const countdownInterval = setInterval(function () {
      const minutes = Math.floor(countdownDuration / 60);
      const seconds = countdownDuration % 60;

      // Display the countdown in the format MM:SS
      countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

      // Decrease the countdown duration
      countdownDuration--;

      // Check if the countdown has reached 0
      if (countdownDuration < 0) {
        // Redirect to home when the countdown reaches 0
        window.location.href = '{{ route("home") }}';

        // Clear the interval to stop the countdown
        clearInterval(countdownInterval);
      }
    }, 1000); // Update every 1000 milliseconds (1 second)
  });
</script>

</body>
