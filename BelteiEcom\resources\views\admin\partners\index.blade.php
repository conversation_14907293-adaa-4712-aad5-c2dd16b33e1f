@extends('layouts.admin')

@section('title', 'Partner Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Partner Management</h1>
            <p class="mb-0 text-muted">Manage partner applications and API access</p>
        </div>
        <div>
            <span class="badge badge-info">{{ $partners->total() }} Total Partners</span>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Applications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ App\Models\ApiPartner::where('status', 'pending')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Partners</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ App\Models\ApiPartner::where('status', 'approved')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-handshake fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Suspended</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ App\Models\ApiPartner::where('status', 'suspended')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ban fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ App\Models\ApiOrder::count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.partners.index') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Company name, contact name, or email..." 
                                   value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Partners Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Partner Applications</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Company</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Tier</th>
                            <th>Commission</th>
                            <th>Applied</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($partners as $partner)
                        <tr>
                            <td>
                                <div class="font-weight-bold">{{ $partner->company_name }}</div>
                                <small class="text-muted">{{ $partner->email }}</small>
                                @if($partner->website)
                                    <br><a href="{{ $partner->website }}" target="_blank" class="text-info">
                                        <i class="fas fa-external-link-alt"></i> Website
                                    </a>
                                @endif
                            </td>
                            <td>
                                <div>{{ $partner->contact_name }}</div>
                                @if($partner->phone)
                                    <small class="text-muted">{{ $partner->phone }}</small>
                                @endif
                            </td>
                            <td>
                                @if($partner->status === 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($partner->status === 'approved')
                                    <span class="badge badge-success">Approved</span>
                                @elseif($partner->status === 'rejected')
                                    <span class="badge badge-danger">Rejected</span>
                                @elseif($partner->status === 'suspended')
                                    <span class="badge badge-secondary">Suspended</span>
                                @endif
                            </td>
                            <td>
                                @if($partner->tier)
                                    <span class="badge badge-info">{{ ucfirst($partner->tier) }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($partner->commission_rate)
                                    {{ $partner->commission_rate }}%
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <div>{{ $partner->created_at->format('M j, Y') }}</div>
                                <small class="text-muted">{{ $partner->created_at->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.partners.show', $partner) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    @if($partner->status === 'pending')
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                data-toggle="modal" data-target="#approveModal{{ $partner->id }}">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                data-toggle="modal" data-target="#rejectModal{{ $partner->id }}">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    @elseif($partner->status === 'approved')
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                data-toggle="modal" data-target="#suspendModal{{ $partner->id }}">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                    @elseif($partner->status === 'suspended')
                                        <form method="POST" action="{{ route('admin.partners.reactivate', $partner) }}" 
                                              style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline-success" 
                                                    onclick="return confirm('Reactivate this partner?')">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>

                        <!-- Approve Modal -->
                        @if($partner->status === 'pending')
                        <div class="modal fade" id="approveModal{{ $partner->id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="{{ route('admin.partners.approve', $partner) }}">
                                        @csrf
                                        <div class="modal-header">
                                            <h5 class="modal-title">Approve Partner Application</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Approve <strong>{{ $partner->company_name }}</strong> as a partner?</p>
                                            
                                            <div class="form-group">
                                                <label>Partner Tier</label>
                                                <select name="tier" class="form-control" required>
                                                    <option value="basic">Basic (60 req/min)</option>
                                                    <option value="premium">Premium (120 req/min)</option>
                                                    <option value="enterprise">Enterprise (300 req/min)</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Commission Rate (%)</label>
                                                <input type="number" name="commission_rate" class="form-control" 
                                                       value="15" min="0" max="50" step="0.01" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Minimum Order ($)</label>
                                                <input type="number" name="minimum_order" class="form-control" 
                                                       value="0" min="0" step="0.01">
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-success">Approve Partner</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Reject Modal -->
                        <div class="modal fade" id="rejectModal{{ $partner->id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="{{ route('admin.partners.reject', $partner) }}">
                                        @csrf
                                        <div class="modal-header">
                                            <h5 class="modal-title">Reject Partner Application</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Reject <strong>{{ $partner->company_name }}</strong>'s application?</p>
                                            
                                            <div class="form-group">
                                                <label>Reason for Rejection</label>
                                                <textarea name="rejection_reason" class="form-control" rows="4" 
                                                          placeholder="Please provide a clear reason for rejection..." required></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-danger">Reject Application</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Suspend Modal -->
                        @if($partner->status === 'approved')
                        <div class="modal fade" id="suspendModal{{ $partner->id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="{{ route('admin.partners.suspend', $partner) }}">
                                        @csrf
                                        <div class="modal-header">
                                            <h5 class="modal-title">Suspend Partner</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Suspend <strong>{{ $partner->company_name }}</strong>?</p>
                                            
                                            <div class="form-group">
                                                <label>Reason for Suspension</label>
                                                <textarea name="suspension_reason" class="form-control" rows="4" 
                                                          placeholder="Please provide a reason for suspension..." required></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-warning">Suspend Partner</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @endif

                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>No partner applications found.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($partners->hasPages())
                <div class="d-flex justify-content-center">
                    {{ $partners->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
