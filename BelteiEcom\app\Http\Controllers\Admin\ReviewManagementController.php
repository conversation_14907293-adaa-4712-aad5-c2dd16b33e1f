<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewManagementController extends Controller
{
    /**
     * Display a listing of reviews.
     */
    public function index(Request $request)
    {
        $query = Review::with(['user', 'product', 'images']);

        // Filter by approval status
        if ($request->has('status')) {
            if ($request->status === 'pending') {
                $query->where('is_approved', false);
            } elseif ($request->status === 'approved') {
                $query->where('is_approved', true);
            }
        }

        // Filter by rating
        if ($request->has('rating') && $request->rating) {
            $query->where('rating', $request->rating);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('comment', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('product', function($productQuery) use ($search) {
                      $productQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $reviews = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.reviews.index', compact('reviews'));
    }

    /**
     * Approve a review.
     */
    public function approve(Review $review)
    {
        $review->update([
            'is_approved' => true,
            'approved_at' => now(),
            'approved_by' => Auth::id(),
        ]);

        return back()->with('success', 'Review approved successfully.');
    }

    /**
     * Reject a review.
     */
    public function reject(Review $review)
    {
        $review->update([
            'is_approved' => false,
            'approved_at' => null,
            'approved_by' => null,
        ]);

        return back()->with('success', 'Review rejected successfully.');
    }

    /**
     * Toggle featured status of a review.
     */
    public function toggleFeatured(Review $review)
    {
        $review->update([
            'is_featured' => !$review->is_featured,
        ]);

        $status = $review->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Review {$status} successfully.");
    }

    /**
     * Delete a review.
     */
    public function destroy(Review $review)
    {
        // Delete associated images
        foreach ($review->images as $image) {
            // Delete from storage
            \Storage::disk('public')->delete($image->image_path);

            // Delete from public storage (Windows XAMPP fix)
            $publicPath = public_path('storage/' . $image->image_path);
            if (file_exists($publicPath)) {
                unlink($publicPath);
            }

            // Delete record
            $image->delete();
        }

        $review->delete();

        return back()->with('success', 'Review deleted successfully.');
    }

    /**
     * Ban a user.
     */
    public function banUser(Request $request, User $user)
    {
        $request->validate([
            'ban_reason' => 'required|string|max:500',
            'ban_duration' => 'nullable|integer|min:1', // days
        ]);

        $banExpiresAt = null;
        if ($request->ban_duration) {
            $banExpiresAt = now()->addDays($request->ban_duration);
        }

        $user->update([
            'is_banned' => true,
            'banned_at' => now(),
            'ban_reason' => $request->ban_reason,
            'banned_by' => Auth::id(),
            'ban_expires_at' => $banExpiresAt,
        ]);

        return back()->with('success', "User {$user->name} has been banned successfully.");
    }

    /**
     * Unban a user.
     */
    public function unbanUser(User $user)
    {
        $user->update([
            'is_banned' => false,
            'banned_at' => null,
            'ban_reason' => null,
            'banned_by' => null,
            'ban_expires_at' => null,
        ]);

        return back()->with('success', "User {$user->name} has been unbanned successfully.");
    }

    /**
     * Update user badge.
     */
    public function updateUserBadge(Request $request, User $user)
    {
        $request->validate([
            'badge' => 'nullable|string|in:verified,premium,vip',
        ]);

        $user->update([
            'badge' => $request->badge,
        ]);

        $badgeText = $request->badge ? ucfirst($request->badge) : 'removed';
        return back()->with('success', "User badge updated to: {$badgeText}");
    }
}
