@extends('layouts.app')

@section('title', 'Confirmation Error')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0" style="border-radius: 20px; overflow: hidden;">
                <!-- Header -->
                <div class="card-header text-center" style="background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); color: white; padding: 3rem 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">❌</div>
                    <h1 style="font-size: 2.5rem; font-weight: 700; margin: 0;">Invalid Confirmation Link</h1>
                    <p style="font-size: 1.2rem; margin: 1rem 0 0 0; opacity: 0.9;">Unable to confirm subscription</p>
                </div>

                <!-- Content -->
                <div class="card-body text-center" style="padding: 3rem 2rem;">
                    <div style="margin-bottom: 2rem;">
                        <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: white;"></i>
                        </div>
                        
                        <h3 style="color: #2c3e50; margin-bottom: 1rem; font-weight: 600;">Confirmation Link Not Found</h3>
                        
                        <p style="color: #6c757d; font-size: 1.1rem; line-height: 1.6; margin-bottom: 2rem;">
                            The confirmation link you clicked is either invalid, expired, or has already been used.
                            <br><br>
                            This could happen if:
                        </p>

                        <div style="background: #f8f9fc; padding: 2rem; border-radius: 15px; margin-bottom: 2rem; text-align: left;">
                            <ul style="color: #6c757d; margin: 0; padding-left: 1.5rem;">
                                <li style="margin-bottom: 0.5rem;">The confirmation link is from an old email</li>
                                <li style="margin-bottom: 0.5rem;">You've already confirmed your subscription</li>
                                <li style="margin-bottom: 0.5rem;">The link was corrupted when copied</li>
                                <li style="margin-bottom: 0.5rem;">The confirmation link has expired (24 hours)</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                        <h5 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-question-circle"></i> Need Help?
                        </h5>
                        <p style="color: #6c757d; margin-bottom: 1.5rem;">
                            If you're trying to subscribe to our newsletter, you can try subscribing again with your email address.
                        </p>
                        
                        <div style="max-width: 400px; margin: 0 auto;">
                            <form id="resubscribeForm" style="display: flex; gap: 1rem;">
                                @csrf
                                <input type="email" name="email" placeholder="Enter your email address" class="form-control" required style="border-radius: 25px; border: 2px solid #e9ecef; padding: 0.75rem 1.5rem;">
                                <button type="submit" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 25px; padding: 0.75rem 1.5rem; white-space: nowrap;">
                                    <i class="fas fa-paper-plane"></i> Subscribe
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div style="margin-top: 2rem;">
                        <a href="{{ route('home') }}" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600; margin-right: 1rem; text-decoration: none;">
                            <i class="fas fa-home"></i> Back to Store
                        </a>
                        
                        <button onclick="window.history.back()" class="btn btn-outline-secondary" style="border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600;">
                            <i class="fas fa-arrow-left"></i> Go Back
                        </button>
                    </div>

                    <!-- Contact Info -->
                    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <p style="color: #6c757d; font-size: 0.9rem; margin: 0;">
                            Still having trouble? Contact our support team at 
                            <a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle resubscribe form
document.getElementById('resubscribeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const email = form.querySelector('input[name="email"]').value;
    const button = form.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;
    
    if (!email) {
        alert('Please enter your email address.');
        return;
    }
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
    button.disabled = true;
    
    // Send subscription request
    fetch('{{ route("newsletter.subscribe") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': form.querySelector('input[name="_token"]').value
        },
        body: JSON.stringify({
            email: email
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            form.reset();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('❌ Something went wrong. Please try again.');
    })
    .finally(() => {
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
});
</script>
@endsection
