<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryMovement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'warehouse_id',
        'product_id',
        'user_id',
        'type',
        'quantity',
        'previous_quantity',
        'new_quantity',
        'reference_type',
        'reference_id',
        'notes',
        'unit_cost',
        'from_warehouse_id',
        'to_warehouse_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'unit_cost' => 'decimal:2',
    ];

    /**
     * Get the warehouse for this movement.
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the product for this movement.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who made this movement.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the source warehouse for transfers.
     */
    public function fromWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'from_warehouse_id');
    }

    /**
     * Get the destination warehouse for transfers.
     */
    public function toWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'to_warehouse_id');
    }

    /**
     * Get the reference model (polymorphic).
     */
    public function reference()
    {
        return $this->morphTo('reference', 'reference_type', 'reference_id');
    }

    /**
     * Get movement type display name.
     */
    public function getTypeDisplayAttribute()
    {
        $types = [
            'in' => 'Stock In',
            'out' => 'Stock Out',
            'transfer' => 'Transfer',
            'adjustment' => 'Adjustment',
            'reserved' => 'Reserved',
            'unreserved' => 'Unreserved',
        ];

        return $types[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get movement type color.
     */
    public function getTypeColorAttribute()
    {
        $colors = [
            'in' => 'success',
            'out' => 'danger',
            'transfer' => 'info',
            'adjustment' => 'warning',
            'reserved' => 'secondary',
            'unreserved' => 'light',
        ];

        return $colors[$this->type] ?? 'primary';
    }

    /**
     * Get movement type icon.
     */
    public function getTypeIconAttribute()
    {
        $icons = [
            'in' => 'fas fa-arrow-up',
            'out' => 'fas fa-arrow-down',
            'transfer' => 'fas fa-exchange-alt',
            'adjustment' => 'fas fa-edit',
            'reserved' => 'fas fa-lock',
            'unreserved' => 'fas fa-unlock',
        ];

        return $icons[$this->type] ?? 'fas fa-box';
    }

    /**
     * Check if movement is inbound.
     */
    public function isInbound()
    {
        return in_array($this->type, ['in', 'adjustment']) && $this->quantity > 0;
    }

    /**
     * Check if movement is outbound.
     */
    public function isOutbound()
    {
        return in_array($this->type, ['out', 'adjustment']) && $this->quantity < 0;
    }

    /**
     * Check if movement is a transfer.
     */
    public function isTransfer()
    {
        return $this->type === 'transfer';
    }

    /**
     * Scope for inbound movements.
     */
    public function scopeInbound($query)
    {
        return $query->where(function ($q) {
            $q->whereIn('type', ['in'])
              ->orWhere(function ($q2) {
                  $q2->where('type', 'adjustment')->where('quantity', '>', 0);
              });
        });
    }

    /**
     * Scope for outbound movements.
     */
    public function scopeOutbound($query)
    {
        return $query->where(function ($q) {
            $q->whereIn('type', ['out'])
              ->orWhere(function ($q2) {
                  $q2->where('type', 'adjustment')->where('quantity', '<', 0);
              });
        });
    }

    /**
     * Scope for transfers.
     */
    public function scopeTransfers($query)
    {
        return $query->where('type', 'transfer');
    }

    /**
     * Scope for a specific warehouse.
     */
    public function scopeForWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    /**
     * Scope for a specific product.
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope for a date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get movements summary for a warehouse.
     */
    public static function getWarehouseSummary($warehouseId, $startDate = null, $endDate = null)
    {
        $query = static::where('warehouse_id', $warehouseId);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return [
            'total_movements' => $query->count(),
            'inbound_movements' => $query->clone()->inbound()->count(),
            'outbound_movements' => $query->clone()->outbound()->count(),
            'transfers_in' => $query->clone()->where('type', 'transfer')->where('to_warehouse_id', $warehouseId)->count(),
            'transfers_out' => $query->clone()->where('type', 'transfer')->where('from_warehouse_id', $warehouseId)->count(),
            'total_quantity_in' => $query->clone()->inbound()->sum('quantity'),
            'total_quantity_out' => abs($query->clone()->outbound()->sum('quantity')),
        ];
    }

    /**
     * Get movements summary for a product.
     */
    public static function getProductSummary($productId, $startDate = null, $endDate = null)
    {
        $query = static::where('product_id', $productId);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return [
            'total_movements' => $query->count(),
            'total_quantity_in' => $query->clone()->inbound()->sum('quantity'),
            'total_quantity_out' => abs($query->clone()->outbound()->sum('quantity')),
            'net_movement' => $query->sum('quantity'),
        ];
    }
}
