@extends('admin.layouts.app')

@section('styles')
<style>
    /* Admin Dashboard Styles */
    .admin-dashboard {
        background: #f8f9fc;
        min-height: 100vh;
        padding: 1.5rem 0;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
        color: white;
        padding: 2.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(30, 58, 138, 0.3);
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateY(0px) rotate(0deg); }
        100% { transform: translateY(-20px) rotate(360deg); }
    }

    .dashboard-header > * {
        position: relative;
        z-index: 2;
    }

    .admin-welcome {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .admin-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 2rem;
    }

    .admin-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .admin-action-btn {
        background: rgba(255,255,255,0.15);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .admin-action-btn:hover {
        background: rgba(255,255,255,0.25);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .admin-info {
        text-align: right;
        opacity: 0.9;
    }

    .admin-time {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .admin-date {
        font-size: 0.95rem;
        opacity: 0.8;
    }

    /* Admin Stats Cards */
    .admin-stats-card {
        background: white;
        border-radius: 12px;
        padding: 1.75rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .admin-stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: var(--card-color);
    }

    .admin-stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        border-color: var(--card-color);
    }

    .admin-stats-card.sales {
        --card-color: #3b82f6;
    }

    .admin-stats-card.orders {
        --card-color: #10b981;
    }

    .admin-stats-card.products {
        --card-color: #f59e0b;
    }

    .admin-stats-card.customers {
        --card-color: #8b5cf6;
    }

    .admin-stats-card.stock-alert {
        --card-color: #ef4444;
    }

    .admin-stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        background: var(--card-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .admin-stats-title {
        font-size: 0.85rem;
        font-weight: 600;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .admin-stats-value {
        font-size: 2.2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .admin-stats-change {
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .admin-stats-change.positive {
        color: #10b981;
    }

    .admin-stats-change.negative {
        color: #ef4444;
    }

    .admin-stats-change.neutral {
        color: #6b7280;
    }

    .admin-stats-change.warning {
        color: #f59e0b;
    }

    /* Admin Content Cards */
    .admin-content-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        overflow: hidden;
        height: 100%;
    }

    .admin-content-card:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }

    .admin-content-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        padding: 1.25rem 1.5rem;
        border: none;
        position: relative;
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .admin-content-header h6 {
        margin: 0;
        font-weight: 700;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .admin-content-body {
        padding: 1.5rem;
    }

    /* Low Stock Specific Styles */
    .low-stock-card {
        border-left: 4px solid #ef4444;
    }

    .low-stock-header {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }

    .stock-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        border: 1px solid #f3f4f6;
        cursor: pointer;
    }

    .stock-item:hover {
        background: #fef2f2;
        border-color: #fecaca;
        transform: translateX(5px);
    }

    .stock-item:last-child {
        margin-bottom: 0;
    }

    .stock-item-image {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stock-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .stock-item-placeholder {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        flex-shrink: 0;
    }

    .stock-item-details {
        flex: 1;
        min-width: 0;
    }

    .stock-item-name {
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .stock-item-category {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .stock-item-price {
        font-size: 0.85rem;
        font-weight: 600;
        color: #059669;
    }

    .stock-level {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 700;
        font-size: 0.9rem;
    }

    .stock-level.critical {
        color: #dc2626;
    }

    .stock-level.low {
        color: #f59e0b;
    }

    .stock-level.out {
        color: #6b7280;
    }

    .stock-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stock-badge.critical {
        background: #fee2e2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .stock-badge.low {
        background: #fef3c7;
        color: #d97706;
        border: 1px solid #fed7aa;
    }

    .stock-badge.out {
        background: #f3f4f6;
        color: #6b7280;
        border: 1px solid #d1d5db;
    }

    /* Admin Table Styles */
    .admin-table {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        font-size: 0.9rem;
    }

    .admin-table thead th {
        background: #f8fafc;
        color: #374151;
        border: none;
        font-weight: 700;
        padding: 0.875rem;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 2px solid #e5e7eb;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border: none;
    }

    .admin-table tbody tr:hover {
        background: #f8fafc;
    }

    .admin-table tbody td {
        padding: 0.875rem;
        border: none;
        border-bottom: 1px solid #f3f4f6;
        vertical-align: middle;
    }

    .admin-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Admin Status Badges */
    .admin-status-badge {
        padding: 0.375rem 0.875rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 1px solid transparent;
    }

    .admin-status-badge.new {
        background: #dbeafe;
        color: #1e40af;
        border-color: #bfdbfe;
    }

    .admin-status-badge.processing {
        background: #fef3c7;
        color: #d97706;
        border-color: #fed7aa;
    }

    .admin-status-badge.shipped {
        background: #ddd6fe;
        color: #7c3aed;
        border-color: #c4b5fd;
    }

    .admin-status-badge.delivered {
        background: #d1fae5;
        color: #059669;
        border-color: #a7f3d0;
    }

    .admin-status-badge.cancelled {
        background: #fee2e2;
        color: #dc2626;
        border-color: #fecaca;
    }

    /* Empty State */
    .admin-empty-state {
        text-align: center;
        padding: 2.5rem 1.5rem;
        color: #6b7280;
    }

    .admin-empty-state i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        opacity: 0.5;
        color: #9ca3af;
    }

    .admin-empty-state h5 {
        margin-bottom: 0.5rem;
        color: #374151;
        font-weight: 600;
    }

    .admin-empty-state p {
        margin: 0;
        opacity: 0.8;
        font-size: 0.9rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .admin-welcome {
            font-size: 1.8rem;
        }

        .admin-actions {
            justify-content: center;
        }

        .admin-info {
            text-align: center;
            margin-top: 1rem;
        }

        .admin-stats-value {
            font-size: 1.8rem;
        }

        .stock-item {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .stock-item-details {
            text-align: center;
        }
    }

</style>
@endsection

@section('content')
<div class="admin-dashboard">
    <!-- Admin Dashboard Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="admin-welcome">🎛️ Admin Dashboard</div>
                <div class="admin-subtitle">Monitor your ecommerce store performance and manage operations</div>
                <div class="admin-actions">
                    <a href="{{ route('admin.products.create') }}" class="admin-action-btn">
                        <i class="fas fa-plus"></i> Add Product
                    </a>
                    <a href="{{ route('admin.orders.index') }}" class="admin-action-btn">
                        <i class="fas fa-clipboard-list"></i> Manage Orders
                    </a>
                    <a href="{{ route('admin.categories.index') }}" class="admin-action-btn">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                    <a href="{{ route('admin.products.index') }}?filter=low_stock" class="admin-action-btn">
                        <i class="fas fa-exclamation-triangle"></i> Stock Alerts
                    </a>
                </div>
            </div>
            <div class="col-md-4">
                <div class="admin-info">
                    <div class="admin-time">{{ date('g:i A') }}</div>
                    <div class="admin-date">{{ date('l, F j, Y') }}</div>
                    <div class="admin-date">Cambodia Time</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Stats Cards -->
    <div class="row mb-4">
        <!-- Total Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="admin-stats-card sales">
                <div class="admin-stats-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="admin-stats-title">Total Revenue</div>
                <div class="admin-stats-value">${{ number_format($totalSales, 0) }}</div>
                <div class="admin-stats-change positive">
                    <i class="fas fa-trending-up"></i> All time earnings
                </div>
            </div>
        </div>

        <!-- Total Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="admin-stats-card orders">
                <div class="admin-stats-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="admin-stats-title">Total Orders</div>
                <div class="admin-stats-value">{{ number_format($totalOrders) }}</div>
                <div class="admin-stats-change positive">
                    <i class="fas fa-chart-line"></i> Orders processed
                </div>
            </div>
        </div>

        <!-- Total Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="admin-stats-card products">
                <div class="admin-stats-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="admin-stats-title">Products</div>
                <div class="admin-stats-value">{{ number_format($totalProducts) }}</div>
                <div class="admin-stats-change neutral">
                    <i class="fas fa-warehouse"></i> In catalog
                </div>
            </div>
        </div>

        <!-- Total Customers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="admin-stats-card customers">
                <div class="admin-stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="admin-stats-title">Customers</div>
                <div class="admin-stats-value">{{ number_format($totalCustomers) }}</div>
                <div class="admin-stats-change positive">
                    <i class="fas fa-user-plus"></i> Registered
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Alert Cards -->
    <div class="row mb-4">
        <!-- Low Stock Alert -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="admin-stats-card stock-alert">
                <div class="admin-stats-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="admin-stats-title">Low Stock Alert</div>
                <div class="admin-stats-value">{{ $lowStockProducts->count() }}</div>
                <div class="admin-stats-change warning">
                    <i class="fas fa-warning"></i> Need attention
                </div>
            </div>
        </div>

        <!-- Critical Stock -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="admin-stats-card stock-alert">
                <div class="admin-stats-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="admin-stats-title">Critical Stock</div>
                <div class="admin-stats-value">{{ $criticalStockProducts }}</div>
                <div class="admin-stats-change negative">
                    <i class="fas fa-exclamation"></i> ≤ 5 items
                </div>
            </div>
        </div>

        <!-- Out of Stock -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="admin-stats-card stock-alert">
                <div class="admin-stats-icon">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="admin-stats-title">Out of Stock</div>
                <div class="admin-stats-value">{{ $outOfStockProducts }}</div>
                <div class="admin-stats-change negative">
                    <i class="fas fa-times"></i> Unavailable
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Low Stock Products -->
        <div class="col-lg-6 mb-4">
            <div class="admin-content-card low-stock-card">
                <div class="admin-content-header low-stock-header d-flex justify-content-between align-items-center">
                    <h6><i class="fas fa-exclamation-triangle"></i> Low Stock Products</h6>
                    <a href="{{ route('admin.products.index') }}?filter=low_stock" class="btn btn-sm btn-light">
                        <i class="fas fa-external-link-alt"></i> View All
                    </a>
                </div>
                <div class="admin-content-body">
                    @if($lowStockProducts->isEmpty())
                        <div class="admin-empty-state">
                            <i class="fas fa-check-circle"></i>
                            <h5>All Products Well Stocked</h5>
                            <p>No products are currently running low on stock</p>
                        </div>
                    @else
                        @foreach($lowStockProducts as $product)
                            <div class="stock-item" onclick="window.location.href='{{ route('admin.products.edit', $product->id) }}'">
                                <div class="stock-item-image">
                                    @if($product->image)
                                        <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                                    @else
                                        <div class="stock-item-placeholder">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="stock-item-details">
                                    <div class="stock-item-name">{{ $product->name }}</div>
                                    <div class="stock-item-category">{{ $product->category->name }}</div>
                                    <div class="stock-item-price">${{ number_format($product->price, 2) }}</div>
                                </div>
                                <div class="stock-level {{ $product->stock == 0 ? 'out' : ($product->stock <= 5 ? 'critical' : 'low') }}">
                                    @if($product->stock == 0)
                                        <span class="stock-badge out">Out of Stock</span>
                                    @elseif($product->stock <= 5)
                                        <span class="stock-badge critical">{{ $product->stock }} left</span>
                                    @else
                                        <span class="stock-badge low">{{ $product->stock }} left</span>
                                    @endif
                                </div>
                            </div>
                        @endforeach

                        @if($lowStockProducts->count() >= 10)
                            <div class="text-center mt-3">
                                <a href="{{ route('admin.products.index') }}?filter=low_stock" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-eye"></i> View All Low Stock Products
                                </a>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="col-lg-6 mb-4">
            <div class="admin-content-card">
                <div class="admin-content-header d-flex justify-content-between align-items-center">
                    <h6><i class="fas fa-shopping-bag"></i> Recent Orders</h6>
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-sm btn-light">
                        <i class="fas fa-external-link-alt"></i> View All
                    </a>
                </div>
                <div class="admin-content-body">
                    @if($recentOrders->isEmpty())
                        <div class="admin-empty-state">
                            <i class="fas fa-shopping-cart"></i>
                            <h5>No Orders Yet</h5>
                            <p>Orders will appear here once customers start purchasing</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="admin-table table">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Customer</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentOrders as $order)
                                        <tr onclick="window.location.href='{{ route('admin.orders.show', $order->id) }}'" style="cursor: pointer;">
                                            <td>
                                                <div class="font-weight-bold text-primary">#{{ $order->id }}</div>
                                                <small class="text-muted">{{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('M j, g:i A') }}</small>
                                            </td>
                                            <td>
                                                <div class="font-weight-bold">{{ $order->user->name }}</div>
                                                <small class="text-muted">{{ Str::limit($order->user->email, 20) }}</small>
                                            </td>
                                            <td>
                                                <span class="admin-status-badge {{ $order->status }}">
                                                    {{ ucfirst($order->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="font-weight-bold text-success">${{ number_format($order->total_amount, 2) }}</div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Products Row -->
    <div class="row">
        <div class="col-lg-12 mb-4">
            <div class="admin-content-card">
                <div class="admin-content-header d-flex justify-content-between align-items-center">
                    <h6><i class="fas fa-star"></i> Top Selling Products</h6>
                    <a href="{{ route('admin.products.index') }}" class="btn btn-sm btn-light">
                        <i class="fas fa-external-link-alt"></i> View All Products
                    </a>
                </div>
                <div class="admin-content-body">
                    @if($popularProducts->isEmpty())
                        <div class="admin-empty-state">
                            <i class="fas fa-chart-bar"></i>
                            <h5>No Sales Data</h5>
                            <p>Popular products will appear here after orders are placed</p>
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="admin-table table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Stock</th>
                                        <th>Sold</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($popularProducts as $product)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($product->image)
                                                        <img src="{{ asset('storage/' . $product->image) }}"
                                                             alt="{{ $product->name }}"
                                                             style="width: 40px; height: 40px; border-radius: 8px; object-fit: cover; margin-right: 0.75rem;">
                                                    @else
                                                        <div style="width: 40px; height: 40px; border-radius: 8px; background: #f3f4f6; display: flex; align-items: center; justify-content: center; color: #9ca3af; margin-right: 0.75rem;">
                                                            <i class="fas fa-image"></i>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        <div class="font-weight-bold">{{ Str::limit($product->name, 25) }}</div>
                                                        <small class="text-muted">ID: {{ $product->id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">{{ $product->category->name }}</span>
                                            </td>
                                            <td>
                                                <div class="font-weight-bold text-success">${{ number_format($product->price, 2) }}</div>
                                            </td>
                                            <td>
                                                @if($product->stock <= 5)
                                                    <span class="stock-badge critical">{{ $product->stock }}</span>
                                                @elseif($product->stock <= 10)
                                                    <span class="stock-badge low">{{ $product->stock }}</span>
                                                @else
                                                    <span class="text-success font-weight-bold">{{ $product->stock }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-success">{{ $product->total_quantity }} sold</span>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
