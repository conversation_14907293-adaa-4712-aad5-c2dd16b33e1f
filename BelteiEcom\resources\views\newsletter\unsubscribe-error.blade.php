@extends('layouts.app')

@section('title', 'Unsubscribe Error')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0" style="border-radius: 20px; overflow: hidden;">
                <!-- Header -->
                <div class="card-header text-center" style="background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); color: white; padding: 3rem 2rem;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">❌</div>
                    <h1 style="font-size: 2.5rem; font-weight: 700; margin: 0;">Oops! Something Went Wrong</h1>
                    <p style="font-size: 1.2rem; margin: 1rem 0 0 0; opacity: 0.9;">Invalid unsubscribe link</p>
                </div>

                <!-- Content -->
                <div class="card-body text-center" style="padding: 3rem 2rem;">
                    <div style="margin-bottom: 2rem;">
                        <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: white;"></i>
                        </div>
                        
                        <h3 style="color: #2c3e50; margin-bottom: 1rem; font-weight: 600;">Invalid Unsubscribe Link</h3>
                        
                        <p style="color: #6c757d; font-size: 1.1rem; line-height: 1.6; margin-bottom: 2rem;">
                            The unsubscribe link you clicked is either invalid, expired, or has already been used.
                            <br><br>
                            This could happen if:
                        </p>

                        <div style="background: #f8f9fc; padding: 2rem; border-radius: 15px; margin-bottom: 2rem; text-align: left;">
                            <ul style="color: #6c757d; margin: 0; padding-left: 1.5rem;">
                                <li style="margin-bottom: 0.5rem;">The link is from an old email</li>
                                <li style="margin-bottom: 0.5rem;">You've already unsubscribed using this link</li>
                                <li style="margin-bottom: 0.5rem;">The link was corrupted when copied</li>
                                <li style="margin-bottom: 0.5rem;">The subscription no longer exists</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                        <h5 style="color: #2c3e50; margin-bottom: 1rem;">
                            <i class="fas fa-question-circle"></i> Need Help?
                        </h5>
                        <p style="color: #6c757d; margin-bottom: 1.5rem;">
                            If you're still receiving unwanted emails, please contact our support team and we'll help you unsubscribe manually.
                        </p>
                        
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary" style="border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600;">
                            <i class="fas fa-envelope"></i> Contact Support
                        </a>
                    </div>

                    <!-- Action Buttons -->
                    <div style="margin-top: 2rem;">
                        <a href="{{ route('home') }}" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600; margin-right: 1rem; text-decoration: none;">
                            <i class="fas fa-home"></i> Back to Store
                        </a>
                        
                        <button onclick="window.history.back()" class="btn btn-outline-secondary" style="border-radius: 25px; padding: 0.75rem 2rem; font-weight: 600;">
                            <i class="fas fa-arrow-left"></i> Go Back
                        </button>
                    </div>

                    <!-- Manual Unsubscribe -->
                    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e9ecef;">
                        <h6 style="color: #2c3e50; margin-bottom: 1rem;">Manual Unsubscribe</h6>
                        <p style="color: #6c757d; font-size: 0.9rem; margin-bottom: 1rem;">
                            Enter your email address to unsubscribe manually:
                        </p>
                        
                        <form id="manualUnsubscribeForm" style="max-width: 400px; margin: 0 auto;">
                            <div class="input-group">
                                <input type="email" class="form-control" placeholder="Enter your email address" required style="border-radius: 25px 0 0 25px; border: 2px solid #e9ecef;">
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-warning" style="border-radius: 0 25px 25px 0; font-weight: 600;">
                                        <i class="fas fa-user-minus"></i> Unsubscribe
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle manual unsubscribe
$('#manualUnsubscribeForm').on('submit', function(e) {
    e.preventDefault();
    
    const email = $(this).find('input[type="email"]').val();
    
    if (!email) {
        alert('Please enter your email address.');
        return;
    }
    
    // Here you would typically make an AJAX call to a manual unsubscribe endpoint
    // For now, we'll show a message
    alert('Please contact our support <NAME_EMAIL> with your email address (' + email + ') to unsubscribe manually.');
});
</script>
@endsection
