<?php

namespace App\Http\Controllers;

use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;

class EmailVerificationController extends Controller
{
    /**
     * Handle email verification
     */
    public function verify(EmailVerificationRequest $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return view('auth.verified')->with('success', 'Your email was already verified!');
        }

        if ($request->user()->markEmailAsVerified()) {
            event(new Verified($request->user()));
        }

        return view('auth.verified')->with('success', 'Your email has been verified successfully!');
    }

    /**
     * Show the email verification notice
     */
    public function notice()
    {
        return view('auth.verify');
    }

    /**
     * Resend the email verification notification
     */
    public function resend(Request $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect()->intended('/')->with('success', 'Your email is already verified!');
        }

        $request->user()->sendEmailVerificationNotification();

        return back()->with('resent', true);
    }
}
