<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        // Get total sales
        $totalSales = Order::sum('total_amount');

        // Get total number of orders
        $totalOrders = Order::count();

        // Get total number of products
        $totalProducts = Product::count();

        // Get total number of customers (non-admin users)
        $totalCustomers = User::where('is_admin', false)->count();

        // Get recent orders
        $recentOrders = Order::with('user')
                            ->orderBy('created_at', 'desc')
                            ->limit(5)
                            ->get();

        // Get popular products (most ordered)
        $popularProducts = DB::table('order_items')
                            ->select('product_id', DB::raw('SUM(quantity) as total_quantity'))
                            ->groupBy('product_id')
                            ->orderBy('total_quantity', 'desc')
                            ->limit(5)
                            ->get()
                            ->map(function ($item) {
                                $product = Product::find($item->product_id);
                                if ($product) {
                                    $product->total_quantity = $item->total_quantity;
                                    return $product;
                                }
                                return null;
                            })
                            ->filter();

        // Get low stock products (stock <= 10)
        $lowStockProducts = Product::with('category')
                                  ->where('stock', '<=', 10)
                                  ->orderBy('stock', 'asc')
                                  ->limit(10)
                                  ->get();

        // Get critical stock products (stock <= 5)
        $criticalStockProducts = Product::where('stock', '<=', 5)->count();

        // Get out of stock products (stock = 0)
        $outOfStockProducts = Product::where('stock', 0)->count();

        return view('admin.dashboard', compact(
            'totalSales',
            'totalOrders',
            'totalProducts',
            'totalCustomers',
            'recentOrders',
            'popularProducts',
            'lowStockProducts',
            'criticalStockProducts',
            'outOfStockProducts'
        ));
    }
}
