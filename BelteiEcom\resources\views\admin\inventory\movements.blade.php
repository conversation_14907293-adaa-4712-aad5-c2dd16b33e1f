@extends('admin.layouts.app')

@section('title', 'Inventory Movements')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.inventory.index') }}">Inventory</a>
                    </li>
                    <li class="breadcrumb-item active">Movements</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-exchange-alt text-info"></i>
                Inventory Movements
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Overview
            </a>
            <a href="{{ route('admin.inventory.export') }}" class="btn btn-success btn-sm">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter"></i>
                        Filters
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.inventory.movements') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="warehouse_id">Warehouse</label>
                                    <select name="warehouse_id" id="warehouse_id" class="form-control">
                                        <option value="">All Warehouses</option>
                                        @foreach($warehouses as $warehouse)
                                            <option value="{{ $warehouse->id }}" {{ ($filters['warehouse_id'] ?? '') == $warehouse->id ? 'selected' : '' }}>
                                                {{ $warehouse->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="product_id">Product</label>
                                    <select name="product_id" id="product_id" class="form-control">
                                        <option value="">All Products</option>
                                        @foreach($products as $product)
                                            <option value="{{ $product->id }}" {{ ($filters['product_id'] ?? '') == $product->id ? 'selected' : '' }}>
                                                {{ $product->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="type">Movement Type</label>
                                    <select name="type" id="type" class="form-control">
                                        <option value="">All Types</option>
                                        <option value="in" {{ ($filters['type'] ?? '') == 'in' ? 'selected' : '' }}>Stock In</option>
                                        <option value="out" {{ ($filters['type'] ?? '') == 'out' ? 'selected' : '' }}>Stock Out</option>
                                        <option value="transfer" {{ ($filters['type'] ?? '') == 'transfer' ? 'selected' : '' }}>Transfer</option>
                                        <option value="adjustment" {{ ($filters['type'] ?? '') == 'adjustment' ? 'selected' : '' }}>Adjustment</option>
                                        <option value="reserved" {{ ($filters['type'] ?? '') == 'reserved' ? 'selected' : '' }}>Reserved</option>
                                        <option value="unreserved" {{ ($filters['type'] ?? '') == 'unreserved' ? 'selected' : '' }}>Unreserved</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" name="start_date" id="start_date" class="form-control" 
                                           value="{{ $filters['start_date'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">End Date</label>
                                    <input type="date" name="end_date" id="end_date" class="form-control" 
                                           value="{{ $filters['end_date'] ?? '' }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Apply Filters
                                </button>
                                <a href="{{ route('admin.inventory.movements') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Movements Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i>
                        Movement History ({{ $movements->total() }} records)
                    </h6>
                </div>
                <div class="card-body">
                    @if($movements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Product</th>
                                        <th>Warehouse</th>
                                        <th>Type</th>
                                        <th>Quantity</th>
                                        <th>Previous</th>
                                        <th>New</th>
                                        <th>User</th>
                                        <th>Notes</th>
                                        <th>Reference</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($movements as $movement)
                                        <tr>
                                            <td>
                                                <strong>{{ $movement->created_at->format('M j, Y') }}</strong><br>
                                                <small class="text-muted">{{ $movement->created_at->format('H:i:s') }}</small>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($movement->product->image)
                                                        <img src="{{ asset('storage/' . $movement->product->image) }}" 
                                                             alt="{{ $movement->product->name }}" 
                                                             class="rounded mr-2" 
                                                             style="width: 30px; height: 30px; object-fit: cover;">
                                                    @endif
                                                    <div>
                                                        <strong>{{ $movement->product->name }}</strong><br>
                                                        <small class="text-muted">{{ $movement->product->sku }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>{{ $movement->warehouse->name }}</strong><br>
                                                <small class="text-muted">{{ $movement->warehouse->code }}</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $movement->type_color }}">
                                                    <i class="{{ $movement->type_icon }}"></i>
                                                    {{ $movement->type_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $movement->quantity > 0 ? 'success' : 'danger' }} badge-lg">
                                                    {{ $movement->quantity > 0 ? '+' : '' }}{{ $movement->quantity }}
                                                </span>
                                            </td>
                                            <td>{{ $movement->previous_quantity }}</td>
                                            <td>{{ $movement->new_quantity }}</td>
                                            <td>
                                                @if($movement->user)
                                                    <strong>{{ $movement->user->name }}</strong><br>
                                                    <small class="text-muted">{{ $movement->user->email }}</small>
                                                @else
                                                    <span class="text-muted">System</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($movement->notes)
                                                    <small>{{ Str::limit($movement->notes, 50) }}</small>
                                                    @if(strlen($movement->notes) > 50)
                                                        <button type="button" class="btn btn-link btn-sm p-0" 
                                                                data-toggle="tooltip" title="{{ $movement->notes }}">
                                                            <i class="fas fa-info-circle"></i>
                                                        </button>
                                                    @endif
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($movement->reference_type && $movement->reference_id)
                                                    <span class="badge badge-info">
                                                        {{ ucfirst($movement->reference_type) }} #{{ $movement->reference_id }}
                                                    </span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $movements->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No movements found</h5>
                            <p class="text-muted">
                                @if(array_filter($filters))
                                    No movements match your current filters. Try adjusting your search criteria.
                                @else
                                    No inventory movements have been recorded yet.
                                @endif
                            </p>
                            @if(array_filter($filters))
                                <a href="{{ route('admin.inventory.movements') }}" class="btn btn-primary">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}

.table th {
    position: sticky;
    top: 0;
    background: #f8f9fc;
    z-index: 10;
}

.btn-link {
    color: #007bff;
}

.btn-link:hover {
    color: #0056b3;
    text-decoration: none;
}
</style>

<script>
// Initialize tooltips
$(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip();
});

// Auto-submit form when date changes
document.getElementById('start_date').addEventListener('change', function() {
    if (this.value && document.getElementById('end_date').value) {
        this.form.submit();
    }
});

document.getElementById('end_date').addEventListener('change', function() {
    if (this.value && document.getElementById('start_date').value) {
        this.form.submit();
    }
});
</script>
@endsection
