<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who made the movement
            $table->enum('type', ['in', 'out', 'transfer', 'adjustment', 'reserved', 'unreserved']);
            $table->integer('quantity'); // Can be negative for outbound movements
            $table->integer('previous_quantity');
            $table->integer('new_quantity');
            $table->string('reference_type')->nullable(); // order, purchase, transfer, etc.
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the reference record
            $table->text('notes')->nullable();
            $table->decimal('unit_cost', 10, 2)->nullable();
            $table->foreignId('from_warehouse_id')->nullable()->constrained('warehouses')->onDelete('set null'); // For transfers
            $table->foreignId('to_warehouse_id')->nullable()->constrained('warehouses')->onDelete('set null'); // For transfers
            $table->timestamps();

            // Indexes for performance and reporting
            $table->index(['warehouse_id', 'product_id', 'created_at']);
            $table->index(['type', 'created_at']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_movements');
    }
};
