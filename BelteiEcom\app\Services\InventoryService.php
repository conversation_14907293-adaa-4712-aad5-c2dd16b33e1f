<?php

namespace App\Services;

use App\Models\Warehouse;
use App\Models\WarehouseInventory;
use App\Models\InventoryMovement;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryService
{
    /**
     * Get inventory for a product across all warehouses.
     */
    public function getProductInventory($productId)
    {
        return WarehouseInventory::where('product_id', $productId)
                                ->with(['warehouse', 'product'])
                                ->get();
    }

    /**
     * Get total available quantity for a product across all warehouses.
     */
    public function getTotalAvailableQuantity($productId)
    {
        return WarehouseInventory::where('product_id', $productId)
                                ->sum(DB::raw('quantity - reserved_quantity'));
    }

    /**
     * Find best warehouse for fulfilling an order.
     */
    public function findBestWarehouse($productId, $quantity, $customerLocation = null)
    {
        $query = WarehouseInventory::where('product_id', $productId)
                                  ->whereRaw('(quantity - reserved_quantity) >= ?', [$quantity])
                                  ->with('warehouse');

        $inventories = $query->get();

        if ($inventories->isEmpty()) {
            return null;
        }

        // If customer location is provided, find nearest warehouse
        if ($customerLocation && isset($customerLocation['latitude']) && isset($customerLocation['longitude'])) {
            $warehousesWithDistance = $inventories->map(function ($inventory) use ($customerLocation) {
                $distance = $inventory->warehouse->distanceTo(
                    $customerLocation['latitude'],
                    $customerLocation['longitude']
                );
                $inventory->warehouse->distance = $distance;
                return $inventory;
            })->filter(function ($inventory) {
                return $inventory->warehouse->distance !== null;
            })->sortBy('warehouse.distance');

            return $warehousesWithDistance->first();
        }

        // Otherwise, return warehouse with highest available quantity
        return $inventories->sortByDesc('available_quantity')->first();
    }

    /**
     * Reserve stock for an order.
     */
    public function reserveStock($productId, $quantity, $warehouseId = null, $referenceType = null, $referenceId = null)
    {
        DB::beginTransaction();

        try {
            if ($warehouseId) {
                $inventory = WarehouseInventory::where('warehouse_id', $warehouseId)
                                             ->where('product_id', $productId)
                                             ->first();
            } else {
                $inventory = $this->findBestWarehouse($productId, $quantity);
            }

            if (!$inventory || !$inventory->hasAvailableStock($quantity)) {
                throw new \Exception('Insufficient stock available');
            }

            $inventory->reserveStock($quantity);

            // Update the movement with reference if provided
            if ($referenceType && $referenceId) {
                $movement = InventoryMovement::where('warehouse_id', $inventory->warehouse_id)
                                           ->where('product_id', $productId)
                                           ->where('type', 'reserved')
                                           ->latest()
                                           ->first();

                if ($movement) {
                    $movement->update([
                        'reference_type' => $referenceType,
                        'reference_id' => $referenceId,
                    ]);
                }
            }

            DB::commit();
            return $inventory;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to reserve stock: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fulfill reserved stock (convert to sold).
     */
    public function fulfillStock($productId, $quantity, $warehouseId, $referenceType = null, $referenceId = null)
    {
        DB::beginTransaction();

        try {
            $inventory = WarehouseInventory::where('warehouse_id', $warehouseId)
                                         ->where('product_id', $productId)
                                         ->first();

            if (!$inventory) {
                throw new \Exception('Inventory not found');
            }

            $inventory->fulfillReservedStock($quantity);

            // Update the movement with reference if provided
            if ($referenceType && $referenceId) {
                $movement = InventoryMovement::where('warehouse_id', $warehouseId)
                                           ->where('product_id', $productId)
                                           ->where('type', 'out')
                                           ->latest()
                                           ->first();

                if ($movement) {
                    $movement->update([
                        'reference_type' => $referenceType,
                        'reference_id' => $referenceId,
                    ]);
                }
            }

            DB::commit();
            return $inventory;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to fulfill stock: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add stock to warehouse.
     */
    public function addStock($productId, $warehouseId, $quantity, $unitCost = null, $notes = null, $userId = null)
    {
        DB::beginTransaction();

        try {
            $inventory = WarehouseInventory::firstOrCreate(
                [
                    'warehouse_id' => $warehouseId,
                    'product_id' => $productId,
                ],
                [
                    'quantity' => 0,
                    'reserved_quantity' => 0,
                    'reorder_level' => 10,
                ]
            );

            $inventory->addStock($quantity, $unitCost, $notes);

            // Update the movement with user if provided
            if ($userId) {
                $movement = InventoryMovement::where('warehouse_id', $warehouseId)
                                           ->where('product_id', $productId)
                                           ->where('type', 'in')
                                           ->latest()
                                           ->first();

                if ($movement) {
                    $movement->update(['user_id' => $userId]);
                }
            }

            DB::commit();
            return $inventory;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to add stock: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Transfer stock between warehouses.
     */
    public function transferStock($productId, $fromWarehouseId, $toWarehouseId, $quantity, $notes = null, $userId = null)
    {
        DB::beginTransaction();

        try {
            // Remove from source warehouse
            $fromInventory = WarehouseInventory::where('warehouse_id', $fromWarehouseId)
                                             ->where('product_id', $productId)
                                             ->first();

            if (!$fromInventory || !$fromInventory->hasAvailableStock($quantity)) {
                throw new \Exception('Insufficient stock in source warehouse');
            }

            $fromInventory->removeStock($quantity, $notes);

            // Add to destination warehouse
            $toInventory = WarehouseInventory::firstOrCreate(
                [
                    'warehouse_id' => $toWarehouseId,
                    'product_id' => $productId,
                ],
                [
                    'quantity' => 0,
                    'reserved_quantity' => 0,
                    'reorder_level' => 10,
                ]
            );

            $toInventory->addStock($quantity, null, $notes);

            // Create transfer movements
            InventoryMovement::create([
                'warehouse_id' => $fromWarehouseId,
                'product_id' => $productId,
                'user_id' => $userId,
                'type' => 'transfer',
                'quantity' => -$quantity,
                'previous_quantity' => $fromInventory->quantity + $quantity,
                'new_quantity' => $fromInventory->quantity,
                'from_warehouse_id' => $fromWarehouseId,
                'to_warehouse_id' => $toWarehouseId,
                'notes' => $notes ?: "Transferred {$quantity} units to warehouse {$toWarehouseId}",
            ]);

            InventoryMovement::create([
                'warehouse_id' => $toWarehouseId,
                'product_id' => $productId,
                'user_id' => $userId,
                'type' => 'transfer',
                'quantity' => $quantity,
                'previous_quantity' => $toInventory->quantity - $quantity,
                'new_quantity' => $toInventory->quantity,
                'from_warehouse_id' => $fromWarehouseId,
                'to_warehouse_id' => $toWarehouseId,
                'notes' => $notes ?: "Received {$quantity} units from warehouse {$fromWarehouseId}",
            ]);

            DB::commit();
            return ['from' => $fromInventory, 'to' => $toInventory];

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to transfer stock: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get low stock alerts.
     */
    public function getLowStockAlerts($warehouseId = null)
    {
        $query = WarehouseInventory::lowStock()->with(['warehouse', 'product']);

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        return $query->get();
    }

    /**
     * Get inventory summary.
     */
    public function getInventorySummary($warehouseId = null)
    {
        $query = WarehouseInventory::query();

        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        $totalProducts = $query->clone()->count();
        $totalQuantity = $query->clone()->sum('quantity');
        $totalReserved = $query->clone()->sum('reserved_quantity');
        $lowStockCount = $query->clone()->lowStock()->count();
        $outOfStockCount = $query->clone()->outOfStock()->count();

        return [
            'total_products' => $totalProducts,
            'total_quantity' => $totalQuantity,
            'total_reserved' => $totalReserved,
            'total_available' => $totalQuantity - $totalReserved,
            'low_stock_count' => $lowStockCount,
            'out_of_stock_count' => $outOfStockCount,
        ];
    }

    /**
     * Get inventory movements for reporting.
     */
    public function getMovements($filters = [])
    {
        $query = InventoryMovement::with(['warehouse', 'product', 'user']);

        if (isset($filters['warehouse_id'])) {
            $query->where('warehouse_id', $filters['warehouse_id']);
        }

        if (isset($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        return $query->orderBy('created_at', 'desc')->paginate(50);
    }

    /**
     * Sync product stock with default warehouse (for backward compatibility).
     */
    public function syncProductStock($productId)
    {
        $product = Product::find($productId);
        $defaultWarehouse = Warehouse::getDefault();

        if (!$product || !$defaultWarehouse) {
            return false;
        }

        $inventory = WarehouseInventory::firstOrCreate(
            [
                'warehouse_id' => $defaultWarehouse->id,
                'product_id' => $productId,
            ],
            [
                'quantity' => $product->stock ?? 0,
                'reserved_quantity' => 0,
                'reorder_level' => 10,
            ]
        );

        // Update product stock to match total available across all warehouses
        $totalAvailable = $this->getTotalAvailableQuantity($productId);
        $product->update(['stock' => $totalAvailable]);

        return $inventory;
    }
}
