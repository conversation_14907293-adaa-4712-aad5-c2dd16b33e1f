var P="top",B="bottom",j="right",k="left",Pe="auto",Ut=[P,B,j,k],wt="start",kt="end",sr="clippingParents",$n="viewport",$t="popper",rr="reference",bn=Ut.reduce(function(e,t){return e.concat([t+"-"+wt,t+"-"+kt])},[]),xn=[].concat(Ut,[Pe]).reduce(function(e,t){return e.concat([t,t+"-"+wt,t+"-"+kt])},[]),ir="beforeRead",or="read",ar="afterRead",cr="beforeMain",lr="main",ur="afterMain",fr="beforeWrite",dr="write",hr="afterWrite",pr=[ir,or,ar,cr,lr,ur,fr,dr,hr];function et(e){return e?(e.nodeName||"").toLowerCase():null}function U(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ot(e){var t=U(e).Element;return e instanceof t||e instanceof Element}function W(e){var t=U(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function In(e){if(typeof ShadowRoot>"u")return!1;var t=U(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Ii(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var s=t.styles[n]||{},r=t.attributes[n]||{},i=t.elements[n];!W(i)||!et(i)||(Object.assign(i.style,s),Object.keys(r).forEach(function(o){var a=r[o];a===!1?i.removeAttribute(o):i.setAttribute(o,a===!0?"":a)}))})}function Pi(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(s){var r=t.elements[s],i=t.attributes[s]||{},o=Object.keys(t.styles.hasOwnProperty(s)?t.styles[s]:n[s]),a=o.reduce(function(l,u){return l[u]="",l},{});!W(r)||!et(r)||(Object.assign(r.style,a),Object.keys(i).forEach(function(l){r.removeAttribute(l)}))})}}const Pn={name:"applyStyles",enabled:!0,phase:"write",fn:Ii,effect:Pi,requires:["computeStyles"]};function Z(e){return e.split("-")[0]}var At=Math.max,De=Math.min,Mt=Math.round;function vn(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function mr(){return!/^((?!chrome|android).)*safari/i.test(vn())}function Ft(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var s=e.getBoundingClientRect(),r=1,i=1;t&&W(e)&&(r=e.offsetWidth>0&&Mt(s.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Mt(s.height)/e.offsetHeight||1);var o=Ot(e)?U(e):window,a=o.visualViewport,l=!mr()&&n,u=(s.left+(l&&a?a.offsetLeft:0))/r,c=(s.top+(l&&a?a.offsetTop:0))/i,h=s.width/r,g=s.height/i;return{width:h,height:g,top:c,right:u+h,bottom:c+g,left:u,x:u,y:c}}function kn(e){var t=Ft(e),n=e.offsetWidth,s=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-s)<=1&&(s=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:s}}function _r(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&In(n)){var s=t;do{if(s&&e.isSameNode(s))return!0;s=s.parentNode||s.host}while(s)}return!1}function rt(e){return U(e).getComputedStyle(e)}function ki(e){return["table","td","th"].indexOf(et(e))>=0}function ft(e){return((Ot(e)?e.ownerDocument:e.document)||window.document).documentElement}function ke(e){return et(e)==="html"?e:e.assignedSlot||e.parentNode||(In(e)?e.host:null)||ft(e)}function cs(e){return!W(e)||rt(e).position==="fixed"?null:e.offsetParent}function Mi(e){var t=/firefox/i.test(vn()),n=/Trident/i.test(vn());if(n&&W(e)){var s=rt(e);if(s.position==="fixed")return null}var r=ke(e);for(In(r)&&(r=r.host);W(r)&&["html","body"].indexOf(et(r))<0;){var i=rt(r);if(i.transform!=="none"||i.perspective!=="none"||i.contain==="paint"||["transform","perspective"].indexOf(i.willChange)!==-1||t&&i.willChange==="filter"||t&&i.filter&&i.filter!=="none")return r;r=r.parentNode}return null}function re(e){for(var t=U(e),n=cs(e);n&&ki(n)&&rt(n).position==="static";)n=cs(n);return n&&(et(n)==="html"||et(n)==="body"&&rt(n).position==="static")?t:n||Mi(e)||t}function Mn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function te(e,t,n){return At(e,De(t,n))}function Fi(e,t,n){var s=te(e,t,n);return s>n?n:s}function gr(){return{top:0,right:0,bottom:0,left:0}}function Er(e){return Object.assign({},gr(),e)}function br(e,t){return t.reduce(function(n,s){return n[s]=e,n},{})}var Vi=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,Er(typeof t!="number"?t:br(t,Ut))};function Hi(e){var t,n=e.state,s=e.name,r=e.options,i=n.elements.arrow,o=n.modifiersData.popperOffsets,a=Z(n.placement),l=Mn(a),u=[k,j].indexOf(a)>=0,c=u?"height":"width";if(!(!i||!o)){var h=Vi(r.padding,n),g=kn(i),E=l==="y"?P:k,m=l==="y"?B:j,_=n.rects.reference[c]+n.rects.reference[l]-o[l]-n.rects.popper[c],p=o[l]-n.rects.reference[l],v=re(i),w=v?l==="y"?v.clientHeight||0:v.clientWidth||0:0,O=_/2-p/2,y=h[E],A=w-g[c]-h[m],S=w/2-g[c]/2+O,C=te(y,S,A),N=l;n.modifiersData[s]=(t={},t[N]=C,t.centerOffset=C-S,t)}}function Bi(e){var t=e.state,n=e.options,s=n.element,r=s===void 0?"[data-popper-arrow]":s;r!=null&&(typeof r=="string"&&(r=t.elements.popper.querySelector(r),!r)||_r(t.elements.popper,r)&&(t.elements.arrow=r))}const vr={name:"arrow",enabled:!0,phase:"main",fn:Hi,effect:Bi,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Vt(e){return e.split("-")[1]}var ji={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ui(e,t){var n=e.x,s=e.y,r=t.devicePixelRatio||1;return{x:Mt(n*r)/r||0,y:Mt(s*r)/r||0}}function ls(e){var t,n=e.popper,s=e.popperRect,r=e.placement,i=e.variation,o=e.offsets,a=e.position,l=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,h=e.isFixed,g=o.x,E=g===void 0?0:g,m=o.y,_=m===void 0?0:m,p=typeof c=="function"?c({x:E,y:_}):{x:E,y:_};E=p.x,_=p.y;var v=o.hasOwnProperty("x"),w=o.hasOwnProperty("y"),O=k,y=P,A=window;if(u){var S=re(n),C="clientHeight",N="clientWidth";if(S===U(n)&&(S=ft(n),rt(S).position!=="static"&&a==="absolute"&&(C="scrollHeight",N="scrollWidth")),S=S,r===P||(r===k||r===j)&&i===kt){y=B;var D=h&&S===A&&A.visualViewport?A.visualViewport.height:S[C];_-=D-s.height,_*=l?1:-1}if(r===k||(r===P||r===B)&&i===kt){O=j;var R=h&&S===A&&A.visualViewport?A.visualViewport.width:S[N];E-=R-s.width,E*=l?1:-1}}var $=Object.assign({position:a},u&&ji),Y=c===!0?Ui({x:E,y:_},U(n)):{x:E,y:_};if(E=Y.x,_=Y.y,l){var I;return Object.assign({},$,(I={},I[y]=w?"0":"",I[O]=v?"0":"",I.transform=(A.devicePixelRatio||1)<=1?"translate("+E+"px, "+_+"px)":"translate3d("+E+"px, "+_+"px, 0)",I))}return Object.assign({},$,(t={},t[y]=w?_+"px":"",t[O]=v?E+"px":"",t.transform="",t))}function Wi(e){var t=e.state,n=e.options,s=n.gpuAcceleration,r=s===void 0?!0:s,i=n.adaptive,o=i===void 0?!0:i,a=n.roundOffsets,l=a===void 0?!0:a,u={placement:Z(t.placement),variation:Vt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,ls(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,ls(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Fn={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Wi,data:{}};var _e={passive:!0};function Ki(e){var t=e.state,n=e.instance,s=e.options,r=s.scroll,i=r===void 0?!0:r,o=s.resize,a=o===void 0?!0:o,l=U(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&u.forEach(function(c){c.addEventListener("scroll",n.update,_e)}),a&&l.addEventListener("resize",n.update,_e),function(){i&&u.forEach(function(c){c.removeEventListener("scroll",n.update,_e)}),a&&l.removeEventListener("resize",n.update,_e)}}const Vn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ki,data:{}};var qi={left:"right",right:"left",bottom:"top",top:"bottom"};function Te(e){return e.replace(/left|right|bottom|top/g,function(t){return qi[t]})}var zi={start:"end",end:"start"};function us(e){return e.replace(/start|end/g,function(t){return zi[t]})}function Hn(e){var t=U(e),n=t.pageXOffset,s=t.pageYOffset;return{scrollLeft:n,scrollTop:s}}function Bn(e){return Ft(ft(e)).left+Hn(e).scrollLeft}function Yi(e,t){var n=U(e),s=ft(e),r=n.visualViewport,i=s.clientWidth,o=s.clientHeight,a=0,l=0;if(r){i=r.width,o=r.height;var u=mr();(u||!u&&t==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:i,height:o,x:a+Bn(e),y:l}}function Gi(e){var t,n=ft(e),s=Hn(e),r=(t=e.ownerDocument)==null?void 0:t.body,i=At(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),o=At(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),a=-s.scrollLeft+Bn(e),l=-s.scrollTop;return rt(r||n).direction==="rtl"&&(a+=At(n.clientWidth,r?r.clientWidth:0)-i),{width:i,height:o,x:a,y:l}}function jn(e){var t=rt(e),n=t.overflow,s=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+s)}function yr(e){return["html","body","#document"].indexOf(et(e))>=0?e.ownerDocument.body:W(e)&&jn(e)?e:yr(ke(e))}function ee(e,t){var n;t===void 0&&(t=[]);var s=yr(e),r=s===((n=e.ownerDocument)==null?void 0:n.body),i=U(s),o=r?[i].concat(i.visualViewport||[],jn(s)?s:[]):s,a=t.concat(o);return r?a:a.concat(ee(ke(o)))}function yn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Xi(e,t){var n=Ft(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function fs(e,t,n){return t===$n?yn(Yi(e,n)):Ot(t)?Xi(t,n):yn(Gi(ft(e)))}function Ji(e){var t=ee(ke(e)),n=["absolute","fixed"].indexOf(rt(e).position)>=0,s=n&&W(e)?re(e):e;return Ot(s)?t.filter(function(r){return Ot(r)&&_r(r,s)&&et(r)!=="body"}):[]}function Qi(e,t,n,s){var r=t==="clippingParents"?Ji(e):[].concat(t),i=[].concat(r,[n]),o=i[0],a=i.reduce(function(l,u){var c=fs(e,u,s);return l.top=At(c.top,l.top),l.right=De(c.right,l.right),l.bottom=De(c.bottom,l.bottom),l.left=At(c.left,l.left),l},fs(e,o,s));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function Ar(e){var t=e.reference,n=e.element,s=e.placement,r=s?Z(s):null,i=s?Vt(s):null,o=t.x+t.width/2-n.width/2,a=t.y+t.height/2-n.height/2,l;switch(r){case P:l={x:o,y:t.y-n.height};break;case B:l={x:o,y:t.y+t.height};break;case j:l={x:t.x+t.width,y:a};break;case k:l={x:t.x-n.width,y:a};break;default:l={x:t.x,y:t.y}}var u=r?Mn(r):null;if(u!=null){var c=u==="y"?"height":"width";switch(i){case wt:l[u]=l[u]-(t[c]/2-n[c]/2);break;case kt:l[u]=l[u]+(t[c]/2-n[c]/2);break}}return l}function Ht(e,t){t===void 0&&(t={});var n=t,s=n.placement,r=s===void 0?e.placement:s,i=n.strategy,o=i===void 0?e.strategy:i,a=n.boundary,l=a===void 0?sr:a,u=n.rootBoundary,c=u===void 0?$n:u,h=n.elementContext,g=h===void 0?$t:h,E=n.altBoundary,m=E===void 0?!1:E,_=n.padding,p=_===void 0?0:_,v=Er(typeof p!="number"?p:br(p,Ut)),w=g===$t?rr:$t,O=e.rects.popper,y=e.elements[m?w:g],A=Qi(Ot(y)?y:y.contextElement||ft(e.elements.popper),l,c,o),S=Ft(e.elements.reference),C=Ar({reference:S,element:O,placement:r}),N=yn(Object.assign({},O,C)),D=g===$t?N:S,R={top:A.top-D.top+v.top,bottom:D.bottom-A.bottom+v.bottom,left:A.left-D.left+v.left,right:D.right-A.right+v.right},$=e.modifiersData.offset;if(g===$t&&$){var Y=$[r];Object.keys(R).forEach(function(I){var pt=[j,B].indexOf(I)>=0?1:-1,mt=[P,B].indexOf(I)>=0?"y":"x";R[I]+=Y[mt]*pt})}return R}function Zi(e,t){t===void 0&&(t={});var n=t,s=n.placement,r=n.boundary,i=n.rootBoundary,o=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,u=l===void 0?xn:l,c=Vt(s),h=c?a?bn:bn.filter(function(m){return Vt(m)===c}):Ut,g=h.filter(function(m){return u.indexOf(m)>=0});g.length===0&&(g=h);var E=g.reduce(function(m,_){return m[_]=Ht(e,{placement:_,boundary:r,rootBoundary:i,padding:o})[Z(_)],m},{});return Object.keys(E).sort(function(m,_){return E[m]-E[_]})}function to(e){if(Z(e)===Pe)return[];var t=Te(e);return[us(e),t,us(t)]}function eo(e){var t=e.state,n=e.options,s=e.name;if(!t.modifiersData[s]._skip){for(var r=n.mainAxis,i=r===void 0?!0:r,o=n.altAxis,a=o===void 0?!0:o,l=n.fallbackPlacements,u=n.padding,c=n.boundary,h=n.rootBoundary,g=n.altBoundary,E=n.flipVariations,m=E===void 0?!0:E,_=n.allowedAutoPlacements,p=t.options.placement,v=Z(p),w=v===p,O=l||(w||!m?[Te(p)]:to(p)),y=[p].concat(O).reduce(function(Dt,ot){return Dt.concat(Z(ot)===Pe?Zi(t,{placement:ot,boundary:c,rootBoundary:h,padding:u,flipVariations:m,allowedAutoPlacements:_}):ot)},[]),A=t.rects.reference,S=t.rects.popper,C=new Map,N=!0,D=y[0],R=0;R<y.length;R++){var $=y[R],Y=Z($),I=Vt($)===wt,pt=[P,B].indexOf(Y)>=0,mt=pt?"width":"height",H=Ht(t,{placement:$,boundary:c,rootBoundary:h,altBoundary:g,padding:u}),G=pt?I?j:k:I?B:P;A[mt]>S[mt]&&(G=Te(G));var fe=Te(G),_t=[];if(i&&_t.push(H[Y]<=0),a&&_t.push(H[G]<=0,H[fe]<=0),_t.every(function(Dt){return Dt})){D=$,N=!1;break}C.set($,_t)}if(N)for(var de=m?3:1,Xe=function(ot){var Xt=y.find(function(pe){var gt=C.get(pe);if(gt)return gt.slice(0,ot).every(function(Je){return Je})});if(Xt)return D=Xt,"break"},Gt=de;Gt>0;Gt--){var he=Xe(Gt);if(he==="break")break}t.placement!==D&&(t.modifiersData[s]._skip=!0,t.placement=D,t.reset=!0)}}const Tr={name:"flip",enabled:!0,phase:"main",fn:eo,requiresIfExists:["offset"],data:{_skip:!1}};function ds(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function hs(e){return[P,j,B,k].some(function(t){return e[t]>=0})}function no(e){var t=e.state,n=e.name,s=t.rects.reference,r=t.rects.popper,i=t.modifiersData.preventOverflow,o=Ht(t,{elementContext:"reference"}),a=Ht(t,{altBoundary:!0}),l=ds(o,s),u=ds(a,r,i),c=hs(l),h=hs(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":h})}const wr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:no};function so(e,t,n){var s=Z(e),r=[k,P].indexOf(s)>=0?-1:1,i=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,o=i[0],a=i[1];return o=o||0,a=(a||0)*r,[k,j].indexOf(s)>=0?{x:a,y:o}:{x:o,y:a}}function ro(e){var t=e.state,n=e.options,s=e.name,r=n.offset,i=r===void 0?[0,0]:r,o=xn.reduce(function(c,h){return c[h]=so(h,t.rects,i),c},{}),a=o[t.placement],l=a.x,u=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[s]=o}const Or={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:ro};function io(e){var t=e.state,n=e.name;t.modifiersData[n]=Ar({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const Un={name:"popperOffsets",enabled:!0,phase:"read",fn:io,data:{}};function oo(e){return e==="x"?"y":"x"}function ao(e){var t=e.state,n=e.options,s=e.name,r=n.mainAxis,i=r===void 0?!0:r,o=n.altAxis,a=o===void 0?!1:o,l=n.boundary,u=n.rootBoundary,c=n.altBoundary,h=n.padding,g=n.tether,E=g===void 0?!0:g,m=n.tetherOffset,_=m===void 0?0:m,p=Ht(t,{boundary:l,rootBoundary:u,padding:h,altBoundary:c}),v=Z(t.placement),w=Vt(t.placement),O=!w,y=Mn(v),A=oo(y),S=t.modifiersData.popperOffsets,C=t.rects.reference,N=t.rects.popper,D=typeof _=="function"?_(Object.assign({},t.rects,{placement:t.placement})):_,R=typeof D=="number"?{mainAxis:D,altAxis:D}:Object.assign({mainAxis:0,altAxis:0},D),$=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,Y={x:0,y:0};if(S){if(i){var I,pt=y==="y"?P:k,mt=y==="y"?B:j,H=y==="y"?"height":"width",G=S[y],fe=G+p[pt],_t=G-p[mt],de=E?-N[H]/2:0,Xe=w===wt?C[H]:N[H],Gt=w===wt?-N[H]:-C[H],he=t.elements.arrow,Dt=E&&he?kn(he):{width:0,height:0},ot=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:gr(),Xt=ot[pt],pe=ot[mt],gt=te(0,C[H],Dt[H]),Je=O?C[H]/2-de-gt-Xt-R.mainAxis:Xe-gt-Xt-R.mainAxis,Ni=O?-C[H]/2+de+gt+pe+R.mainAxis:Gt+gt+pe+R.mainAxis,Qe=t.elements.arrow&&re(t.elements.arrow),Di=Qe?y==="y"?Qe.clientTop||0:Qe.clientLeft||0:0,Zn=(I=$==null?void 0:$[y])!=null?I:0,Li=G+Je-Zn-Di,Ri=G+Ni-Zn,ts=te(E?De(fe,Li):fe,G,E?At(_t,Ri):_t);S[y]=ts,Y[y]=ts-G}if(a){var es,$i=y==="x"?P:k,xi=y==="x"?B:j,Et=S[A],me=A==="y"?"height":"width",ns=Et+p[$i],ss=Et-p[xi],Ze=[P,k].indexOf(v)!==-1,rs=(es=$==null?void 0:$[A])!=null?es:0,is=Ze?ns:Et-C[me]-N[me]-rs+R.altAxis,os=Ze?Et+C[me]+N[me]-rs-R.altAxis:ss,as=E&&Ze?Fi(is,Et,os):te(E?is:ns,Et,E?os:ss);S[A]=as,Y[A]=as-Et}t.modifiersData[s]=Y}}const Sr={name:"preventOverflow",enabled:!0,phase:"main",fn:ao,requiresIfExists:["offset"]};function co(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function lo(e){return e===U(e)||!W(e)?Hn(e):co(e)}function uo(e){var t=e.getBoundingClientRect(),n=Mt(t.width)/e.offsetWidth||1,s=Mt(t.height)/e.offsetHeight||1;return n!==1||s!==1}function fo(e,t,n){n===void 0&&(n=!1);var s=W(t),r=W(t)&&uo(t),i=ft(t),o=Ft(e,r,n),a={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(s||!s&&!n)&&((et(t)!=="body"||jn(i))&&(a=lo(t)),W(t)?(l=Ft(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):i&&(l.x=Bn(i))),{x:o.left+a.scrollLeft-l.x,y:o.top+a.scrollTop-l.y,width:o.width,height:o.height}}function ho(e){var t=new Map,n=new Set,s=[];e.forEach(function(i){t.set(i.name,i)});function r(i){n.add(i.name);var o=[].concat(i.requires||[],i.requiresIfExists||[]);o.forEach(function(a){if(!n.has(a)){var l=t.get(a);l&&r(l)}}),s.push(i)}return e.forEach(function(i){n.has(i.name)||r(i)}),s}function po(e){var t=ho(e);return pr.reduce(function(n,s){return n.concat(t.filter(function(r){return r.phase===s}))},[])}function mo(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function _o(e){var t=e.reduce(function(n,s){var r=n[s.name];return n[s.name]=r?Object.assign({},r,s,{options:Object.assign({},r.options,s.options),data:Object.assign({},r.data,s.data)}):s,n},{});return Object.keys(t).map(function(n){return t[n]})}var ps={placement:"bottom",modifiers:[],strategy:"absolute"};function ms(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(s){return!(s&&typeof s.getBoundingClientRect=="function")})}function Me(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,s=n===void 0?[]:n,r=t.defaultOptions,i=r===void 0?ps:r;return function(a,l,u){u===void 0&&(u=i);var c={placement:"bottom",orderedModifiers:[],options:Object.assign({},ps,i),modifiersData:{},elements:{reference:a,popper:l},attributes:{},styles:{}},h=[],g=!1,E={state:c,setOptions:function(v){var w=typeof v=="function"?v(c.options):v;_(),c.options=Object.assign({},i,c.options,w),c.scrollParents={reference:Ot(a)?ee(a):a.contextElement?ee(a.contextElement):[],popper:ee(l)};var O=po(_o([].concat(s,c.options.modifiers)));return c.orderedModifiers=O.filter(function(y){return y.enabled}),m(),E.update()},forceUpdate:function(){if(!g){var v=c.elements,w=v.reference,O=v.popper;if(ms(w,O)){c.rects={reference:fo(w,re(O),c.options.strategy==="fixed"),popper:kn(O)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach(function(R){return c.modifiersData[R.name]=Object.assign({},R.data)});for(var y=0;y<c.orderedModifiers.length;y++){if(c.reset===!0){c.reset=!1,y=-1;continue}var A=c.orderedModifiers[y],S=A.fn,C=A.options,N=C===void 0?{}:C,D=A.name;typeof S=="function"&&(c=S({state:c,options:N,name:D,instance:E})||c)}}}},update:mo(function(){return new Promise(function(p){E.forceUpdate(),p(c)})}),destroy:function(){_(),g=!0}};if(!ms(a,l))return E;E.setOptions(u).then(function(p){!g&&u.onFirstUpdate&&u.onFirstUpdate(p)});function m(){c.orderedModifiers.forEach(function(p){var v=p.name,w=p.options,O=w===void 0?{}:w,y=p.effect;if(typeof y=="function"){var A=y({state:c,name:v,instance:E,options:O}),S=function(){};h.push(A||S)}})}function _(){h.forEach(function(p){return p()}),h=[]}return E}}var go=Me(),Eo=[Vn,Un,Fn,Pn],bo=Me({defaultModifiers:Eo}),vo=[Vn,Un,Fn,Pn,Or,Tr,Sr,vr,wr],Wn=Me({defaultModifiers:vo});const Cr=Object.freeze(Object.defineProperty({__proto__:null,afterMain:ur,afterRead:ar,afterWrite:hr,applyStyles:Pn,arrow:vr,auto:Pe,basePlacements:Ut,beforeMain:cr,beforeRead:ir,beforeWrite:fr,bottom:B,clippingParents:sr,computeStyles:Fn,createPopper:Wn,createPopperBase:go,createPopperLite:bo,detectOverflow:Ht,end:kt,eventListeners:Vn,flip:Tr,hide:wr,left:k,main:lr,modifierPhases:pr,offset:Or,placements:xn,popper:$t,popperGenerator:Me,popperOffsets:Un,preventOverflow:Sr,read:or,reference:rr,right:j,start:wt,top:P,variationPlacements:bn,viewport:$n,write:dr},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.3.6 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const at=new Map,tn={set(e,t,n){at.has(e)||at.set(e,new Map);const s=at.get(e);if(!s.has(t)&&s.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`);return}s.set(t,n)},get(e,t){return at.has(e)&&at.get(e).get(t)||null},remove(e,t){if(!at.has(e))return;const n=at.get(e);n.delete(t),n.size===0&&at.delete(e)}},yo=1e6,Ao=1e3,An="transitionend",Nr=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(t,n)=>`#${CSS.escape(n)}`)),e),To=e=>e==null?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),wo=e=>{do e+=Math.floor(Math.random()*yo);while(document.getElementById(e));return e},Oo=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const s=Number.parseFloat(t),r=Number.parseFloat(n);return!s&&!r?0:(t=t.split(",")[0],n=n.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(n))*Ao)},Dr=e=>{e.dispatchEvent(new Event(An))},nt=e=>!e||typeof e!="object"?!1:(typeof e.jquery<"u"&&(e=e[0]),typeof e.nodeType<"u"),ct=e=>nt(e)?e.jquery?e[0]:e:typeof e=="string"&&e.length>0?document.querySelector(Nr(e)):null,Wt=e=>{if(!nt(e)||e.getClientRects().length===0)return!1;const t=getComputedStyle(e).getPropertyValue("visibility")==="visible",n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const s=e.closest("summary");if(s&&s.parentNode!==n||s===null)return!1}return t},lt=e=>!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled")?!0:typeof e.disabled<"u"?e.disabled:e.hasAttribute("disabled")&&e.getAttribute("disabled")!=="false",Lr=e=>{if(!document.documentElement.attachShadow)return null;if(typeof e.getRootNode=="function"){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Lr(e.parentNode):null},Le=()=>{},ie=e=>{e.offsetHeight},Rr=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,en=[],So=e=>{document.readyState==="loading"?(en.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of en)t()}),en.push(e)):e()},K=()=>document.documentElement.dir==="rtl",z=e=>{So(()=>{const t=Rr();if(t){const n=e.NAME,s=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=s,e.jQueryInterface)}})},M=(e,t=[],n=e)=>typeof e=="function"?e.call(...t):n,$r=(e,t,n=!0)=>{if(!n){M(e);return}const r=Oo(t)+5;let i=!1;const o=({target:a})=>{a===t&&(i=!0,t.removeEventListener(An,o),M(e))};t.addEventListener(An,o),setTimeout(()=>{i||Dr(t)},r)},Kn=(e,t,n,s)=>{const r=e.length;let i=e.indexOf(t);return i===-1?!n&&s?e[r-1]:e[0]:(i+=n?1:-1,s&&(i=(i+r)%r),e[Math.max(0,Math.min(i,r-1))])},Co=/[^.]*(?=\..*)\.|.*/,No=/\..*/,Do=/::\d+$/,nn={};let _s=1;const xr={mouseenter:"mouseover",mouseleave:"mouseout"},Lo=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Ir(e,t){return t&&`${t}::${_s++}`||e.uidEvent||_s++}function Pr(e){const t=Ir(e);return e.uidEvent=t,nn[t]=nn[t]||{},nn[t]}function Ro(e,t){return function n(s){return qn(s,{delegateTarget:e}),n.oneOff&&d.off(e,s.type,t),t.apply(e,[s])}}function $o(e,t,n){return function s(r){const i=e.querySelectorAll(t);for(let{target:o}=r;o&&o!==this;o=o.parentNode)for(const a of i)if(a===o)return qn(r,{delegateTarget:o}),s.oneOff&&d.off(e,r.type,t,n),n.apply(o,[r])}}function kr(e,t,n=null){return Object.values(e).find(s=>s.callable===t&&s.delegationSelector===n)}function Mr(e,t,n){const s=typeof t=="string",r=s?n:t||n;let i=Fr(e);return Lo.has(i)||(i=e),[s,r,i]}function gs(e,t,n,s,r){if(typeof t!="string"||!e)return;let[i,o,a]=Mr(t,n,s);t in xr&&(o=(m=>function(_){if(!_.relatedTarget||_.relatedTarget!==_.delegateTarget&&!_.delegateTarget.contains(_.relatedTarget))return m.call(this,_)})(o));const l=Pr(e),u=l[a]||(l[a]={}),c=kr(u,o,i?n:null);if(c){c.oneOff=c.oneOff&&r;return}const h=Ir(o,t.replace(Co,"")),g=i?$o(e,n,o):Ro(e,o);g.delegationSelector=i?n:null,g.callable=o,g.oneOff=r,g.uidEvent=h,u[h]=g,e.addEventListener(a,g,i)}function Tn(e,t,n,s,r){const i=kr(t[n],s,r);i&&(e.removeEventListener(n,i,!!r),delete t[n][i.uidEvent])}function xo(e,t,n,s){const r=t[n]||{};for(const[i,o]of Object.entries(r))i.includes(s)&&Tn(e,t,n,o.callable,o.delegationSelector)}function Fr(e){return e=e.replace(No,""),xr[e]||e}const d={on(e,t,n,s){gs(e,t,n,s,!1)},one(e,t,n,s){gs(e,t,n,s,!0)},off(e,t,n,s){if(typeof t!="string"||!e)return;const[r,i,o]=Mr(t,n,s),a=o!==t,l=Pr(e),u=l[o]||{},c=t.startsWith(".");if(typeof i<"u"){if(!Object.keys(u).length)return;Tn(e,l,o,i,r?n:null);return}if(c)for(const h of Object.keys(l))xo(e,l,h,t.slice(1));for(const[h,g]of Object.entries(u)){const E=h.replace(Do,"");(!a||t.includes(E))&&Tn(e,l,o,g.callable,g.delegationSelector)}},trigger(e,t,n){if(typeof t!="string"||!e)return null;const s=Rr(),r=Fr(t),i=t!==r;let o=null,a=!0,l=!0,u=!1;i&&s&&(o=s.Event(t,n),s(e).trigger(o),a=!o.isPropagationStopped(),l=!o.isImmediatePropagationStopped(),u=o.isDefaultPrevented());const c=qn(new Event(t,{bubbles:a,cancelable:!0}),n);return u&&c.preventDefault(),l&&e.dispatchEvent(c),c.defaultPrevented&&o&&o.preventDefault(),c}};function qn(e,t={}){for(const[n,s]of Object.entries(t))try{e[n]=s}catch{Object.defineProperty(e,n,{configurable:!0,get(){return s}})}return e}function Es(e){if(e==="true")return!0;if(e==="false")return!1;if(e===Number(e).toString())return Number(e);if(e===""||e==="null")return null;if(typeof e!="string")return e;try{return JSON.parse(decodeURIComponent(e))}catch{return e}}function sn(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const st={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${sn(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${sn(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter(s=>s.startsWith("bs")&&!s.startsWith("bsConfig"));for(const s of n){let r=s.replace(/^bs/,"");r=r.charAt(0).toLowerCase()+r.slice(1),t[r]=Es(e.dataset[s])}return t},getDataAttribute(e,t){return Es(e.getAttribute(`data-bs-${sn(t)}`))}};class oe{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,n){const s=nt(n)?st.getDataAttribute(n,"config"):{};return{...this.constructor.Default,...typeof s=="object"?s:{},...nt(n)?st.getDataAttributes(n):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,n=this.constructor.DefaultType){for(const[s,r]of Object.entries(n)){const i=t[s],o=nt(i)?"element":To(i);if(!new RegExp(r).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${o}" but expected type "${r}".`)}}}const Io="5.3.6";class X extends oe{constructor(t,n){super(),t=ct(t),t&&(this._element=t,this._config=this._getConfig(n),tn.set(this._element,this.constructor.DATA_KEY,this))}dispose(){tn.remove(this._element,this.constructor.DATA_KEY),d.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,n,s=!0){$r(t,n,s)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return tn.get(ct(t),this.DATA_KEY)}static getOrCreateInstance(t,n={}){return this.getInstance(t)||new this(t,typeof n=="object"?n:null)}static get VERSION(){return Io}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const rn=e=>{let t=e.getAttribute("data-bs-target");if(!t||t==="#"){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&n!=="#"?n.trim():null}return t?t.split(",").map(n=>Nr(n)).join(","):null},b={find(e,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,e))},findOne(e,t=document.documentElement){return Element.prototype.querySelector.call(t,e)},children(e,t){return[].concat(...e.children).filter(n=>n.matches(t))},parents(e,t){const n=[];let s=e.parentNode.closest(t);for(;s;)n.push(s),s=s.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(n=>`${n}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(n=>!lt(n)&&Wt(n))},getSelectorFromElement(e){const t=rn(e);return t&&b.findOne(t)?t:null},getElementFromSelector(e){const t=rn(e);return t?b.findOne(t):null},getMultipleElementsFromSelector(e){const t=rn(e);return t?b.find(t):[]}},Fe=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,s=e.NAME;d.on(document,n,`[data-bs-dismiss="${s}"]`,function(r){if(["A","AREA"].includes(this.tagName)&&r.preventDefault(),lt(this))return;const i=b.getElementFromSelector(this)||this.closest(`.${s}`);e.getOrCreateInstance(i)[t]()})},Po="alert",ko="bs.alert",Vr=`.${ko}`,Mo=`close${Vr}`,Fo=`closed${Vr}`,Vo="fade",Ho="show";class Ve extends X{static get NAME(){return Po}close(){if(d.trigger(this._element,Mo).defaultPrevented)return;this._element.classList.remove(Ho);const n=this._element.classList.contains(Vo);this._queueCallback(()=>this._destroyElement(),this._element,n)}_destroyElement(){this._element.remove(),d.trigger(this._element,Fo),this.dispose()}static jQueryInterface(t){return this.each(function(){const n=Ve.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}Fe(Ve,"close");z(Ve);const Bo="button",jo="bs.button",Uo=`.${jo}`,Wo=".data-api",Ko="active",bs='[data-bs-toggle="button"]',qo=`click${Uo}${Wo}`;class He extends X{static get NAME(){return Bo}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(Ko))}static jQueryInterface(t){return this.each(function(){const n=He.getOrCreateInstance(this);t==="toggle"&&n[t]()})}}d.on(document,qo,bs,e=>{e.preventDefault();const t=e.target.closest(bs);He.getOrCreateInstance(t).toggle()});z(He);const zo="swipe",Kt=".bs.swipe",Yo=`touchstart${Kt}`,Go=`touchmove${Kt}`,Xo=`touchend${Kt}`,Jo=`pointerdown${Kt}`,Qo=`pointerup${Kt}`,Zo="touch",ta="pen",ea="pointer-event",na=40,sa={endCallback:null,leftCallback:null,rightCallback:null},ra={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Re extends oe{constructor(t,n){super(),this._element=t,!(!t||!Re.isSupported())&&(this._config=this._getConfig(n),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return sa}static get DefaultType(){return ra}static get NAME(){return zo}dispose(){d.off(this._element,Kt)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),M(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=na)return;const n=t/this._deltaX;this._deltaX=0,n&&M(n>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(d.on(this._element,Jo,t=>this._start(t)),d.on(this._element,Qo,t=>this._end(t)),this._element.classList.add(ea)):(d.on(this._element,Yo,t=>this._start(t)),d.on(this._element,Go,t=>this._move(t)),d.on(this._element,Xo,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===ta||t.pointerType===Zo)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ia="carousel",oa="bs.carousel",dt=`.${oa}`,Hr=".data-api",aa="ArrowLeft",ca="ArrowRight",la=500,Jt="next",Lt="prev",xt="left",we="right",ua=`slide${dt}`,on=`slid${dt}`,fa=`keydown${dt}`,da=`mouseenter${dt}`,ha=`mouseleave${dt}`,pa=`dragstart${dt}`,ma=`load${dt}${Hr}`,_a=`click${dt}${Hr}`,Br="carousel",ge="active",ga="slide",Ea="carousel-item-end",ba="carousel-item-start",va="carousel-item-next",ya="carousel-item-prev",jr=".active",Ur=".carousel-item",Aa=jr+Ur,Ta=".carousel-item img",wa=".carousel-indicators",Oa="[data-bs-slide], [data-bs-slide-to]",Sa='[data-bs-ride="carousel"]',Ca={[aa]:we,[ca]:xt},Na={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Da={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class ae extends X{constructor(t,n){super(t,n),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=b.findOne(wa,this._element),this._addEventListeners(),this._config.ride===Br&&this.cycle()}static get Default(){return Na}static get DefaultType(){return Da}static get NAME(){return ia}next(){this._slide(Jt)}nextWhenVisible(){!document.hidden&&Wt(this._element)&&this.next()}prev(){this._slide(Lt)}pause(){this._isSliding&&Dr(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){d.one(this._element,on,()=>this.cycle());return}this.cycle()}}to(t){const n=this._getItems();if(t>n.length-1||t<0)return;if(this._isSliding){d.one(this._element,on,()=>this.to(t));return}const s=this._getItemIndex(this._getActive());if(s===t)return;const r=t>s?Jt:Lt;this._slide(r,n[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&d.on(this._element,fa,t=>this._keydown(t)),this._config.pause==="hover"&&(d.on(this._element,da,()=>this.pause()),d.on(this._element,ha,()=>this._maybeEnableCycle())),this._config.touch&&Re.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of b.find(Ta,this._element))d.on(s,pa,r=>r.preventDefault());const n={leftCallback:()=>this._slide(this._directionToOrder(xt)),rightCallback:()=>this._slide(this._directionToOrder(we)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),la+this._config.interval))}};this._swipeHelper=new Re(this._element,n)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const n=Ca[t.key];n&&(t.preventDefault(),this._slide(this._directionToOrder(n)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const n=b.findOne(jr,this._indicatorsElement);n.classList.remove(ge),n.removeAttribute("aria-current");const s=b.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);s&&(s.classList.add(ge),s.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const n=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=n||this._config.defaultInterval}_slide(t,n=null){if(this._isSliding)return;const s=this._getActive(),r=t===Jt,i=n||Kn(this._getItems(),s,r,this._config.wrap);if(i===s)return;const o=this._getItemIndex(i),a=E=>d.trigger(this._element,E,{relatedTarget:i,direction:this._orderToDirection(t),from:this._getItemIndex(s),to:o});if(a(ua).defaultPrevented||!s||!i)return;const u=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=i;const c=r?ba:Ea,h=r?va:ya;i.classList.add(h),ie(i),s.classList.add(c),i.classList.add(c);const g=()=>{i.classList.remove(c,h),i.classList.add(ge),s.classList.remove(ge,h,c),this._isSliding=!1,a(on)};this._queueCallback(g,s,this._isAnimated()),u&&this.cycle()}_isAnimated(){return this._element.classList.contains(ga)}_getActive(){return b.findOne(Aa,this._element)}_getItems(){return b.find(Ur,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return K()?t===xt?Lt:Jt:t===xt?Jt:Lt}_orderToDirection(t){return K()?t===Lt?xt:we:t===Lt?we:xt}static jQueryInterface(t){return this.each(function(){const n=ae.getOrCreateInstance(this,t);if(typeof t=="number"){n.to(t);return}if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}d.on(document,_a,Oa,function(e){const t=b.getElementFromSelector(this);if(!t||!t.classList.contains(Br))return;e.preventDefault();const n=ae.getOrCreateInstance(t),s=this.getAttribute("data-bs-slide-to");if(s){n.to(s),n._maybeEnableCycle();return}if(st.getDataAttribute(this,"slide")==="next"){n.next(),n._maybeEnableCycle();return}n.prev(),n._maybeEnableCycle()});d.on(window,ma,()=>{const e=b.find(Sa);for(const t of e)ae.getOrCreateInstance(t)});z(ae);const La="collapse",Ra="bs.collapse",ce=`.${Ra}`,$a=".data-api",xa=`show${ce}`,Ia=`shown${ce}`,Pa=`hide${ce}`,ka=`hidden${ce}`,Ma=`click${ce}${$a}`,an="show",Pt="collapse",Ee="collapsing",Fa="collapsed",Va=`:scope .${Pt} .${Pt}`,Ha="collapse-horizontal",Ba="width",ja="height",Ua=".collapse.show, .collapse.collapsing",wn='[data-bs-toggle="collapse"]',Wa={parent:null,toggle:!0},Ka={parent:"(null|element)",toggle:"boolean"};class ne extends X{constructor(t,n){super(t,n),this._isTransitioning=!1,this._triggerArray=[];const s=b.find(wn);for(const r of s){const i=b.getSelectorFromElement(r),o=b.find(i).filter(a=>a===this._element);i!==null&&o.length&&this._triggerArray.push(r)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Wa}static get DefaultType(){return Ka}static get NAME(){return La}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(Ua).filter(a=>a!==this._element).map(a=>ne.getOrCreateInstance(a,{toggle:!1}))),t.length&&t[0]._isTransitioning||d.trigger(this._element,xa).defaultPrevented)return;for(const a of t)a.hide();const s=this._getDimension();this._element.classList.remove(Pt),this._element.classList.add(Ee),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const r=()=>{this._isTransitioning=!1,this._element.classList.remove(Ee),this._element.classList.add(Pt,an),this._element.style[s]="",d.trigger(this._element,Ia)},o=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(r,this._element,!0),this._element.style[s]=`${this._element[o]}px`}hide(){if(this._isTransitioning||!this._isShown()||d.trigger(this._element,Pa).defaultPrevented)return;const n=this._getDimension();this._element.style[n]=`${this._element.getBoundingClientRect()[n]}px`,ie(this._element),this._element.classList.add(Ee),this._element.classList.remove(Pt,an);for(const r of this._triggerArray){const i=b.getElementFromSelector(r);i&&!this._isShown(i)&&this._addAriaAndCollapsedClass([r],!1)}this._isTransitioning=!0;const s=()=>{this._isTransitioning=!1,this._element.classList.remove(Ee),this._element.classList.add(Pt),d.trigger(this._element,ka)};this._element.style[n]="",this._queueCallback(s,this._element,!0)}_isShown(t=this._element){return t.classList.contains(an)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=ct(t.parent),t}_getDimension(){return this._element.classList.contains(Ha)?Ba:ja}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(wn);for(const n of t){const s=b.getElementFromSelector(n);s&&this._addAriaAndCollapsedClass([n],this._isShown(s))}}_getFirstLevelChildren(t){const n=b.find(Va,this._config.parent);return b.find(t,this._config.parent).filter(s=>!n.includes(s))}_addAriaAndCollapsedClass(t,n){if(t.length)for(const s of t)s.classList.toggle(Fa,!n),s.setAttribute("aria-expanded",n)}static jQueryInterface(t){const n={};return typeof t=="string"&&/show|hide/.test(t)&&(n.toggle=!1),this.each(function(){const s=ne.getOrCreateInstance(this,n);if(typeof t=="string"){if(typeof s[t]>"u")throw new TypeError(`No method named "${t}"`);s[t]()}})}}d.on(document,Ma,wn,function(e){(e.target.tagName==="A"||e.delegateTarget&&e.delegateTarget.tagName==="A")&&e.preventDefault();for(const t of b.getMultipleElementsFromSelector(this))ne.getOrCreateInstance(t,{toggle:!1}).toggle()});z(ne);const vs="dropdown",qa="bs.dropdown",Ct=`.${qa}`,zn=".data-api",za="Escape",ys="Tab",Ya="ArrowUp",As="ArrowDown",Ga=2,Xa=`hide${Ct}`,Ja=`hidden${Ct}`,Qa=`show${Ct}`,Za=`shown${Ct}`,Wr=`click${Ct}${zn}`,Kr=`keydown${Ct}${zn}`,tc=`keyup${Ct}${zn}`,It="show",ec="dropup",nc="dropend",sc="dropstart",rc="dropup-center",ic="dropdown-center",bt='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',oc=`${bt}.${It}`,Oe=".dropdown-menu",ac=".navbar",cc=".navbar-nav",lc=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",uc=K()?"top-end":"top-start",fc=K()?"top-start":"top-end",dc=K()?"bottom-end":"bottom-start",hc=K()?"bottom-start":"bottom-end",pc=K()?"left-start":"right-start",mc=K()?"right-start":"left-start",_c="top",gc="bottom",Ec={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},bc={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class tt extends X{constructor(t,n){super(t,n),this._popper=null,this._parent=this._element.parentNode,this._menu=b.next(this._element,Oe)[0]||b.prev(this._element,Oe)[0]||b.findOne(Oe,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Ec}static get DefaultType(){return bc}static get NAME(){return vs}toggle(){return this._isShown()?this.hide():this.show()}show(){if(lt(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!d.trigger(this._element,Qa,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(cc))for(const s of[].concat(...document.body.children))d.on(s,"mouseover",Le);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(It),this._element.classList.add(It),d.trigger(this._element,Za,t)}}hide(){if(lt(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!d.trigger(this._element,Xa,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))d.off(s,"mouseover",Le);this._popper&&this._popper.destroy(),this._menu.classList.remove(It),this._element.classList.remove(It),this._element.setAttribute("aria-expanded","false"),st.removeDataAttribute(this._menu,"popper"),d.trigger(this._element,Ja,t),this._element.focus()}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!nt(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${vs.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof Cr>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;this._config.reference==="parent"?t=this._parent:nt(this._config.reference)?t=ct(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const n=this._getPopperConfig();this._popper=Wn(t,this._menu,n)}_isShown(){return this._menu.classList.contains(It)}_getPlacement(){const t=this._parent;if(t.classList.contains(nc))return pc;if(t.classList.contains(sc))return mc;if(t.classList.contains(rc))return _c;if(t.classList.contains(ic))return gc;const n=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(ec)?n?fc:uc:n?hc:dc}_detectNavbar(){return this._element.closest(ac)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(st.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...M(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:n}){const s=b.find(lc,this._menu).filter(r=>Wt(r));s.length&&Kn(s,n,t===As,!s.includes(n)).focus()}static jQueryInterface(t){return this.each(function(){const n=tt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}static clearMenus(t){if(t.button===Ga||t.type==="keyup"&&t.key!==ys)return;const n=b.find(oc);for(const s of n){const r=tt.getInstance(s);if(!r||r._config.autoClose===!1)continue;const i=t.composedPath(),o=i.includes(r._menu);if(i.includes(r._element)||r._config.autoClose==="inside"&&!o||r._config.autoClose==="outside"&&o||r._menu.contains(t.target)&&(t.type==="keyup"&&t.key===ys||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const a={relatedTarget:r._element};t.type==="click"&&(a.clickEvent=t),r._completeHide(a)}}static dataApiKeydownHandler(t){const n=/input|textarea/i.test(t.target.tagName),s=t.key===za,r=[Ya,As].includes(t.key);if(!r&&!s||n&&!s)return;t.preventDefault();const i=this.matches(bt)?this:b.prev(this,bt)[0]||b.next(this,bt)[0]||b.findOne(bt,t.delegateTarget.parentNode),o=tt.getOrCreateInstance(i);if(r){t.stopPropagation(),o.show(),o._selectMenuItem(t);return}o._isShown()&&(t.stopPropagation(),o.hide(),i.focus())}}d.on(document,Kr,bt,tt.dataApiKeydownHandler);d.on(document,Kr,Oe,tt.dataApiKeydownHandler);d.on(document,Wr,tt.clearMenus);d.on(document,tc,tt.clearMenus);d.on(document,Wr,bt,function(e){e.preventDefault(),tt.getOrCreateInstance(this).toggle()});z(tt);const qr="backdrop",vc="fade",Ts="show",ws=`mousedown.bs.${qr}`,yc={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Ac={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class zr extends oe{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return yc}static get DefaultType(){return Ac}static get NAME(){return qr}show(t){if(!this._config.isVisible){M(t);return}this._append();const n=this._getElement();this._config.isAnimated&&ie(n),n.classList.add(Ts),this._emulateAnimation(()=>{M(t)})}hide(t){if(!this._config.isVisible){M(t);return}this._getElement().classList.remove(Ts),this._emulateAnimation(()=>{this.dispose(),M(t)})}dispose(){this._isAppended&&(d.off(this._element,ws),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(vc),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=ct(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),d.on(t,ws,()=>{M(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){$r(t,this._getElement(),this._config.isAnimated)}}const Tc="focustrap",wc="bs.focustrap",$e=`.${wc}`,Oc=`focusin${$e}`,Sc=`keydown.tab${$e}`,Cc="Tab",Nc="forward",Os="backward",Dc={autofocus:!0,trapElement:null},Lc={autofocus:"boolean",trapElement:"element"};class Yr extends oe{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Dc}static get DefaultType(){return Lc}static get NAME(){return Tc}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),d.off(document,$e),d.on(document,Oc,t=>this._handleFocusin(t)),d.on(document,Sc,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,d.off(document,$e))}_handleFocusin(t){const{trapElement:n}=this._config;if(t.target===document||t.target===n||n.contains(t.target))return;const s=b.focusableChildren(n);s.length===0?n.focus():this._lastTabNavDirection===Os?s[s.length-1].focus():s[0].focus()}_handleKeydown(t){t.key===Cc&&(this._lastTabNavDirection=t.shiftKey?Os:Nc)}}const Ss=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Cs=".sticky-top",be="padding-right",Ns="margin-right";class On{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,be,n=>n+t),this._setElementAttributes(Ss,be,n=>n+t),this._setElementAttributes(Cs,Ns,n=>n-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,be),this._resetElementAttributes(Ss,be),this._resetElementAttributes(Cs,Ns)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,n,s){const r=this.getWidth(),i=o=>{if(o!==this._element&&window.innerWidth>o.clientWidth+r)return;this._saveInitialAttribute(o,n);const a=window.getComputedStyle(o).getPropertyValue(n);o.style.setProperty(n,`${s(Number.parseFloat(a))}px`)};this._applyManipulationCallback(t,i)}_saveInitialAttribute(t,n){const s=t.style.getPropertyValue(n);s&&st.setDataAttribute(t,n,s)}_resetElementAttributes(t,n){const s=r=>{const i=st.getDataAttribute(r,n);if(i===null){r.style.removeProperty(n);return}st.removeDataAttribute(r,n),r.style.setProperty(n,i)};this._applyManipulationCallback(t,s)}_applyManipulationCallback(t,n){if(nt(t)){n(t);return}for(const s of b.find(t,this._element))n(s)}}const Rc="modal",$c="bs.modal",q=`.${$c}`,xc=".data-api",Ic="Escape",Pc=`hide${q}`,kc=`hidePrevented${q}`,Gr=`hidden${q}`,Xr=`show${q}`,Mc=`shown${q}`,Fc=`resize${q}`,Vc=`click.dismiss${q}`,Hc=`mousedown.dismiss${q}`,Bc=`keydown.dismiss${q}`,jc=`click${q}${xc}`,Ds="modal-open",Uc="fade",Ls="show",cn="modal-static",Wc=".modal.show",Kc=".modal-dialog",qc=".modal-body",zc='[data-bs-toggle="modal"]',Yc={backdrop:!0,focus:!0,keyboard:!0},Gc={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Bt extends X{constructor(t,n){super(t,n),this._dialog=b.findOne(Kc,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new On,this._addEventListeners()}static get Default(){return Yc}static get DefaultType(){return Gc}static get NAME(){return Rc}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||d.trigger(this._element,Xr,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Ds),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||d.trigger(this._element,Pc).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Ls),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){d.off(window,q),d.off(this._dialog,q),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new zr({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Yr({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const n=b.findOne(qc,this._dialog);n&&(n.scrollTop=0),ie(this._element),this._element.classList.add(Ls);const s=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,d.trigger(this._element,Mc,{relatedTarget:t})};this._queueCallback(s,this._dialog,this._isAnimated())}_addEventListeners(){d.on(this._element,Bc,t=>{if(t.key===Ic){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),d.on(window,Fc,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),d.on(this._element,Hc,t=>{d.one(this._element,Vc,n=>{if(!(this._element!==t.target||this._element!==n.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ds),this._resetAdjustments(),this._scrollBar.reset(),d.trigger(this._element,Gr)})}_isAnimated(){return this._element.classList.contains(Uc)}_triggerBackdropTransition(){if(d.trigger(this._element,kc).defaultPrevented)return;const n=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;s==="hidden"||this._element.classList.contains(cn)||(n||(this._element.style.overflowY="hidden"),this._element.classList.add(cn),this._queueCallback(()=>{this._element.classList.remove(cn),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,n=this._scrollBar.getWidth(),s=n>0;if(s&&!t){const r=K()?"paddingLeft":"paddingRight";this._element.style[r]=`${n}px`}if(!s&&t){const r=K()?"paddingRight":"paddingLeft";this._element.style[r]=`${n}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,n){return this.each(function(){const s=Bt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof s[t]>"u")throw new TypeError(`No method named "${t}"`);s[t](n)}})}}d.on(document,jc,zc,function(e){const t=b.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),d.one(t,Xr,r=>{r.defaultPrevented||d.one(t,Gr,()=>{Wt(this)&&this.focus()})});const n=b.findOne(Wc);n&&Bt.getInstance(n).hide(),Bt.getOrCreateInstance(t).toggle(this)});Fe(Bt);z(Bt);const Xc="offcanvas",Jc="bs.offcanvas",it=`.${Jc}`,Jr=".data-api",Qc=`load${it}${Jr}`,Zc="Escape",Rs="show",$s="showing",xs="hiding",tl="offcanvas-backdrop",Qr=".offcanvas.show",el=`show${it}`,nl=`shown${it}`,sl=`hide${it}`,Is=`hidePrevented${it}`,Zr=`hidden${it}`,rl=`resize${it}`,il=`click${it}${Jr}`,ol=`keydown.dismiss${it}`,al='[data-bs-toggle="offcanvas"]',cl={backdrop:!0,keyboard:!0,scroll:!1},ll={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ut extends X{constructor(t,n){super(t,n),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return cl}static get DefaultType(){return ll}static get NAME(){return Xc}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||d.trigger(this._element,el,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new On().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add($s);const s=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(Rs),this._element.classList.remove($s),d.trigger(this._element,nl,{relatedTarget:t})};this._queueCallback(s,this._element,!0)}hide(){if(!this._isShown||d.trigger(this._element,sl).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(xs),this._backdrop.hide();const n=()=>{this._element.classList.remove(Rs,xs),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new On().reset(),d.trigger(this._element,Zr)};this._queueCallback(n,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){d.trigger(this._element,Is);return}this.hide()},n=!!this._config.backdrop;return new zr({className:tl,isVisible:n,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:n?t:null})}_initializeFocusTrap(){return new Yr({trapElement:this._element})}_addEventListeners(){d.on(this._element,ol,t=>{if(t.key===Zc){if(this._config.keyboard){this.hide();return}d.trigger(this._element,Is)}})}static jQueryInterface(t){return this.each(function(){const n=ut.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}d.on(document,il,al,function(e){const t=b.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),lt(this))return;d.one(t,Zr,()=>{Wt(this)&&this.focus()});const n=b.findOne(Qr);n&&n!==t&&ut.getInstance(n).hide(),ut.getOrCreateInstance(t).toggle(this)});d.on(window,Qc,()=>{for(const e of b.find(Qr))ut.getOrCreateInstance(e).show()});d.on(window,rl,()=>{for(const e of b.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(e).position!=="fixed"&&ut.getOrCreateInstance(e).hide()});Fe(ut);z(ut);const ul=/^aria-[\w-]*$/i,ti={"*":["class","dir","id","lang","role",ul],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},fl=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),dl=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,hl=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?fl.has(n)?!!dl.test(e.nodeValue):!0:t.filter(s=>s instanceof RegExp).some(s=>s.test(n))};function pl(e,t,n){if(!e.length)return e;if(n&&typeof n=="function")return n(e);const r=new window.DOMParser().parseFromString(e,"text/html"),i=[].concat(...r.body.querySelectorAll("*"));for(const o of i){const a=o.nodeName.toLowerCase();if(!Object.keys(t).includes(a)){o.remove();continue}const l=[].concat(...o.attributes),u=[].concat(t["*"]||[],t[a]||[]);for(const c of l)hl(c,u)||o.removeAttribute(c.nodeName)}return r.body.innerHTML}const ml="TemplateFactory",_l={allowList:ti,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},gl={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},El={entry:"(string|element|function|null)",selector:"(string|element)"};class bl extends oe{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return _l}static get DefaultType(){return gl}static get NAME(){return ml}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[r,i]of Object.entries(this._config.content))this._setContent(t,i,r);const n=t.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&n.classList.add(...s.split(" ")),n}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[n,s]of Object.entries(t))super._typeCheckConfig({selector:n,entry:s},El)}_setContent(t,n,s){const r=b.findOne(s,t);if(r){if(n=this._resolvePossibleFunction(n),!n){r.remove();return}if(nt(n)){this._putElementInTemplate(ct(n),r);return}if(this._config.html){r.innerHTML=this._maybeSanitize(n);return}r.textContent=n}}_maybeSanitize(t){return this._config.sanitize?pl(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return M(t,[void 0,this])}_putElementInTemplate(t,n){if(this._config.html){n.innerHTML="",n.append(t);return}n.textContent=t.textContent}}const vl="tooltip",yl=new Set(["sanitize","allowList","sanitizeFn"]),ln="fade",Al="modal",ve="show",Tl=".tooltip-inner",Ps=`.${Al}`,ks="hide.bs.modal",Qt="hover",un="focus",wl="click",Ol="manual",Sl="hide",Cl="hidden",Nl="show",Dl="shown",Ll="inserted",Rl="click",$l="focusin",xl="focusout",Il="mouseenter",Pl="mouseleave",kl={AUTO:"auto",TOP:"top",RIGHT:K()?"left":"right",BOTTOM:"bottom",LEFT:K()?"right":"left"},Ml={allowList:ti,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Fl={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class qt extends X{constructor(t,n){if(typeof Cr>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,n),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Ml}static get DefaultType(){return Fl}static get NAME(){return vl}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),d.off(this._element.closest(Ps),ks,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=d.trigger(this._element,this.constructor.eventName(Nl)),s=(Lr(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!s)return;this._disposePopper();const r=this._getTipElement();this._element.setAttribute("aria-describedby",r.getAttribute("id"));const{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(r),d.trigger(this._element,this.constructor.eventName(Ll))),this._popper=this._createPopper(r),r.classList.add(ve),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))d.on(a,"mouseover",Le);const o=()=>{d.trigger(this._element,this.constructor.eventName(Dl)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(o,this.tip,this._isAnimated())}hide(){if(!this._isShown()||d.trigger(this._element,this.constructor.eventName(Sl)).defaultPrevented)return;if(this._getTipElement().classList.remove(ve),"ontouchstart"in document.documentElement)for(const r of[].concat(...document.body.children))d.off(r,"mouseover",Le);this._activeTrigger[wl]=!1,this._activeTrigger[un]=!1,this._activeTrigger[Qt]=!1,this._isHovered=null;const s=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),d.trigger(this._element,this.constructor.eventName(Cl)))};this._queueCallback(s,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const n=this._getTemplateFactory(t).toHtml();if(!n)return null;n.classList.remove(ln,ve),n.classList.add(`bs-${this.constructor.NAME}-auto`);const s=wo(this.constructor.NAME).toString();return n.setAttribute("id",s),this._isAnimated()&&n.classList.add(ln),n}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new bl({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Tl]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ln)}_isShown(){return this.tip&&this.tip.classList.contains(ve)}_createPopper(t){const n=M(this._config.placement,[this,t,this._element]),s=kl[n.toUpperCase()];return Wn(this._element,t,this._getPopperConfig(s))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_resolvePossibleFunction(t){return M(t,[this._element,this._element])}_getPopperConfig(t){const n={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:s=>{this._getTipElement().setAttribute("data-popper-placement",s.state.placement)}}]};return{...n,...M(this._config.popperConfig,[void 0,n])}}_setListeners(){const t=this._config.trigger.split(" ");for(const n of t)if(n==="click")d.on(this._element,this.constructor.eventName(Rl),this._config.selector,s=>{this._initializeOnDelegatedTarget(s).toggle()});else if(n!==Ol){const s=n===Qt?this.constructor.eventName(Il):this.constructor.eventName($l),r=n===Qt?this.constructor.eventName(Pl):this.constructor.eventName(xl);d.on(this._element,s,this._config.selector,i=>{const o=this._initializeOnDelegatedTarget(i);o._activeTrigger[i.type==="focusin"?un:Qt]=!0,o._enter()}),d.on(this._element,r,this._config.selector,i=>{const o=this._initializeOnDelegatedTarget(i);o._activeTrigger[i.type==="focusout"?un:Qt]=o._element.contains(i.relatedTarget),o._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},d.on(this._element.closest(Ps),ks,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,n){clearTimeout(this._timeout),this._timeout=setTimeout(t,n)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const n=st.getDataAttributes(this._element);for(const s of Object.keys(n))yl.has(s)&&delete n[s];return t={...n,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:ct(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[n,s]of Object.entries(this._config))this.constructor.Default[n]!==s&&(t[n]=s);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const n=qt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}z(qt);const Vl="popover",Hl=".popover-header",Bl=".popover-body",jl={...qt.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Ul={...qt.DefaultType,content:"(null|string|element|function)"};class Yn extends qt{static get Default(){return jl}static get DefaultType(){return Ul}static get NAME(){return Vl}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Hl]:this._getTitle(),[Bl]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const n=Yn.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}z(Yn);const Wl="scrollspy",Kl="bs.scrollspy",Gn=`.${Kl}`,ql=".data-api",zl=`activate${Gn}`,Ms=`click${Gn}`,Yl=`load${Gn}${ql}`,Gl="dropdown-item",Rt="active",Xl='[data-bs-spy="scroll"]',fn="[href]",Jl=".nav, .list-group",Fs=".nav-link",Ql=".nav-item",Zl=".list-group-item",tu=`${Fs}, ${Ql} > ${Fs}, ${Zl}`,eu=".dropdown",nu=".dropdown-toggle",su={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},ru={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Be extends X{constructor(t,n){super(t,n),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return su}static get DefaultType(){return ru}static get NAME(){return Wl}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=ct(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(n=>Number.parseFloat(n))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(d.off(this._config.target,Ms),d.on(this._config.target,Ms,fn,t=>{const n=this._observableSections.get(t.target.hash);if(n){t.preventDefault();const s=this._rootElement||window,r=n.offsetTop-this._element.offsetTop;if(s.scrollTo){s.scrollTo({top:r,behavior:"smooth"});return}s.scrollTop=r}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(n=>this._observerCallback(n),t)}_observerCallback(t){const n=o=>this._targetLinks.get(`#${o.target.id}`),s=o=>{this._previousScrollData.visibleEntryTop=o.target.offsetTop,this._process(n(o))},r=(this._rootElement||document.documentElement).scrollTop,i=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(n(o));continue}const a=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(i&&a){if(s(o),!r)return;continue}!i&&!a&&s(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=b.find(fn,this._config.target);for(const n of t){if(!n.hash||lt(n))continue;const s=b.findOne(decodeURI(n.hash),this._element);Wt(s)&&(this._targetLinks.set(decodeURI(n.hash),n),this._observableSections.set(n.hash,s))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Rt),this._activateParents(t),d.trigger(this._element,zl,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(Gl)){b.findOne(nu,t.closest(eu)).classList.add(Rt);return}for(const n of b.parents(t,Jl))for(const s of b.prev(n,tu))s.classList.add(Rt)}_clearActiveClass(t){t.classList.remove(Rt);const n=b.find(`${fn}.${Rt}`,t);for(const s of n)s.classList.remove(Rt)}static jQueryInterface(t){return this.each(function(){const n=Be.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}d.on(window,Yl,()=>{for(const e of b.find(Xl))Be.getOrCreateInstance(e)});z(Be);const iu="tab",ou="bs.tab",Nt=`.${ou}`,au=`hide${Nt}`,cu=`hidden${Nt}`,lu=`show${Nt}`,uu=`shown${Nt}`,fu=`click${Nt}`,du=`keydown${Nt}`,hu=`load${Nt}`,pu="ArrowLeft",Vs="ArrowRight",mu="ArrowUp",Hs="ArrowDown",dn="Home",Bs="End",vt="active",js="fade",hn="show",_u="dropdown",ei=".dropdown-toggle",gu=".dropdown-menu",pn=`:not(${ei})`,Eu='.list-group, .nav, [role="tablist"]',bu=".nav-item, .list-group-item",vu=`.nav-link${pn}, .list-group-item${pn}, [role="tab"]${pn}`,ni='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',mn=`${vu}, ${ni}`,yu=`.${vt}[data-bs-toggle="tab"], .${vt}[data-bs-toggle="pill"], .${vt}[data-bs-toggle="list"]`;class jt extends X{constructor(t){super(t),this._parent=this._element.closest(Eu),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),d.on(this._element,du,n=>this._keydown(n)))}static get NAME(){return iu}show(){const t=this._element;if(this._elemIsActive(t))return;const n=this._getActiveElem(),s=n?d.trigger(n,au,{relatedTarget:t}):null;d.trigger(t,lu,{relatedTarget:n}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(n,t),this._activate(t,n))}_activate(t,n){if(!t)return;t.classList.add(vt),this._activate(b.getElementFromSelector(t));const s=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(hn);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),d.trigger(t,uu,{relatedTarget:n})};this._queueCallback(s,t,t.classList.contains(js))}_deactivate(t,n){if(!t)return;t.classList.remove(vt),t.blur(),this._deactivate(b.getElementFromSelector(t));const s=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(hn);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),d.trigger(t,cu,{relatedTarget:n})};this._queueCallback(s,t,t.classList.contains(js))}_keydown(t){if(![pu,Vs,mu,Hs,dn,Bs].includes(t.key))return;t.stopPropagation(),t.preventDefault();const n=this._getChildren().filter(r=>!lt(r));let s;if([dn,Bs].includes(t.key))s=n[t.key===dn?0:n.length-1];else{const r=[Vs,Hs].includes(t.key);s=Kn(n,t.target,r,!0)}s&&(s.focus({preventScroll:!0}),jt.getOrCreateInstance(s).show())}_getChildren(){return b.find(mn,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,n){this._setAttributeIfNotExists(t,"role","tablist");for(const s of n)this._setInitialAttributesOnChild(s)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const n=this._elemIsActive(t),s=this._getOuterElement(t);t.setAttribute("aria-selected",n),s!==t&&this._setAttributeIfNotExists(s,"role","presentation"),n||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const n=b.getElementFromSelector(t);n&&(this._setAttributeIfNotExists(n,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(n,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,n){const s=this._getOuterElement(t);if(!s.classList.contains(_u))return;const r=(i,o)=>{const a=b.findOne(i,s);a&&a.classList.toggle(o,n)};r(ei,vt),r(gu,hn),s.setAttribute("aria-expanded",n)}_setAttributeIfNotExists(t,n,s){t.hasAttribute(n)||t.setAttribute(n,s)}_elemIsActive(t){return t.classList.contains(vt)}_getInnerElement(t){return t.matches(mn)?t:b.findOne(mn,t)}_getOuterElement(t){return t.closest(bu)||t}static jQueryInterface(t){return this.each(function(){const n=jt.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}d.on(document,fu,ni,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),!lt(this)&&jt.getOrCreateInstance(this).show()});d.on(window,hu,()=>{for(const e of b.find(yu))jt.getOrCreateInstance(e)});z(jt);const Au="toast",Tu="bs.toast",ht=`.${Tu}`,wu=`mouseover${ht}`,Ou=`mouseout${ht}`,Su=`focusin${ht}`,Cu=`focusout${ht}`,Nu=`hide${ht}`,Du=`hidden${ht}`,Lu=`show${ht}`,Ru=`shown${ht}`,$u="fade",Us="hide",ye="show",Ae="showing",xu={animation:"boolean",autohide:"boolean",delay:"number"},Iu={animation:!0,autohide:!0,delay:5e3};class je extends X{constructor(t,n){super(t,n),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Iu}static get DefaultType(){return xu}static get NAME(){return Au}show(){if(d.trigger(this._element,Lu).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add($u);const n=()=>{this._element.classList.remove(Ae),d.trigger(this._element,Ru),this._maybeScheduleHide()};this._element.classList.remove(Us),ie(this._element),this._element.classList.add(ye,Ae),this._queueCallback(n,this._element,this._config.animation)}hide(){if(!this.isShown()||d.trigger(this._element,Nu).defaultPrevented)return;const n=()=>{this._element.classList.add(Us),this._element.classList.remove(Ae,ye),d.trigger(this._element,Du)};this._element.classList.add(Ae),this._queueCallback(n,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(ye),super.dispose()}isShown(){return this._element.classList.contains(ye)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,n){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=n;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=n;break}}if(n){this._clearTimeout();return}const s=t.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){d.on(this._element,wu,t=>this._onInteraction(t,!0)),d.on(this._element,Ou,t=>this._onInteraction(t,!1)),d.on(this._element,Su,t=>this._onInteraction(t,!0)),d.on(this._element,Cu,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const n=je.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}Fe(je);z(je);function si(e,t){return function(){return e.apply(t,arguments)}}const{toString:Pu}=Object.prototype,{getPrototypeOf:Xn}=Object,{iterator:Ue,toStringTag:ri}=Symbol,We=(e=>t=>{const n=Pu.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),J=e=>(e=e.toLowerCase(),t=>We(t)===e),Ke=e=>t=>typeof t===e,{isArray:zt}=Array,se=Ke("undefined");function ku(e){return e!==null&&!se(e)&&e.constructor!==null&&!se(e.constructor)&&F(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ii=J("ArrayBuffer");function Mu(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ii(e.buffer),t}const Fu=Ke("string"),F=Ke("function"),oi=Ke("number"),qe=e=>e!==null&&typeof e=="object",Vu=e=>e===!0||e===!1,Se=e=>{if(We(e)!=="object")return!1;const t=Xn(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ri in e)&&!(Ue in e)},Hu=J("Date"),Bu=J("File"),ju=J("Blob"),Uu=J("FileList"),Wu=e=>qe(e)&&F(e.pipe),Ku=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||F(e.append)&&((t=We(e))==="formdata"||t==="object"&&F(e.toString)&&e.toString()==="[object FormData]"))},qu=J("URLSearchParams"),[zu,Yu,Gu,Xu]=["ReadableStream","Request","Response","Headers"].map(J),Ju=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function le(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),zt(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let a;for(s=0;s<o;s++)a=i[s],t.call(null,e[a],a,e)}}function ai(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const yt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ci=e=>!se(e)&&e!==yt;function Sn(){const{caseless:e}=ci(this)&&this||{},t={},n=(s,r)=>{const i=e&&ai(t,r)||r;Se(t[i])&&Se(s)?t[i]=Sn(t[i],s):Se(s)?t[i]=Sn({},s):zt(s)?t[i]=s.slice():t[i]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&le(arguments[s],n);return t}const Qu=(e,t,n,{allOwnKeys:s}={})=>(le(t,(r,i)=>{n&&F(r)?e[i]=si(r,n):e[i]=r},{allOwnKeys:s}),e),Zu=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),tf=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},ef=(e,t,n,s)=>{let r,i,o;const a={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),i=r.length;i-- >0;)o=r[i],(!s||s(o,e,t))&&!a[o]&&(t[o]=e[o],a[o]=!0);e=n!==!1&&Xn(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},nf=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},sf=e=>{if(!e)return null;if(zt(e))return e;let t=e.length;if(!oi(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},rf=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Xn(Uint8Array)),of=(e,t)=>{const s=(e&&e[Ue]).call(e);let r;for(;(r=s.next())&&!r.done;){const i=r.value;t.call(e,i[0],i[1])}},af=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},cf=J("HTMLFormElement"),lf=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),Ws=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),uf=J("RegExp"),li=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};le(n,(r,i)=>{let o;(o=t(r,i,e))!==!1&&(s[i]=o||r)}),Object.defineProperties(e,s)},ff=e=>{li(e,(t,n)=>{if(F(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(F(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},df=(e,t)=>{const n={},s=r=>{r.forEach(i=>{n[i]=!0})};return zt(e)?s(e):s(String(e).split(t)),n},hf=()=>{},pf=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function mf(e){return!!(e&&F(e.append)&&e[ri]==="FormData"&&e[Ue])}const _f=e=>{const t=new Array(10),n=(s,r)=>{if(qe(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const i=zt(s)?[]:{};return le(s,(o,a)=>{const l=n(o,r+1);!se(l)&&(i[a]=l)}),t[r]=void 0,i}}return s};return n(e,0)},gf=J("AsyncFunction"),Ef=e=>e&&(qe(e)||F(e))&&F(e.then)&&F(e.catch),ui=((e,t)=>e?setImmediate:t?((n,s)=>(yt.addEventListener("message",({source:r,data:i})=>{r===yt&&i===n&&s.length&&s.shift()()},!1),r=>{s.push(r),yt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",F(yt.postMessage)),bf=typeof queueMicrotask<"u"?queueMicrotask.bind(yt):typeof process<"u"&&process.nextTick||ui,vf=e=>e!=null&&F(e[Ue]),f={isArray:zt,isArrayBuffer:ii,isBuffer:ku,isFormData:Ku,isArrayBufferView:Mu,isString:Fu,isNumber:oi,isBoolean:Vu,isObject:qe,isPlainObject:Se,isReadableStream:zu,isRequest:Yu,isResponse:Gu,isHeaders:Xu,isUndefined:se,isDate:Hu,isFile:Bu,isBlob:ju,isRegExp:uf,isFunction:F,isStream:Wu,isURLSearchParams:qu,isTypedArray:rf,isFileList:Uu,forEach:le,merge:Sn,extend:Qu,trim:Ju,stripBOM:Zu,inherits:tf,toFlatObject:ef,kindOf:We,kindOfTest:J,endsWith:nf,toArray:sf,forEachEntry:of,matchAll:af,isHTMLForm:cf,hasOwnProperty:Ws,hasOwnProp:Ws,reduceDescriptors:li,freezeMethods:ff,toObjectSet:df,toCamelCase:lf,noop:hf,toFiniteNumber:pf,findKey:ai,global:yt,isContextDefined:ci,isSpecCompliantForm:mf,toJSONObject:_f,isAsyncFn:gf,isThenable:Ef,setImmediate:ui,asap:bf,isIterable:vf};function T(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}f.inherits(T,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const fi=T.prototype,di={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{di[e]={value:e}});Object.defineProperties(T,di);Object.defineProperty(fi,"isAxiosError",{value:!0});T.from=(e,t,n,s,r,i)=>{const o=Object.create(fi);return f.toFlatObject(e,o,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),T.call(o,e.message,t,n,s,r),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const yf=null;function Cn(e){return f.isPlainObject(e)||f.isArray(e)}function hi(e){return f.endsWith(e,"[]")?e.slice(0,-2):e}function Ks(e,t,n){return e?e.concat(t).map(function(r,i){return r=hi(r),!n&&i?"["+r+"]":r}).join(n?".":""):t}function Af(e){return f.isArray(e)&&!e.some(Cn)}const Tf=f.toFlatObject(f,{},null,function(t){return/^is[A-Z]/.test(t)});function ze(e,t,n){if(!f.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=f.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(_,p){return!f.isUndefined(p[_])});const s=n.metaTokens,r=n.visitor||c,i=n.dots,o=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&f.isSpecCompliantForm(t);if(!f.isFunction(r))throw new TypeError("visitor must be a function");function u(m){if(m===null)return"";if(f.isDate(m))return m.toISOString();if(!l&&f.isBlob(m))throw new T("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(m)||f.isTypedArray(m)?l&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function c(m,_,p){let v=m;if(m&&!p&&typeof m=="object"){if(f.endsWith(_,"{}"))_=s?_:_.slice(0,-2),m=JSON.stringify(m);else if(f.isArray(m)&&Af(m)||(f.isFileList(m)||f.endsWith(_,"[]"))&&(v=f.toArray(m)))return _=hi(_),v.forEach(function(O,y){!(f.isUndefined(O)||O===null)&&t.append(o===!0?Ks([_],y,i):o===null?_:_+"[]",u(O))}),!1}return Cn(m)?!0:(t.append(Ks(p,_,i),u(m)),!1)}const h=[],g=Object.assign(Tf,{defaultVisitor:c,convertValue:u,isVisitable:Cn});function E(m,_){if(!f.isUndefined(m)){if(h.indexOf(m)!==-1)throw Error("Circular reference detected in "+_.join("."));h.push(m),f.forEach(m,function(v,w){(!(f.isUndefined(v)||v===null)&&r.call(t,v,f.isString(w)?w.trim():w,_,g))===!0&&E(v,_?_.concat(w):[w])}),h.pop()}}if(!f.isObject(e))throw new TypeError("data must be an object");return E(e),t}function qs(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Jn(e,t){this._pairs=[],e&&ze(e,this,t)}const pi=Jn.prototype;pi.append=function(t,n){this._pairs.push([t,n])};pi.toString=function(t){const n=t?function(s){return t.call(this,s,qs)}:qs;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function wf(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function mi(e,t,n){if(!t)return e;const s=n&&n.encode||wf;f.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let i;if(r?i=r(t,n):i=f.isURLSearchParams(t)?t.toString():new Jn(t,n).toString(s),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class zs{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){f.forEach(this.handlers,function(s){s!==null&&t(s)})}}const _i={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Of=typeof URLSearchParams<"u"?URLSearchParams:Jn,Sf=typeof FormData<"u"?FormData:null,Cf=typeof Blob<"u"?Blob:null,Nf={isBrowser:!0,classes:{URLSearchParams:Of,FormData:Sf,Blob:Cf},protocols:["http","https","file","blob","url","data"]},Qn=typeof window<"u"&&typeof document<"u",Nn=typeof navigator=="object"&&navigator||void 0,Df=Qn&&(!Nn||["ReactNative","NativeScript","NS"].indexOf(Nn.product)<0),Lf=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Rf=Qn&&window.location.href||"http://localhost",$f=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Qn,hasStandardBrowserEnv:Df,hasStandardBrowserWebWorkerEnv:Lf,navigator:Nn,origin:Rf},Symbol.toStringTag,{value:"Module"})),x={...$f,...Nf};function xf(e,t){return ze(e,new x.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,i){return x.isNode&&f.isBuffer(n)?(this.append(s,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function If(e){return f.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Pf(e){const t={},n=Object.keys(e);let s;const r=n.length;let i;for(s=0;s<r;s++)i=n[s],t[i]=e[i];return t}function gi(e){function t(n,s,r,i){let o=n[i++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),l=i>=n.length;return o=!o&&f.isArray(r)?r.length:o,l?(f.hasOwnProp(r,o)?r[o]=[r[o],s]:r[o]=s,!a):((!r[o]||!f.isObject(r[o]))&&(r[o]=[]),t(n,s,r[o],i)&&f.isArray(r[o])&&(r[o]=Pf(r[o])),!a)}if(f.isFormData(e)&&f.isFunction(e.entries)){const n={};return f.forEachEntry(e,(s,r)=>{t(If(s),r,n,0)}),n}return null}function kf(e,t,n){if(f.isString(e))try{return(t||JSON.parse)(e),f.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const ue={transitional:_i,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,i=f.isObject(t);if(i&&f.isHTMLForm(t)&&(t=new FormData(t)),f.isFormData(t))return r?JSON.stringify(gi(t)):t;if(f.isArrayBuffer(t)||f.isBuffer(t)||f.isStream(t)||f.isFile(t)||f.isBlob(t)||f.isReadableStream(t))return t;if(f.isArrayBufferView(t))return t.buffer;if(f.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return xf(t,this.formSerializer).toString();if((a=f.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return ze(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return i||r?(n.setContentType("application/json",!1),kf(t)):t}],transformResponse:[function(t){const n=this.transitional||ue.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(f.isResponse(t)||f.isReadableStream(t))return t;if(t&&f.isString(t)&&(s&&!this.responseType||r)){const o=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(a){if(o)throw a.name==="SyntaxError"?T.from(a,T.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:x.classes.FormData,Blob:x.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],e=>{ue.headers[e]={}});const Mf=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ff=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(o){r=o.indexOf(":"),n=o.substring(0,r).trim().toLowerCase(),s=o.substring(r+1).trim(),!(!n||t[n]&&Mf[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Ys=Symbol("internals");function Zt(e){return e&&String(e).trim().toLowerCase()}function Ce(e){return e===!1||e==null?e:f.isArray(e)?e.map(Ce):String(e)}function Vf(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Hf=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function _n(e,t,n,s,r){if(f.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!f.isString(t)){if(f.isString(s))return t.indexOf(s)!==-1;if(f.isRegExp(s))return s.test(t)}}function Bf(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function jf(e,t){const n=f.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,i,o){return this[s].call(this,t,r,i,o)},configurable:!0})})}let V=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function i(a,l,u){const c=Zt(l);if(!c)throw new Error("header name must be a non-empty string");const h=f.findKey(r,c);(!h||r[h]===void 0||u===!0||u===void 0&&r[h]!==!1)&&(r[h||l]=Ce(a))}const o=(a,l)=>f.forEach(a,(u,c)=>i(u,c,l));if(f.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(f.isString(t)&&(t=t.trim())&&!Hf(t))o(Ff(t),n);else if(f.isObject(t)&&f.isIterable(t)){let a={},l,u;for(const c of t){if(!f.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[u=c[0]]=(l=a[u])?f.isArray(l)?[...l,c[1]]:[l,c[1]]:c[1]}o(a,n)}else t!=null&&i(n,t,s);return this}get(t,n){if(t=Zt(t),t){const s=f.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Vf(r);if(f.isFunction(n))return n.call(this,r,s);if(f.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Zt(t),t){const s=f.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||_n(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function i(o){if(o=Zt(o),o){const a=f.findKey(s,o);a&&(!n||_n(s,s[a],a,n))&&(delete s[a],r=!0)}}return f.isArray(t)?t.forEach(i):i(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const i=n[s];(!t||_n(this,this[i],i,t,!0))&&(delete this[i],r=!0)}return r}normalize(t){const n=this,s={};return f.forEach(this,(r,i)=>{const o=f.findKey(s,i);if(o){n[o]=Ce(r),delete n[i];return}const a=t?Bf(i):String(i).trim();a!==i&&delete n[i],n[a]=Ce(r),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return f.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&f.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[Ys]=this[Ys]={accessors:{}}).accessors,r=this.prototype;function i(o){const a=Zt(o);s[a]||(jf(r,o),s[a]=!0)}return f.isArray(t)?t.forEach(i):i(t),this}};V.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(V.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});f.freezeMethods(V);function gn(e,t){const n=this||ue,s=t||n,r=V.from(s.headers);let i=s.data;return f.forEach(e,function(a){i=a.call(n,i,r.normalize(),t?t.status:void 0)}),r.normalize(),i}function Ei(e){return!!(e&&e.__CANCEL__)}function Yt(e,t,n){T.call(this,e??"canceled",T.ERR_CANCELED,t,n),this.name="CanceledError"}f.inherits(Yt,T,{__CANCEL__:!0});function bi(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new T("Request failed with status code "+n.status,[T.ERR_BAD_REQUEST,T.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Uf(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Wf(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,i=0,o;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=s[i];o||(o=u),n[r]=l,s[r]=u;let h=i,g=0;for(;h!==r;)g+=n[h++],h=h%e;if(r=(r+1)%e,r===i&&(i=(i+1)%e),u-o<t)return;const E=c&&u-c;return E?Math.round(g*1e3/E):void 0}}function Kf(e,t){let n=0,s=1e3/t,r,i;const o=(u,c=Date.now())=>{n=c,r=null,i&&(clearTimeout(i),i=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),h=c-n;h>=s?o(u,c):(r=u,i||(i=setTimeout(()=>{i=null,o(r)},s-h)))},()=>r&&o(r)]}const xe=(e,t,n=3)=>{let s=0;const r=Wf(50,250);return Kf(i=>{const o=i.loaded,a=i.lengthComputable?i.total:void 0,l=o-s,u=r(l),c=o<=a;s=o;const h={loaded:o,total:a,progress:a?o/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&c?(a-o)/u:void 0,event:i,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(h)},n)},Gs=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Xs=e=>(...t)=>f.asap(()=>e(...t)),qf=x.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,x.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(x.origin),x.navigator&&/(msie|trident)/i.test(x.navigator.userAgent)):()=>!0,zf=x.hasStandardBrowserEnv?{write(e,t,n,s,r,i){const o=[e+"="+encodeURIComponent(t)];f.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),f.isString(s)&&o.push("path="+s),f.isString(r)&&o.push("domain="+r),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Yf(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Gf(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function vi(e,t,n){let s=!Yf(t);return e&&(s||n==!1)?Gf(e,t):t}const Js=e=>e instanceof V?{...e}:e;function St(e,t){t=t||{};const n={};function s(u,c,h,g){return f.isPlainObject(u)&&f.isPlainObject(c)?f.merge.call({caseless:g},u,c):f.isPlainObject(c)?f.merge({},c):f.isArray(c)?c.slice():c}function r(u,c,h,g){if(f.isUndefined(c)){if(!f.isUndefined(u))return s(void 0,u,h,g)}else return s(u,c,h,g)}function i(u,c){if(!f.isUndefined(c))return s(void 0,c)}function o(u,c){if(f.isUndefined(c)){if(!f.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function a(u,c,h){if(h in t)return s(u,c);if(h in e)return s(void 0,u)}const l={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(u,c,h)=>r(Js(u),Js(c),h,!0)};return f.forEach(Object.keys(Object.assign({},e,t)),function(c){const h=l[c]||r,g=h(e[c],t[c],c);f.isUndefined(g)&&h!==a||(n[c]=g)}),n}const yi=e=>{const t=St({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:i,headers:o,auth:a}=t;t.headers=o=V.from(o),t.url=mi(vi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(f.isFormData(n)){if(x.hasStandardBrowserEnv||x.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((l=o.getContentType())!==!1){const[u,...c]=l?l.split(";").map(h=>h.trim()).filter(Boolean):[];o.setContentType([u||"multipart/form-data",...c].join("; "))}}if(x.hasStandardBrowserEnv&&(s&&f.isFunction(s)&&(s=s(t)),s||s!==!1&&qf(t.url))){const u=r&&i&&zf.read(i);u&&o.set(r,u)}return t},Xf=typeof XMLHttpRequest<"u",Jf=Xf&&function(e){return new Promise(function(n,s){const r=yi(e);let i=r.data;const o=V.from(r.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=r,c,h,g,E,m;function _(){E&&E(),m&&m(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let p=new XMLHttpRequest;p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout;function v(){if(!p)return;const O=V.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),A={data:!a||a==="text"||a==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:O,config:e,request:p};bi(function(C){n(C),_()},function(C){s(C),_()},A),p=null}"onloadend"in p?p.onloadend=v:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(v)},p.onabort=function(){p&&(s(new T("Request aborted",T.ECONNABORTED,e,p)),p=null)},p.onerror=function(){s(new T("Network Error",T.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let y=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const A=r.transitional||_i;r.timeoutErrorMessage&&(y=r.timeoutErrorMessage),s(new T(y,A.clarifyTimeoutError?T.ETIMEDOUT:T.ECONNABORTED,e,p)),p=null},i===void 0&&o.setContentType(null),"setRequestHeader"in p&&f.forEach(o.toJSON(),function(y,A){p.setRequestHeader(A,y)}),f.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),a&&a!=="json"&&(p.responseType=r.responseType),u&&([g,m]=xe(u,!0),p.addEventListener("progress",g)),l&&p.upload&&([h,E]=xe(l),p.upload.addEventListener("progress",h),p.upload.addEventListener("loadend",E)),(r.cancelToken||r.signal)&&(c=O=>{p&&(s(!O||O.type?new Yt(null,e,p):O),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const w=Uf(r.url);if(w&&x.protocols.indexOf(w)===-1){s(new T("Unsupported protocol "+w+":",T.ERR_BAD_REQUEST,e));return}p.send(i||null)})},Qf=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const i=function(u){if(!r){r=!0,a();const c=u instanceof Error?u:this.reason;s.abort(c instanceof T?c:new Yt(c instanceof Error?c.message:c))}};let o=t&&setTimeout(()=>{o=null,i(new T(`timeout ${t} of ms exceeded`,T.ETIMEDOUT))},t);const a=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:l}=s;return l.unsubscribe=()=>f.asap(a),l}},Zf=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},td=async function*(e,t){for await(const n of ed(e))yield*Zf(n,t)},ed=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Qs=(e,t,n,s)=>{const r=td(e,t);let i=0,o,a=l=>{o||(o=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:u,value:c}=await r.next();if(u){a(),l.close();return}let h=c.byteLength;if(n){let g=i+=h;n(g)}l.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(l){return a(l),r.return()}},{highWaterMark:2})},Ye=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ai=Ye&&typeof ReadableStream=="function",nd=Ye&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ti=(e,...t)=>{try{return!!e(...t)}catch{return!1}},sd=Ai&&Ti(()=>{let e=!1;const t=new Request(x.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Zs=64*1024,Dn=Ai&&Ti(()=>f.isReadableStream(new Response("").body)),Ie={stream:Dn&&(e=>e.body)};Ye&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ie[t]&&(Ie[t]=f.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new T(`Response type '${t}' is not supported`,T.ERR_NOT_SUPPORT,s)})})})(new Response);const rd=async e=>{if(e==null)return 0;if(f.isBlob(e))return e.size;if(f.isSpecCompliantForm(e))return(await new Request(x.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(f.isArrayBufferView(e)||f.isArrayBuffer(e))return e.byteLength;if(f.isURLSearchParams(e)&&(e=e+""),f.isString(e))return(await nd(e)).byteLength},id=async(e,t)=>{const n=f.toFiniteNumber(e.getContentLength());return n??rd(t)},od=Ye&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:i,timeout:o,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:h="same-origin",fetchOptions:g}=yi(e);u=u?(u+"").toLowerCase():"text";let E=Qf([r,i&&i.toAbortSignal()],o),m;const _=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let p;try{if(l&&sd&&n!=="get"&&n!=="head"&&(p=await id(c,s))!==0){let A=new Request(t,{method:"POST",body:s,duplex:"half"}),S;if(f.isFormData(s)&&(S=A.headers.get("content-type"))&&c.setContentType(S),A.body){const[C,N]=Gs(p,xe(Xs(l)));s=Qs(A.body,Zs,C,N)}}f.isString(h)||(h=h?"include":"omit");const v="credentials"in Request.prototype;m=new Request(t,{...g,signal:E,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:v?h:void 0});let w=await fetch(m);const O=Dn&&(u==="stream"||u==="response");if(Dn&&(a||O&&_)){const A={};["status","statusText","headers"].forEach(D=>{A[D]=w[D]});const S=f.toFiniteNumber(w.headers.get("content-length")),[C,N]=a&&Gs(S,xe(Xs(a),!0))||[];w=new Response(Qs(w.body,Zs,C,()=>{N&&N(),_&&_()}),A)}u=u||"text";let y=await Ie[f.findKey(Ie,u)||"text"](w,e);return!O&&_&&_(),await new Promise((A,S)=>{bi(A,S,{data:y,headers:V.from(w.headers),status:w.status,statusText:w.statusText,config:e,request:m})})}catch(v){throw _&&_(),v&&v.name==="TypeError"&&/Load failed|fetch/i.test(v.message)?Object.assign(new T("Network Error",T.ERR_NETWORK,e,m),{cause:v.cause||v}):T.from(v,v&&v.code,e,m)}}),Ln={http:yf,xhr:Jf,fetch:od};f.forEach(Ln,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const tr=e=>`- ${e}`,ad=e=>f.isFunction(e)||e===null||e===!1,wi={getAdapter:e=>{e=f.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let i=0;i<t;i++){n=e[i];let o;if(s=n,!ad(n)&&(s=Ln[(o=String(n)).toLowerCase()],s===void 0))throw new T(`Unknown adapter '${o}'`);if(s)break;r[o||"#"+i]=s}if(!s){const i=Object.entries(r).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(tr).join(`
`):" "+tr(i[0]):"as no adapter specified";throw new T("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:Ln};function En(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Yt(null,e)}function er(e){return En(e),e.headers=V.from(e.headers),e.data=gn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),wi.getAdapter(e.adapter||ue.adapter)(e).then(function(s){return En(e),s.data=gn.call(e,e.transformResponse,s),s.headers=V.from(s.headers),s},function(s){return Ei(s)||(En(e),s&&s.response&&(s.response.data=gn.call(e,e.transformResponse,s.response),s.response.headers=V.from(s.response.headers))),Promise.reject(s)})}const Oi="1.9.0",Ge={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ge[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const nr={};Ge.transitional=function(t,n,s){function r(i,o){return"[Axios v"+Oi+"] Transitional option '"+i+"'"+o+(s?". "+s:"")}return(i,o,a)=>{if(t===!1)throw new T(r(o," has been removed"+(n?" in "+n:"")),T.ERR_DEPRECATED);return n&&!nr[o]&&(nr[o]=!0,console.warn(r(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,o,a):!0}};Ge.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function cd(e,t,n){if(typeof e!="object")throw new T("options must be an object",T.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const i=s[r],o=t[i];if(o){const a=e[i],l=a===void 0||o(a,i,e);if(l!==!0)throw new T("option "+i+" must be "+l,T.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new T("Unknown option "+i,T.ERR_BAD_OPTION)}}const Ne={assertOptions:cd,validators:Ge},Q=Ne.validators;let Tt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new zs,response:new zs}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const i=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=St(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:i}=n;s!==void 0&&Ne.assertOptions(s,{silentJSONParsing:Q.transitional(Q.boolean),forcedJSONParsing:Q.transitional(Q.boolean),clarifyTimeoutError:Q.transitional(Q.boolean)},!1),r!=null&&(f.isFunction(r)?n.paramsSerializer={serialize:r}:Ne.assertOptions(r,{encode:Q.function,serialize:Q.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ne.assertOptions(n,{baseUrl:Q.spelling("baseURL"),withXsrfToken:Q.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=i&&f.merge(i.common,i[n.method]);i&&f.forEach(["delete","get","head","post","put","patch","common"],m=>{delete i[m]}),n.headers=V.concat(o,i);const a=[];let l=!0;this.interceptors.request.forEach(function(_){typeof _.runWhen=="function"&&_.runWhen(n)===!1||(l=l&&_.synchronous,a.unshift(_.fulfilled,_.rejected))});const u=[];this.interceptors.response.forEach(function(_){u.push(_.fulfilled,_.rejected)});let c,h=0,g;if(!l){const m=[er.bind(this),void 0];for(m.unshift.apply(m,a),m.push.apply(m,u),g=m.length,c=Promise.resolve(n);h<g;)c=c.then(m[h++],m[h++]);return c}g=a.length;let E=n;for(h=0;h<g;){const m=a[h++],_=a[h++];try{E=m(E)}catch(p){_.call(this,p);break}}try{c=er.call(this,E)}catch(m){return Promise.reject(m)}for(h=0,g=u.length;h<g;)c=c.then(u[h++],u[h++]);return c}getUri(t){t=St(this.defaults,t);const n=vi(t.baseURL,t.url,t.allowAbsoluteUrls);return mi(n,t.params,t.paramsSerializer)}};f.forEach(["delete","get","head","options"],function(t){Tt.prototype[t]=function(n,s){return this.request(St(s||{},{method:t,url:n,data:(s||{}).data}))}});f.forEach(["post","put","patch"],function(t){function n(s){return function(i,o,a){return this.request(St(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}Tt.prototype[t]=n(),Tt.prototype[t+"Form"]=n(!0)});let ld=class Si{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const s=this;this.promise.then(r=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](r);s._listeners=null}),this.promise.then=r=>{let i;const o=new Promise(a=>{s.subscribe(a),i=a}).then(r);return o.cancel=function(){s.unsubscribe(i)},o},t(function(i,o,a){s.reason||(s.reason=new Yt(i,o,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Si(function(r){t=r}),cancel:t}}};function ud(e){return function(n){return e.apply(null,n)}}function fd(e){return f.isObject(e)&&e.isAxiosError===!0}const Rn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Rn).forEach(([e,t])=>{Rn[t]=e});function Ci(e){const t=new Tt(e),n=si(Tt.prototype.request,t);return f.extend(n,Tt.prototype,t,{allOwnKeys:!0}),f.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Ci(St(e,r))},n}const L=Ci(ue);L.Axios=Tt;L.CanceledError=Yt;L.CancelToken=ld;L.isCancel=Ei;L.VERSION=Oi;L.toFormData=ze;L.AxiosError=T;L.Cancel=L.CanceledError;L.all=function(t){return Promise.all(t)};L.spread=ud;L.isAxiosError=fd;L.mergeConfig=St;L.AxiosHeaders=V;L.formToJSON=e=>gi(f.isHTMLForm(e)?new FormData(e):e);L.getAdapter=wi.getAdapter;L.HttpStatusCode=Rn;L.default=L;const{Axios:pd,AxiosError:md,CanceledError:_d,isCancel:gd,CancelToken:Ed,VERSION:bd,all:vd,Cancel:yd,isAxiosError:Ad,spread:Td,toFormData:wd,AxiosHeaders:Od,HttpStatusCode:Sd,formToJSON:Cd,getAdapter:Nd,mergeConfig:Dd}=L;window.axios=L;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";
