<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarehouseInventory extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'warehouse_inventory';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'warehouse_id',
        'product_id',
        'quantity',
        'reserved_quantity',
        'reorder_level',
        'max_stock_level',
        'cost_price',
        'location_code',
        'last_restocked_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_restocked_at' => 'date',
        'cost_price' => 'decimal:2',
    ];

    /**
     * Get the warehouse that owns this inventory.
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the product for this inventory.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the available quantity (computed attribute).
     */
    public function getAvailableQuantityAttribute()
    {
        return max(0, $this->quantity - $this->reserved_quantity);
    }

    /**
     * Check if stock is low.
     */
    public function isLowStock()
    {
        return $this->quantity <= $this->reorder_level;
    }

    /**
     * Check if stock is out.
     */
    public function isOutOfStock()
    {
        return $this->quantity <= 0;
    }

    /**
     * Check if available quantity is sufficient.
     */
    public function hasAvailableStock($requiredQuantity = 1)
    {
        return $this->available_quantity >= $requiredQuantity;
    }

    /**
     * Reserve stock for an order.
     */
    public function reserveStock($quantity)
    {
        if ($this->available_quantity < $quantity) {
            throw new \Exception("Insufficient available stock. Available: {$this->available_quantity}, Required: {$quantity}");
        }

        $this->increment('reserved_quantity', $quantity);

        // Log the movement
        InventoryMovement::create([
            'warehouse_id' => $this->warehouse_id,
            'product_id' => $this->product_id,
            'type' => 'reserved',
            'quantity' => $quantity,
            'previous_quantity' => $this->quantity,
            'new_quantity' => $this->quantity,
            'notes' => "Reserved {$quantity} units for order",
        ]);

        return $this;
    }

    /**
     * Unreserve stock (e.g., when order is cancelled).
     */
    public function unreserveStock($quantity)
    {
        $unreserveAmount = min($quantity, $this->reserved_quantity);

        if ($unreserveAmount > 0) {
            $this->decrement('reserved_quantity', $unreserveAmount);

            // Log the movement
            InventoryMovement::create([
                'warehouse_id' => $this->warehouse_id,
                'product_id' => $this->product_id,
                'type' => 'unreserved',
                'quantity' => -$unreserveAmount,
                'previous_quantity' => $this->quantity,
                'new_quantity' => $this->quantity,
                'notes' => "Unreserved {$unreserveAmount} units",
            ]);
        }

        return $this;
    }

    /**
     * Fulfill reserved stock (convert reserved to sold).
     */
    public function fulfillReservedStock($quantity)
    {
        $fulfillAmount = min($quantity, $this->reserved_quantity);

        if ($fulfillAmount > 0) {
            $this->decrement('reserved_quantity', $fulfillAmount);
            $this->decrement('quantity', $fulfillAmount);

            // Log the movement
            InventoryMovement::create([
                'warehouse_id' => $this->warehouse_id,
                'product_id' => $this->product_id,
                'type' => 'out',
                'quantity' => -$fulfillAmount,
                'previous_quantity' => $this->quantity + $fulfillAmount,
                'new_quantity' => $this->quantity,
                'notes' => "Fulfilled {$fulfillAmount} units from reserved stock",
            ]);
        }

        return $this;
    }

    /**
     * Add stock to inventory.
     */
    public function addStock($quantity, $unitCost = null, $notes = null)
    {
        $previousQuantity = $this->quantity;
        $this->increment('quantity', $quantity);
        $this->last_restocked_at = now();
        $this->save();

        // Log the movement
        InventoryMovement::create([
            'warehouse_id' => $this->warehouse_id,
            'product_id' => $this->product_id,
            'type' => 'in',
            'quantity' => $quantity,
            'previous_quantity' => $previousQuantity,
            'new_quantity' => $this->quantity,
            'unit_cost' => $unitCost,
            'notes' => $notes ?: "Added {$quantity} units to inventory",
        ]);

        return $this;
    }

    /**
     * Remove stock from inventory.
     */
    public function removeStock($quantity, $notes = null)
    {
        if ($this->quantity < $quantity) {
            throw new \Exception("Insufficient stock. Available: {$this->quantity}, Required: {$quantity}");
        }

        $previousQuantity = $this->quantity;
        $this->decrement('quantity', $quantity);

        // Log the movement
        InventoryMovement::create([
            'warehouse_id' => $this->warehouse_id,
            'product_id' => $this->product_id,
            'type' => 'out',
            'quantity' => -$quantity,
            'previous_quantity' => $previousQuantity,
            'new_quantity' => $this->quantity,
            'notes' => $notes ?: "Removed {$quantity} units from inventory",
        ]);

        return $this;
    }

    /**
     * Adjust stock quantity (for corrections).
     */
    public function adjustStock($newQuantity, $notes = null)
    {
        $previousQuantity = $this->quantity;
        $difference = $newQuantity - $previousQuantity;

        $this->quantity = $newQuantity;
        $this->save();

        // Log the movement
        InventoryMovement::create([
            'warehouse_id' => $this->warehouse_id,
            'product_id' => $this->product_id,
            'type' => 'adjustment',
            'quantity' => $difference,
            'previous_quantity' => $previousQuantity,
            'new_quantity' => $newQuantity,
            'notes' => $notes ?: "Stock adjusted from {$previousQuantity} to {$newQuantity}",
        ]);

        return $this;
    }

    /**
     * Get stock status.
     */
    public function getStockStatus()
    {
        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        } elseif ($this->isLowStock()) {
            return 'low_stock';
        } elseif ($this->max_stock_level && $this->quantity >= $this->max_stock_level) {
            return 'overstock';
        } else {
            return 'in_stock';
        }
    }

    /**
     * Get stock status color.
     */
    public function getStockStatusColor()
    {
        switch ($this->getStockStatus()) {
            case 'out_of_stock':
                return 'danger';
            case 'low_stock':
                return 'warning';
            case 'overstock':
                return 'info';
            default:
                return 'success';
        }
    }

    /**
     * Scope for low stock items.
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('quantity <= reorder_level');
    }

    /**
     * Scope for out of stock items.
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('quantity', '<=', 0);
    }

    /**
     * Scope for in stock items.
     */
    public function scopeInStock($query)
    {
        return $query->where('quantity', '>', 0);
    }
}
