<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\QrLoginSession;
use App\Models\User;
use Carbon\Carbon;

class QrLoginController extends Controller
{
    /**
     * Generate a new QR code for login
     */
    public function generateQrCode(Request $request): JsonResponse
    {
        try {
            // Clean up expired sessions first
            QrLoginSession::cleanupExpired();

            // Create new session
            $session = QrLoginSession::createSession([
                'browser' => $request->userAgent(),
                'platform' => $this->detectPlatform($request->userAgent()),
            ]);

            // Generate QR code data
            $qrData = route('qr-login.scan', ['token' => $session->token]);

            return response()->json([
                'success' => true,
                'token' => $session->token,
                'qr_data' => $qrData,
                'qr_image' => "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($qrData),
                'one_time_code' => $session->one_time_code,
                'expires_at' => $session->expires_at->toISOString(),
            ]);
        } catch (\Exception $e) {
            Log::error('QR Code generation failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code'
            ], 500);
        }
    }

    /**
     * Check the status of a QR login session
     */
    public function checkStatus(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string|size:64'
        ]);

        $session = QrLoginSession::where('token', $request->token)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'status' => 'not_found'
            ], 404);
        }

        if ($session->isExpired()) {
            $session->update(['status' => 'expired']);
            return response()->json([
                'success' => false,
                'status' => 'expired'
            ]);
        }

        return response()->json([
            'success' => true,
            'status' => $session->status,
            'scanned_at' => $session->scanned_at?->toISOString(),
            'authenticated_at' => $session->authenticated_at?->toISOString(),
            'user' => $session->user ? [
                'name' => $session->user->name,
                'email' => $session->user->email,
            ] : null,
        ]);
    }

    /**
     * Handle QR code scanning (from mobile device)
     */
    public function scanQrCode(Request $request, string $token)
    {
        $session = QrLoginSession::where('token', $token)->first();

        if (!$session) {
            return view('qr-login.error', [
                'message' => 'QR code not found or invalid.'
            ]);
        }

        if (!$session->isValid()) {
            return view('qr-login.error', [
                'message' => 'QR code has expired or is no longer valid.'
            ]);
        }

        // If user is not logged in, redirect to login
        if (!Auth::check()) {
            return redirect()->route('login')->with('qr_token', $token);
        }

        // Show confirmation page
        return view('qr-login.confirm', [
            'session' => $session,
            'user' => Auth::user(),
        ]);
    }

    /**
     * Confirm QR login authentication
     */
    public function confirmLogin(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string|size:64'
        ]);

        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'You must be logged in to confirm authentication'
            ], 401);
        }

        $session = QrLoginSession::where('token', $request->token)->first();

        if (!$session || !$session->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired QR code'
            ], 400);
        }

        // Mark as scanned
        if ($session->markAsScanned(Auth::user())) {
            return response()->json([
                'success' => true,
                'message' => 'Authentication confirmed successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to confirm authentication'
        ], 500);
    }

    /**
     * Authenticate user via QR code (called by the original device)
     */
    public function authenticate(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string|size:64'
        ]);

        $session = QrLoginSession::where('token', $request->token)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        if ($session->status !== 'scanned') {
            return response()->json([
                'success' => false,
                'message' => 'QR code has not been scanned yet'
            ]);
        }

        if ($session->isExpired()) {
            return response()->json([
                'success' => false,
                'message' => 'Session has expired'
            ]);
        }

        // Mark as authenticated and log in the user
        if ($session->markAsAuthenticated()) {
            Auth::login($session->user);

            return response()->json([
                'success' => true,
                'message' => 'Authentication successful',
                'redirect_url' => route('home')
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Authentication failed'
        ], 500);
    }

    /**
     * Login with one-time code
     */
    public function loginWithCode(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|size:8'
        ]);

        $session = QrLoginSession::where('one_time_code', $request->code)
            ->where('status', 'scanned')
            ->first();

        if (!$session || $session->isExpired()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired code'
            ], 400);
        }

        // Mark as authenticated and log in the user
        if ($session->markAsAuthenticated()) {
            Auth::login($session->user);

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'redirect_url' => route('home')
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Login failed'
        ], 500);
    }

    /**
     * Cancel a QR login session
     */
    public function cancel(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string|size:64'
        ]);

        $session = QrLoginSession::where('token', $request->token)->first();

        if ($session && in_array($session->status, ['pending', 'scanned'])) {
            $session->update(['status' => 'cancelled']);

            return response()->json([
                'success' => true,
                'message' => 'QR login cancelled'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Session not found or cannot be cancelled'
        ], 400);
    }

    /**
     * Detect platform from user agent
     */
    private function detectPlatform(string $userAgent): string
    {
        if (stripos($userAgent, 'Windows') !== false) return 'Windows';
        if (stripos($userAgent, 'Mac') !== false) return 'macOS';
        if (stripos($userAgent, 'Linux') !== false) return 'Linux';
        if (stripos($userAgent, 'Android') !== false) return 'Android';
        if (stripos($userAgent, 'iPhone') !== false || stripos($userAgent, 'iPad') !== false) return 'iOS';

        return 'Unknown';
    }
}
