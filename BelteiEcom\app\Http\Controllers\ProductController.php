<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display a listing of all products.
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // Apply search filter
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
        }

        // Apply category filter
        if ($request->filled('category')) {
            $categoryId = $request->input('category');
            if (!empty($categoryId)) {
                $category = Category::find($categoryId);
                if ($category) {
                    $categoryIds = [$categoryId];

                    // If this is a parent category (has children), include all its children
                    if ($category->children->count() > 0) {
                        $childrenIds = $category->children->pluck('id')->toArray();
                        $categoryIds = array_merge($categoryIds, $childrenIds);
                    }

                    $query->whereIn('category_id', $categoryIds);
                }
            }
        }

        // Apply price range filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // Get organized categories (parent categories with their children)
        $categories = Category::whereNull('parent_id')->with('children')->orderBy('name')->get();

        // Paginate the results
        $products = $query->paginate(12);

        return view('products.index', compact('products', 'categories'));
    }

    /**
     * Display the specified product.
     */
    public function show(Product $product)
    {
        // Get related products from the same category
        $relatedProducts = Product::where('category_id', $product->category_id)
                                 ->where('id', '!=', $product->id)
                                 ->limit(4)
                                 ->get();

        // Get approved reviews with user and images
        $reviews = $product->approvedReviews()
                          ->with(['user', 'images'])
                          ->orderBy('is_featured', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->paginate(10);

        // Check if current user has already reviewed this product
        $userReview = null;
        if (auth()->check()) {
            $userReview = $product->reviews()
                                 ->where('user_id', auth()->id())
                                 ->first();
        }

        return view('products.show', compact('product', 'relatedProducts', 'reviews', 'userReview'));
    }

    /**
     * Display products by category.
     */
    public function byCategory(Category $category)
    {
        // Get category IDs to include in the search
        $categoryIds = [$category->id];

        // If this is a parent category (has children), include all its children
        if ($category->children->count() > 0) {
            $childrenIds = $category->children->pluck('id')->toArray();
            $categoryIds = array_merge($categoryIds, $childrenIds);
        }

        // Get products in these categories
        $products = Product::whereIn('category_id', $categoryIds)->paginate(12);

        // Get organized categories (parent categories with their children)
        $categories = Category::whereNull('parent_id')->with('children')->orderBy('name')->get();

        return view('products.index', compact('products', 'categories', 'category'));
    }

    /**
     * Live search for products (AJAX endpoint).
     */
    public function liveSearch(Request $request)
    {
        $query = $request->input('query');

        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }

        $products = Product::where('name', 'like', "%{$query}%")
                          ->orWhere('description', 'like', "%{$query}%")
                          ->limit(8)
                          ->get(['id', 'slug', 'name', 'price', 'image']);

        $results = $products->map(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'price' => number_format($product->price, 2),
                'image' => $product->image ? asset('storage/' . $product->image) : 'https://via.placeholder.com/60x60?text=No+Image&bg=f8f9fa&color=636e72',
                'url' => route('products.show', $product->slug)
            ];
        });

        return response()->json($results);
    }
}
