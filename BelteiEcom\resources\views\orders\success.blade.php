@extends('layouts.app')

@section('title', 'Order Placed Successfully')

@section('styles')
<style>
    .success-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .success-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 3rem 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .success-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateY(0px) rotate(0deg); }
        100% { transform: translateY(-20px) rotate(360deg); }
    }

    .success-hero > * {
        position: relative;
        z-index: 2;
    }

    .success-icon {
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 3rem;
        backdrop-filter: blur(10px);
        border: 3px solid rgba(255, 255, 255, 0.3);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .success-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .success-subtitle {
        font-size: 1.2rem;
        opacity: 0.95;
        margin-bottom: 0.5rem;
    }

    .order-number {
        font-size: 1.3rem;
        font-weight: 700;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        display: inline-block;
        margin: 1rem 0;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .payment-status {
        background: rgba(255, 255, 255, 0.15);
        padding: 1.5rem;
        border-radius: 15px;
        margin: 2rem 0;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .payment-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.8rem;
        backdrop-filter: blur(10px);
    }

    .payment-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .payment-description {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 2rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.875rem 1.75rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        font-size: 0.95rem;
    }

    .btn-primary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .btn-primary:hover {
        background: white;
        color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
        text-decoration: none;
    }

    .btn-secondary {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .btn-secondary:hover {
        background: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        text-decoration: none;
        color: white;
    }

    .order-details {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .details-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .details-header h3 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .details-content {
        padding: 2rem;
    }

    .order-info {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 2rem;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid #f0f0f0;
    }

    .order-meta h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
    }

    .order-date {
        color: #666;
        font-size: 0.95rem;
    }

    .status-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 700;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-new {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .items-section h4 {
        margin-bottom: 1.5rem;
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .order-item {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .order-item:hover {
        background: #f0f0f0;
        transform: translateY(-1px);
    }

    .item-image {
        width: 100px;
        height: 100px;
        border-radius: 15px;
        overflow: hidden;
        flex-shrink: 0;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .item-image:hover img {
        transform: scale(1.05);
    }

    .item-details {
        flex: 1;
    }

    .item-name {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
    }

    .item-name a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .item-name a:hover {
        color: #667eea;
    }

    .item-pricing {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 1rem;
        color: #666;
    }

    .item-total {
        font-weight: 700;
        color: #667eea;
        font-size: 1.1rem;
    }

    .summary-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #f0f0f0;
    }

    .shipping-info h4,
    .order-total h4 {
        margin-bottom: 1rem;
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-row {
        margin-bottom: 0.75rem;
    }

    .info-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }

    .info-value {
        color: #666;
        white-space: pre-line;
    }

    .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
    }

    .total-row.final {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        border-top: 2px solid #667eea;
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .payment-method-display {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
    }

    .payment-cash {
        color: #28a745;
    }

    .payment-khqr {
        color: #667eea;
    }

    .payment-note {
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 10px;
        font-size: 0.9rem;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .note-cash {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(34, 139, 34, 0.1) 100%);
        color: #155724;
        border: 2px solid #28a745;
    }

    .note-khqr {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        color: #0c5460;
        border: 2px solid #667eea;
    }

    @media (max-width: 768px) {
        .success-container {
            padding: 1rem;
        }

        .success-hero {
            padding: 2rem 1rem;
        }

        .success-title {
            font-size: 2rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .action-btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .order-info {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .summary-section {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .order-item {
            flex-direction: column;
            text-align: center;
        }

        .item-image {
            align-self: center;
        }

        .item-pricing {
            justify-content: center;
            gap: 1rem;
        }
    }
</style>
@endsection

@section('content')
<div class="success-container">
    <!-- Success Hero Section -->
    <div class="success-hero">
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>
        <h1 class="success-title">🎉 Order Placed Successfully!</h1>
        <p class="success-subtitle">Your order has been confirmed and is being processed.</p>
        <div class="order-number">Order #{{ $order->id }}</div>

        @if($order->payment_method === 'cash_on_delivery')
            <div class="payment-status">
                <div class="payment-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="payment-title">💵 Cash on Delivery</div>
                <p class="payment-description">Payment will be collected when your order is delivered to your address</p>
            </div>
        @else
            <div class="payment-status">
                <div class="payment-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="payment-title">💳 Bakong KHQR Payment</div>
                <p class="payment-description">Payment has been processed successfully through Bakong</p>
            </div>
        @endif

        <div class="action-buttons">
            <a href="{{ route('orders.show', $order->id) }}" class="action-btn btn-primary">
                <i class="fas fa-eye"></i> View Order Details
            </a>
            <a href="{{ route('orders.receipt.download', $order->pdf_token) }}" class="action-btn btn-secondary">
                <i class="fas fa-file-pdf"></i> Download Receipt PDF
            </a>
            <a href="{{ route('products.index') }}" class="action-btn btn-primary">
                <i class="fas fa-shopping-bag"></i> Continue Shopping
            </a>
        </div>
    </div>

    <!-- Order Details -->
    <div class="order-details">
        <div class="details-header">
            <h3><i class="fas fa-receipt"></i> Order Summary</h3>
        </div>
        <div class="details-content">
            <div class="order-info">
                <div class="order-meta">
                    <h4>Order #{{ $order->id }}</h4>
                    <p class="order-date">Placed on {{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('F j, Y, g:i a') }} (Cambodia Time)</p>
                </div>
                <div>
                    <span class="status-badge status-new">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>
            </div>

            <div class="items-section">
                <h4><i class="fas fa-box"></i> Items Ordered</h4>

                @foreach($order->orderItems as $item)
                    <div class="order-item">
                        <div class="item-image">
                            @if($item->product->image)
                                <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product->name }}">
                            @else
                                <img src="https://via.placeholder.com/100x100?text=No+Image" alt="No Image">
                            @endif
                        </div>
                        <div class="item-details">
                            <h4 class="item-name">
                                <a href="{{ route('products.show', $item->product->slug) }}">{{ $item->product->name }}</a>
                            </h4>
                            <div class="item-pricing">
                                <span>{{ $item->quantity }} x ${{ number_format($item->price, 2) }}</span>
                                <span class="item-total">${{ number_format($item->price * $item->quantity, 2) }}</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="summary-section">
                <div class="shipping-info">
                    <h4><i class="fas fa-truck"></i> Shipping Information</h4>
                    <div class="info-row">
                        <div class="info-label">Phone Number:</div>
                        <div class="info-value">{{ $order->phone_number }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Delivery Address:</div>
                        <div class="info-value">{{ $order->shipping_address }}</div>
                    </div>
                </div>

                <div class="order-total">
                    <h4><i class="fas fa-calculator"></i> Order Total</h4>

                    <div class="total-row">
                        <span>Subtotal ({{ $order->orderItems->count() }} items):</span>
                        <span>${{ number_format($order->total_amount, 2) }}</span>
                    </div>

                    <div class="total-row">
                        <span>Shipping:</span>
                        <span style="color: #28a745; font-weight: 600;">Free</span>
                    </div>

                    <div class="total-row">
                        <span>Tax:</span>
                        <span>Included</span>
                    </div>

                    <div class="total-row">
                        <span>Payment Method:</span>
                        <span class="payment-method-display">
                            @if($order->payment_method === 'cash_on_delivery')
                                <span class="payment-cash">
                                    <i class="fas fa-money-bill-wave"></i> Cash on Delivery
                                </span>
                            @else
                                <span class="payment-khqr">
                                    <i class="fas fa-qrcode"></i> Bakong KHQR
                                </span>
                            @endif
                        </span>
                    </div>

                    <div class="total-row final">
                        <span>Total:</span>
                        <span>${{ number_format($order->total_amount, 2) }}</span>
                    </div>

                    @if($order->payment_method === 'cash_on_delivery')
                        <div class="payment-note note-cash">
                            <i class="fas fa-info-circle"></i>
                            <div>
                                <strong>Cash on Delivery:</strong> Please have the exact amount ready when your order arrives. Our delivery team will collect the payment upon delivery.
                            </div>
                        </div>
                    @else
                        <div class="payment-note note-khqr">
                            <i class="fas fa-check-circle"></i>
                            <div>
                                <strong>Payment Confirmed:</strong> Your Bakong KHQR payment has been successfully processed. No additional payment is required.
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
