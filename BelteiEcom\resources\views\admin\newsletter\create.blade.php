@extends('admin.layouts.app')

@section('title', 'Send Newsletter')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">📧 Send Newsletter</h1>
            <p class="text-muted">Compose and send newsletter to {{ number_format($subscriberCount) }} subscribers</p>
        </div>
        <div>
            <a href="{{ route('admin.newsletter.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Subscribers
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    @endif

    <div class="row">
        <!-- Newsletter Composer -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i> Compose Newsletter
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.newsletter.send') }}" method="POST" id="newsletterForm">
                        @csrf
                        
                        <!-- Subject Line -->
                        <div class="form-group">
                            <label for="subject" class="font-weight-bold">
                                <i class="fas fa-tag"></i> Subject Line
                            </label>
                            <input type="text" 
                                   class="form-control @error('subject') is-invalid @enderror" 
                                   id="subject" 
                                   name="subject" 
                                   placeholder="Enter newsletter subject..."
                                   value="{{ old('subject') }}"
                                   required>
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                Keep it engaging and under 50 characters for best results
                            </small>
                        </div>

                        <!-- Message Content -->
                        <div class="form-group">
                            <label for="message" class="font-weight-bold">
                                <i class="fas fa-align-left"></i> Newsletter Content
                            </label>
                            <textarea class="form-control @error('message') is-invalid @enderror" 
                                      id="message" 
                                      name="message" 
                                      rows="12" 
                                      placeholder="Write your newsletter content here..."
                                      required>{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">
                                You can use line breaks for paragraphs. Links will be automatically formatted.
                            </small>
                        </div>

                        <!-- Send Options -->
                        <div class="form-group">
                            <label class="font-weight-bold">
                                <i class="fas fa-paper-plane"></i> Send Options
                            </label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="send_to" id="send_test" value="test" checked>
                                <label class="form-check-label" for="send_test">
                                    <i class="fas fa-flask text-warning"></i> Send Test Email (to yourself)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="send_to" id="send_all" value="all">
                                <label class="form-check-label" for="send_all">
                                    <i class="fas fa-users text-primary"></i> Send to All Subscribers ({{ number_format($subscriberCount) }} people)
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary btn-lg" id="sendButton">
                                <i class="fas fa-paper-plane"></i> Send Newsletter
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ml-2" onclick="previewNewsletter()">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Newsletter Tips -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-lightbulb"></i> Newsletter Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="tip-item mb-3">
                        <h6 class="text-primary">📝 Subject Line</h6>
                        <p class="small text-muted mb-0">Keep it short, clear, and engaging. Use emojis sparingly for visual appeal.</p>
                    </div>
                    
                    <div class="tip-item mb-3">
                        <h6 class="text-primary">📖 Content</h6>
                        <p class="small text-muted mb-0">Write in a friendly, conversational tone. Include valuable information or offers.</p>
                    </div>
                    
                    <div class="tip-item mb-3">
                        <h6 class="text-primary">🎯 Call to Action</h6>
                        <p class="small text-muted mb-0">Include clear next steps for readers, like "Shop Now" or "Learn More".</p>
                    </div>
                    
                    <div class="tip-item mb-3">
                        <h6 class="text-primary">🧪 Test First</h6>
                        <p class="small text-muted mb-0">Always send a test email to yourself before broadcasting to all subscribers.</p>
                    </div>
                </div>
            </div>

            <!-- Subscriber Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-chart-bar"></i> Subscriber Stats
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <h3 class="text-primary">{{ number_format($subscriberCount) }}</h3>
                        <p class="text-muted mb-0">Active Subscribers</p>
                    </div>
                    
                    @if($subscriberCount > 0)
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Your newsletter will reach <strong>{{ number_format($subscriberCount) }}</strong> people!
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            No subscribers yet. Promote your newsletter to grow your audience!
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> Newsletter Preview
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="preview-content">
                    <div class="email-preview" style="border: 1px solid #ddd; border-radius: 10px; padding: 20px; background: #f8f9fa;">
                        <div class="preview-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
                            <h3 id="previewSubject">Newsletter Subject</h3>
                            <p>{{ config('app.name') }} Newsletter</p>
                        </div>
                        <div class="preview-body" id="previewContent">
                            Newsletter content will appear here...
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="$('#previewModal').modal('hide'); $('#newsletterForm').submit();">
                    <i class="fas fa-paper-plane"></i> Send Newsletter
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function previewNewsletter() {
    const subject = document.getElementById('subject').value || 'Newsletter Subject';
    const content = document.getElementById('message').value || 'Newsletter content will appear here...';
    
    document.getElementById('previewSubject').textContent = subject;
    document.getElementById('previewContent').innerHTML = content.replace(/\n/g, '<br>');
    
    $('#previewModal').modal('show');
}

// Form submission handling
document.getElementById('newsletterForm').addEventListener('submit', function(e) {
    const sendButton = document.getElementById('sendButton');
    const sendToAll = document.getElementById('send_all').checked;
    
    if (sendToAll) {
        if (!confirm('Are you sure you want to send this newsletter to all {{ number_format($subscriberCount) }} subscribers? This action cannot be undone.')) {
            e.preventDefault();
            return;
        }
    }
    
    // Show loading state
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    sendButton.disabled = true;
});
</script>
@endsection
