<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bakong API Documentation - BelteiEcom</title>
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <style>
        :root {
            --primary-color: #0077b6;
            --secondary-color: #00b4d8;
            --accent-color: #90e0ef;
            --light-color: #caf0f8;
            --dark-color: #03045e;
            --code-bg: #f8f9fa;
            --code-border: #e9ecef;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .api-docs {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
        }

        .api-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .api-title {
            color: var(--primary-color);
            font-size: 28px;
            margin-bottom: 10px;
        }

        .api-description {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }

        .endpoint {
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .endpoint h3 {
            color: var(--dark-color);
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--accent-color);
            padding-bottom: 10px;
        }

        .endpoint-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .detail-label {
            font-weight: bold;
            color: var(--dark-color);
            width: 120px;
            flex-shrink: 0;
        }

        .detail-value {
            flex-grow: 1;
        }

        .code-block {
            background-color: var(--code-bg);
            border: 1px solid var(--code-border);
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', Courier, monospace;
        }

        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            background-color: #4CAF50;
        }

        .method.post {
            background-color: #FF9800;
        }

        .method.put {
            background-color: #2196F3;
        }

        .method.delete {
            background-color: #F44336;
        }

        .url {
            font-family: 'Courier New', Courier, monospace;
            background-color: #f5f5f5;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
        }

        .section-title {
            color: var(--primary-color);
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .response-example {
            margin-top: 20px;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
        }

        .tab-button {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: none;
            cursor: pointer;
            transition: 0.3s;
        }

        .tab-button:hover {
            background-color: #ddd;
        }

        .tab-button.active {
            background-color: var(--primary-color);
            color: white;
        }

        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
        }

        .tab-content.active {
            display: block;
        }

        .code-block {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .note {
            background-color: #fff8dc;
            border-left: 4px solid #ffeb3b;
            padding: 10px 15px;
            margin-top: 10px;
            border-radius: 0 4px 4px 0;
        }

        .note p {
            margin: 0;
            color: #333;
        }

        .note strong {
            color: #e65100;
        }

        .note code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }

        @media (max-width: 768px) {
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="api-docs">
            <div class="api-header">
                <h1 class="api-title">Bakong API Documentation</h1>
                <p class="api-description">Documentation for the Bakong payment integration API endpoints</p>
            </div>

            <div class="endpoint">
                <h3>Check Transaction Status with Bakong API</h3>
                <div class="endpoint-details">
                    <div class="detail-row">
                        <span class="detail-label">Endpoint:</span>
                        <span class="detail-value">
                            <span class="method post">POST</span>
                            <span class="url">/api/bakong/check-transaction</span>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Description:</span>
                        <span class="detail-value">Checks the status of a transaction with the Bakong API using the MD5 hash</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Authentication:</span>
                        <span class="detail-value">Bearer Token (Pass the Bakong API token in the Authorization header)</span>
                    </div>
                </div>

                <h4 class="section-title">Request Headers</h4>
                <div class="code-block">
                    Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiY2FiNDcyNmUyMWYwNDVmZiJ9LCJpYXQiOjE3MzU3MTcyNzQsImV4cCI6MTc0MzQ5MzI3NH0.HqwrkpainZZ2b6jchuwnBaz54d2m8z_JLEIgLOfYZ6Y<br>
                    Content-Type: application/json
                </div>

                <h4 class="section-title">Request Body</h4>
                <div class="code-block">
                    {<br>
                    &nbsp;&nbsp;"md5": "2c74f66650a02c921bdfa30d62eefe5d"<br>
                    }
                </div>

                <h4 class="section-title">Response Examples</h4>
                <div class="tab-container">
                    <div class="tab-buttons">
                        <button class="tab-button active" onclick="openTab(event, 'success')">Success</button>
                        <button class="tab-button" onclick="openTab(event, 'error')">Error</button>
                        <button class="tab-button" onclick="openTab(event, 'not-found')">Not Found</button>
                    </div>

                    <div id="success" class="tab-content active">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"responseCode": 0,<br>
                            &nbsp;&nbsp;"responseMessage": "Getting transaction successfully.",<br>
                            &nbsp;&nbsp;"errorCode": null,<br>
                            &nbsp;&nbsp;"data": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"hash": "e40a...",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"fromAccountId": "developer@cmcb",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"toAccountId": "chhunlichhean_kun@wing",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"currency": "USD",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"amount": 0.01,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"description": "",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"createdDateMs": *************.0,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"acknowledgedDateMs": *************.0<br>
                            &nbsp;&nbsp;}<br>
                            }
                        </div>
                    </div>

                    <div id="error" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"data": null,<br>
                            &nbsp;&nbsp;"errorCode": 3,<br>
                            &nbsp;&nbsp;"responseCode": 1,<br>
                            &nbsp;&nbsp;"responseMessage": "Transaction failed."<br>
                            }
                        </div>
                    </div>

                    <div id="not-found" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"data": null,<br>
                            &nbsp;&nbsp;"errorCode": 1,<br>
                            &nbsp;&nbsp;"responseCode": 1,<br>
                            &nbsp;&nbsp;"responseMessage": "Transaction could not be found. Please check and try again."<br>
                            }
                        </div>
                    </div>
                </div>

                <h4 class="section-title">Postman Example</h4>
                <p>To test this API with Postman:</p>
                <ol>
                    <li>Create a new request in Postman</li>
                    <li>Set the request method to <strong>POST</strong></li>
                    <li>Enter the URL: <code>http://127.0.0.1:8000/api/bakong/check-transaction</code></li>
                    <li>In the Headers tab, add:
                        <ul>
                            <li><strong>Authorization</strong>: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiY2FiNDcyNmUyMWYwNDVmZiJ9LCJpYXQiOjE3MzU3MTcyNzQsImV4cCI6MTc0MzQ5MzI3NH0.HqwrkpainZZ2b6jchuwnBaz54d2m8z_JLEIgLOfYZ6Y</li>
                            <li><strong>Content-Type</strong>: application/json</li>
                        </ul>
                    </li>
                    <li>In the Body tab, select "raw" and JSON format, then enter:
                        <pre>{
  "md5": "2c74f66650a02c921bdfa30d62eefe5d"
}</pre>
                    </li>
                    <li>Click Send to make the request</li>
                </ol>
            </div>

            <div class="endpoint">
                <h3>Check Transaction Status by Order Number</h3>
                <div class="endpoint-details">
                    <div class="detail-row">
                        <span class="detail-label">Endpoint:</span>
                        <span class="detail-value">
                            <span class="method post">POST</span>
                            <span class="url">/api/bakong/check-order-transaction</span>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Description:</span>
                        <span class="detail-value">Directly calls the Bakong API to check the status of a transaction for a specific order number using the transaction_id (MD5) stored in the bakong_payments table. Returns the exact raw response from the Bakong API without any modifications.</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Authentication:</span>
                        <span class="detail-value">None required</span>
                    </div>
                </div>

                <h4 class="section-title">Request Headers</h4>
                <div class="code-block">
                    Content-Type: application/json
                </div>

                <h4 class="section-title">Request Body</h4>
                <div class="code-block">
                    {<br>
                    &nbsp;&nbsp;"order": "26"<br>
                    }
                </div>

                <h4 class="section-title">Response Examples</h4>
                <div class="tab-container">
                    <div class="tab-buttons">
                        <button class="tab-button active" onclick="openTab(event, 'order-success')">Success (Full)</button>
                        <button class="tab-button" onclick="openTab(event, 'order-success-alt')">Success (Alt)</button>
                        <button class="tab-button" onclick="openTab(event, 'order-not-bakong')">Not Bakong</button>
                        <button class="tab-button" onclick="openTab(event, 'order-not-found')">Not Found</button>
                        <button class="tab-button" onclick="openTab(event, 'order-pending')">Pending</button>
                        <button class="tab-button" onclick="openTab(event, 'order-unauthorized')">Unauthorized</button>
                    </div>

                    <div id="order-success" class="tab-content active">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"order_id": "31",<br>
                            &nbsp;&nbsp;"transaction_id": "0536d1d33d664741d265aada49b59a39",<br>
                            &nbsp;&nbsp;"payment_id": 26,<br>
                            &nbsp;&nbsp;"payment_status": "completed",<br>
                            &nbsp;&nbsp;"bakong_response": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseCode": 0,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseMessage": "Success",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"errorCode": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"hash": "5a0983f6eaa1ea05f193729ee370f8ce0b5a0a492be7e399a893b04dadb9c0f7",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"fromAccountId": "abaakhppxxx@abaa",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"toAccountId": "chhunlichhean_kun@wing",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"currency": "USD",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"amount": 0.01,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"description": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"createdDateMs": *************,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"acknowledgedDateMs": *************,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"trackingStatus": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"receiverBank": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"receiverBankAccount": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"instructionRef": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"externalRef": "100FT34618929401"<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;}<br>
                            &nbsp;&nbsp;}<br>
                            }
                        </div>
                        <div class="note">
                            <p><strong>Note:</strong> The <code>hash</code> value in the Bakong API response is different from the <code>transaction_id</code> we send. This is because Bakong uses its own internal hash for the transaction.</p>
                        </div>
                    </div>

                    <div id="order-success-alt" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"order_id": "31",<br>
                            &nbsp;&nbsp;"transaction_id": "0536d1d33d664741d265aada49b59a39",<br>
                            &nbsp;&nbsp;"payment_id": 26,<br>
                            &nbsp;&nbsp;"payment_status": "completed",<br>
                            &nbsp;&nbsp;"bakong_response": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseCode": 0,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseMessage": "Getting transaction successfully.",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"errorCode": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"data": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"hash": "e40a7c93f5d2bc3631c3e8c6f6806df89a2e647edaff3c45eca10cb36295a9",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"fromAccountId": "customer@bakong",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"toAccountId": "chhunlichhean_kun@wing",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"currency": "USD",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"amount": 0.01,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"description": "Payment for order 31",<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"createdDateMs": *************,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"acknowledgedDateMs": *************<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;}<br>
                            &nbsp;&nbsp;}<br>
                            }
                        </div>
                    </div>

                    <div id="order-not-bakong" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"success": false,<br>
                            &nbsp;&nbsp;"message": "This order was not paid with Bakong/KHQR",<br>
                            &nbsp;&nbsp;"order_id": "26",<br>
                            &nbsp;&nbsp;"payment_method": "cod"<br>
                            }
                        </div>
                    </div>

                    <div id="order-not-found" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"success": false,<br>
                            &nbsp;&nbsp;"message": "Order not found",<br>
                            &nbsp;&nbsp;"order_id": "999"<br>
                            }
                        </div>
                    </div>

                    <div id="order-pending" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"order_id": "31",<br>
                            &nbsp;&nbsp;"transaction_id": "0536d1d33d664741d265aada49b59a39",<br>
                            &nbsp;&nbsp;"payment_id": 26,<br>
                            &nbsp;&nbsp;"payment_status": "pending",<br>
                            &nbsp;&nbsp;"bakong_response": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"data": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 1,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseCode": 1,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseMessage": "Transaction could not be found. Please check and try again."<br>
                            &nbsp;&nbsp;}<br>
                            }
                        </div>
                    </div>

                    <div id="order-unauthorized" class="tab-content">
                        <div class="code-block">
                            {<br>
                            &nbsp;&nbsp;"order_id": "31",<br>
                            &nbsp;&nbsp;"transaction_id": "0536d1d33d664741d265aada49b59a39",<br>
                            &nbsp;&nbsp;"payment_id": 26,<br>
                            &nbsp;&nbsp;"payment_status": "pending",<br>
                            &nbsp;&nbsp;"bakong_response": {<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"data": null,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"errorCode": 6,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseCode": 1,<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;"responseMessage": "Unauthorized, not yet requested for token or code invalid"<br>
                            &nbsp;&nbsp;}<br>
                            }
                        </div>
                        <div class="note">
                            <p><strong>Note:</strong> This error occurs when the Bakong API token is invalid or expired. Please update the <code>BAKONG_API_TOKEN</code> in the <code>.env</code> file with a valid token from Bakong.</p>
                        </div>
                    </div>
                </div>

                <h4 class="section-title">Postman Example</h4>
                <p>To test this API with Postman:</p>
                <ol>
                    <li>Create a new request in Postman</li>
                    <li>Set the request method to <strong>POST</strong></li>
                    <li>Enter the URL: <code>http://127.0.0.1:8000/api/bakong/check-order-transaction</code></li>
                    <li>In the Headers tab, add:
                        <ul>
                            <li><strong>Content-Type</strong>: application/json</li>
                        </ul>
                    </li>
                    <li>In the Body tab, select "raw" and JSON format, then enter:
                        <pre>{
  "order": "26"
}</pre>
                    </li>
                    <li>Click Send to make the request</li>
                </ol>

                <p>Note: This endpoint will only work for orders that were paid with Bakong/KHQR. It will return an error for orders with other payment methods like Cash on Delivery.</p>
            </div>
        </div>
    </div>

    <script>
        function openTab(evt, tabName) {
            // Get the parent tab container
            var tabContainer = evt.currentTarget.closest('.tab-container');

            // Hide all tab content within this container
            var tabcontent = tabContainer.getElementsByClassName("tab-content");
            for (var i = 0; i < tabcontent.length; i++) {
                tabcontent[i].classList.remove("active");
            }

            // Remove "active" class from all tab buttons within this container
            var tabbuttons = tabContainer.getElementsByClassName("tab-button");
            for (var i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].classList.remove("active");
            }

            // Show the current tab and add "active" class to the button
            document.getElementById(tabName).classList.add("active");
            evt.currentTarget.classList.add("active");
        }

        // Initialize all tab containers
        document.addEventListener('DOMContentLoaded', function() {
            var tabContainers = document.getElementsByClassName('tab-container');
            for (var i = 0; i < tabContainers.length; i++) {
                var activeButton = tabContainers[i].querySelector('.tab-button.active');
                if (activeButton) {
                    var tabId = activeButton.getAttribute('onclick').match(/openTab\(event,\s*['"]([^'"]+)['"]\)/)[1];
                    document.getElementById(tabId).classList.add('active');
                }
            }
        });
    </script>
</body>
</html>
