<?php

use App\Http\Controllers\CartController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\AdminProductsController;
use App\Http\Controllers\AdminCategoriesController;
use App\Http\Controllers\AdminOrdersController;
use App\Http\Controllers\AdminCustomersController;
use App\Http\Controllers\AdminUsersController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Redirect /home to / for backward compatibility
Route::get('/home', function () {
    return redirect('/');
});

// Product routes
Route::get('/products', [ProductController::class, 'index'])->name('products.index');
Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
Route::get('/categories/{category}', [ProductController::class, 'byCategory'])->name('products.category');
Route::get('/products/search/live', [ProductController::class, 'liveSearch'])->name('products.live-search');

// Authentication routes with email verification
Auth::routes(['verify' => false]); // Disable default verification routes

// Custom email verification routes
Route::get('/email/verify', [App\Http\Controllers\EmailVerificationController::class, 'notice'])
    ->middleware('auth')->name('verification.notice');

Route::get('/email/verify/{id}/{hash}', [App\Http\Controllers\EmailVerificationController::class, 'verify'])
    ->middleware(['auth', 'signed'])->name('verification.verify');

Route::post('/email/verification-notification', [App\Http\Controllers\EmailVerificationController::class, 'resend'])
    ->middleware(['auth', 'throttle:6,1'])->name('verification.resend');

// Social Authentication routes
Route::get('/auth/google', [App\Http\Controllers\SocialAuthController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [App\Http\Controllers\SocialAuthController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// QR Code Login routes
Route::prefix('qr-login')->name('qr-login.')->group(function () {
    // Generate QR code for login (public)
    Route::post('/generate', [App\Http\Controllers\QrLoginController::class, 'generateQrCode'])->name('generate');

    // Check QR login status (public)
    Route::post('/status', [App\Http\Controllers\QrLoginController::class, 'checkStatus'])->name('status');

    // Scan QR code (public - accessed from mobile device)
    Route::get('/scan/{token}', [App\Http\Controllers\QrLoginController::class, 'scanQrCode'])->name('scan');

    // Confirm login (requires authentication)
    Route::post('/confirm', [App\Http\Controllers\QrLoginController::class, 'confirmLogin'])->middleware('auth')->name('confirm');

    // Authenticate via QR (public - for the original device)
    Route::post('/authenticate', [App\Http\Controllers\QrLoginController::class, 'authenticate'])->name('authenticate');

    // Login with one-time code (public)
    Route::post('/code', [App\Http\Controllers\QrLoginController::class, 'loginWithCode'])->name('code');

    // Cancel QR login session (public)
    Route::post('/cancel', [App\Http\Controllers\QrLoginController::class, 'cancel'])->name('cancel');
});

// User Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [App\Http\Controllers\UserProfileController::class, 'index'])->name('profile.index');
    Route::get('/profile/edit', [App\Http\Controllers\UserProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [App\Http\Controllers\UserProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile/picture', [App\Http\Controllers\UserProfileController::class, 'removeProfilePicture'])->name('profile.remove-picture');
    Route::get('/profile/password', [App\Http\Controllers\UserProfileController::class, 'password'])->name('profile.password');
    Route::put('/profile/password', [App\Http\Controllers\UserProfileController::class, 'updatePassword'])->name('profile.password.update');
});

// Newsletter routes
Route::post('/newsletter/subscribe', [App\Http\Controllers\NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
Route::get('/newsletter/confirm/{token}', [App\Http\Controllers\NewsletterController::class, 'confirm'])->name('newsletter.confirm');
Route::get('/newsletter/unsubscribe/{token}', [App\Http\Controllers\NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');

// Test newsletter subscription with name lookup
Route::get('/test-newsletter-subscribe/{email}', function($email) {
    try {
        $controller = new \App\Http\Controllers\NewsletterController();
        $request = new \Illuminate\Http\Request(['email' => $email]);

        // Call the subscribe method
        $response = $controller->subscribe($request);
        $data = $response->getData(true);

        // Get the created newsletter
        $newsletter = \App\Models\Newsletter::where('email', $email)->first();

        return response()->json([
            'success' => $data['success'],
            'message' => $data['message'],
            'subscriber' => $newsletter,
            'admin_url' => route('admin.newsletter.index')
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to create subscription: ' . $e->getMessage()
        ]);
    }
});

// Test newsletter welcome email
Route::get('/test-newsletter-email/{email}', function($email) {
    try {
        // Find or create newsletter subscription
        $newsletter = \App\Models\Newsletter::where('email', $email)->first();
        if (!$newsletter) {
            $newsletter = \App\Models\Newsletter::create([
                'email' => $email,
                'name' => 'Test User',
                'is_active' => true,
                'subscribed_at' => now()
            ]);
        }

        // Send welcome email
        \Illuminate\Support\Facades\Mail::send('emails.newsletter-welcome', ['newsletter' => $newsletter], function ($message) use ($newsletter) {
            $message->to($newsletter->email, $newsletter->name)
                    ->subject('🎉 Welcome to ' . config('app.name') . ' Newsletter!')
                    ->from(config('mail.from.address'), config('mail.from.name'));
        });

        return response()->json([
            'success' => true,
            'message' => 'Newsletter welcome email sent successfully!',
            'sent_to' => $email,
            'subscriber' => $newsletter,
            'mail_config' => [
                'driver' => config('mail.default'),
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name')
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send newsletter email: ' . $e->getMessage(),
            'error_details' => $e->getTraceAsString()
        ]);
    }
});

// Check newsletter subscription status
Route::get('/check-newsletter-status/{email?}', function($email = null) {
    if (!$email && auth()->check()) {
        $email = auth()->user()->email;
    }

    if (!$email) {
        return response()->json([
            'success' => false,
            'message' => 'No email provided and user not logged in'
        ]);
    }

    $newsletter = \App\Models\Newsletter::where('email', $email)->first();

    return response()->json([
        'success' => true,
        'email' => $email,
        'subscribed' => $newsletter ? $newsletter->is_active : false,
        'subscription_date' => $newsletter ? $newsletter->subscribed_at : null,
        'subscriber_name' => $newsletter ? $newsletter->name : null,
        'unsubscribe_token' => $newsletter ? $newsletter->subscription_token : null
    ]);
});

// Quick subscribe route for testing
Route::get('/quick-subscribe/{email}', function($email) {
    try {
        // Check if already subscribed
        $existing = \App\Models\Newsletter::where('email', $email)->first();

        if ($existing && $existing->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Already subscribed!',
                'subscriber' => $existing
            ]);
        }

        // Create or reactivate subscription
        if ($existing) {
            $existing->update([
                'is_active' => true,
                'unsubscribed_at' => null
            ]);
            $newsletter = $existing;
        } else {
            // Get name from user account if exists
            $user = \App\Models\User::where('email', $email)->first();
            $name = $user ? $user->name : null;

            $newsletter = \App\Models\Newsletter::create([
                'email' => $email,
                'name' => $name,
                'is_active' => true,
                'subscribed_at' => now()
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Successfully subscribed!',
            'subscriber' => $newsletter,
            'admin_url' => route('admin.newsletter.index')
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to subscribe: ' . $e->getMessage()
        ]);
    }
});

// Test newsletter confirmation email
Route::get('/test-newsletter-confirmation/{email}', function($email) {
    try {
        // Create a test newsletter subscription
        $newsletter = \App\Models\Newsletter::create([
            'email' => $email,
            'name' => 'Test User',
            'is_active' => false,
            'email_confirmed' => false,
            'subscribed_at' => now()
        ]);

        // Send confirmation email
        \Illuminate\Support\Facades\Mail::send('emails.newsletter-confirmation', ['newsletter' => $newsletter], function ($message) use ($newsletter) {
            $message->to($newsletter->email, $newsletter->name)
                    ->subject('📧 Please Confirm Your Newsletter Subscription - ' . config('app.name'))
                    ->from(config('mail.from.address'), config('mail.from.name'));
        });

        return response()->json([
            'success' => true,
            'message' => 'Newsletter confirmation email sent successfully!',
            'sent_to' => $email,
            'subscriber' => $newsletter,
            'confirmation_url' => route('newsletter.confirm', $newsletter->confirmation_token),
            'mail_config' => [
                'driver' => config('mail.default'),
                'host' => config('mail.mailers.smtp.host'),
                'from_address' => config('mail.from.address')
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send confirmation email: ' . $e->getMessage(),
            'error_details' => $e->getTraceAsString()
        ]);
    }
});

// Test admin sidebar
Route::get('/test-admin-sidebar', function() {
    return response()->json([
        'message' => 'Admin sidebar should now have working toggle functionality',
        'features' => [
            'Click the toggle button at bottom of sidebar',
            'Sidebar should smoothly collapse to 80px width',
            'Icons should remain visible with tooltips on hover',
            'Content area should adjust automatically',
            'State should persist between page loads'
        ],
        'admin_url' => route('admin.dashboard')
    ]);
});

// Test Google OAuth configuration
Route::get('/test-google-config', function() {
    return response()->json([
        'google_client_id' => config('services.google.client_id'),
        'google_redirect' => config('services.google.redirect'),
        'google_client_secret_set' => !empty(config('services.google.client_secret')),
        'socialite_installed' => class_exists('Laravel\Socialite\Facades\Socialite'),
    ]);
});

// Test product slugs
Route::get('/test-product-slugs', function() {
    $products = \App\Models\Product::take(5)->get(['id', 'slug', 'name']);
    $results = [];

    foreach($products as $product) {
        $results[] = [
            'name' => $product->name,
            'old_url' => url("/products/{$product->id}"),
            'new_url' => route('products.show', $product->slug),
            'slug' => $product->slug,
            'id' => $product->id
        ];
    }

    return response()->json([
        'message' => 'Product URLs now use secure slugs instead of exposed IDs',
        'examples' => $results
    ]);
});

// Test category slugs
Route::get('/test-category-slugs', function() {
    $categories = \App\Models\Category::take(10)->get(['id', 'slug', 'name']);
    $results = [];

    foreach($categories as $category) {
        $results[] = [
            'name' => $category->name,
            'old_url' => url("/categories/{$category->id}"),
            'new_url' => route('products.category', $category->slug),
            'slug' => $category->slug,
            'id' => $category->id
        ];
    }

    return response()->json([
        'message' => 'Category URLs now use secure slugs instead of exposed IDs',
        'examples' => $results
    ]);
});

// Test email configuration
Route::get('/test-email-config', function() {
    return response()->json([
        'mail_mailer' => config('mail.default'),
        'mail_host' => config('mail.mailers.smtp.host'),
        'mail_port' => config('mail.mailers.smtp.port'),
        'mail_username' => config('mail.mailers.smtp.username') ? 'Set' : 'Not set',
        'mail_password' => config('mail.mailers.smtp.password') ? 'Set' : 'Not set',
        'mail_encryption' => config('mail.mailers.smtp.encryption'),
        'mail_from_address' => config('mail.from.address'),
        'mail_from_name' => config('mail.from.name'),
        'verification_enabled' => true,
        'custom_notification' => class_exists('App\Notifications\CustomVerifyEmail'),
    ]);
});

// Test sending verification email
Route::get('/test-send-verification', function() {
    try {
        // Create a test user (don't save to database)
        $testUser = new \App\Models\User([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'email_verified_at' => null,
        ]);
        $testUser->id = 999; // Fake ID for testing

        // Send verification email
        $testUser->sendEmailVerificationNotification();

        return response()->json([
            'success' => true,
            'message' => 'Test verification email sent successfully!',
            'sent_to' => '<EMAIL>',
            'note' => 'Check your email inbox (and spam folder) for the verification email.'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send test email: ' . $e->getMessage(),
            'error_details' => $e->getTraceAsString()
        ]);
    }
});

// Test sending verification email to any email address
Route::get('/test-send-verification/{email}', function($email) {
    try {
        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email format: ' . $email
            ]);
        }

        // Create a test user (don't save to database)
        $testUser = new \App\Models\User([
            'name' => 'Test User',
            'email' => $email,
            'email_verified_at' => null,
        ]);
        $testUser->id = 999; // Fake ID for testing

        // Send verification email
        $testUser->sendEmailVerificationNotification();

        return response()->json([
            'success' => true,
            'message' => 'Test verification email sent successfully!',
            'sent_to' => $email,
            'note' => 'Check the email inbox (and spam folder) for the verification email.',
            'from_address' => config('mail.from.address'),
            'smtp_host' => config('mail.mailers.smtp.host')
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send test email: ' . $e->getMessage(),
            'error_details' => $e->getTraceAsString()
        ]);
    }
});

// Test basic email sending (without queue)
Route::get('/test-basic-email/{email}', function($email) {
    try {
        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email format: ' . $email
            ]);
        }

        // Send a simple test email
        \Illuminate\Support\Facades\Mail::raw('This is a test email from BelteiEcom. If you receive this, your email configuration is working!', function ($message) use ($email) {
            $message->to($email)
                    ->subject('🧪 Test Email from BelteiEcom')
                    ->from(config('mail.from.address'), config('mail.from.name'));
        });

        return response()->json([
            'success' => true,
            'message' => 'Basic test email sent successfully!',
            'sent_to' => $email,
            'note' => 'Check the email inbox (and spam folder) for a simple test message.',
            'from_address' => config('mail.from.address'),
            'smtp_settings' => [
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'encryption' => config('mail.mailers.smtp.encryption'),
                'username' => config('mail.mailers.smtp.username')
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send basic email: ' . $e->getMessage(),
            'error_details' => $e->getTraceAsString()
        ]);
    }
});

// Detailed email diagnostic test
Route::get('/test-email-detailed/{email}', function($email) {
    try {
        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email format: ' . $email
            ]);
        }

        $diagnostics = [];

        // Test 1: Check configuration
        $diagnostics['config'] = [
            'mail_mailer' => config('mail.default'),
            'mail_host' => config('mail.mailers.smtp.host'),
            'mail_port' => config('mail.mailers.smtp.port'),
            'mail_username' => config('mail.mailers.smtp.username'),
            'mail_password_set' => !empty(config('mail.mailers.smtp.password')),
            'mail_encryption' => config('mail.mailers.smtp.encryption'),
            'mail_from_address' => config('mail.from.address'),
            'mail_from_name' => config('mail.from.name'),
        ];

        // Test 2: Try to send email with detailed error catching
        try {
            \Illuminate\Support\Facades\Mail::raw('🧪 DETAILED TEST EMAIL from BelteiEcom\n\nThis is a diagnostic test email.\n\nIf you receive this, your email configuration is working correctly!\n\nTimestamp: ' . now()->format('Y-m-d H:i:s'), function ($message) use ($email) {
                $message->to($email)
                        ->subject('🧪 BelteiEcom Email Diagnostic Test - ' . now()->format('H:i:s'))
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            $diagnostics['email_send'] = [
                'success' => true,
                'message' => 'Email sent successfully via SMTP'
            ];

        } catch (\Swift_TransportException $e) {
            $diagnostics['email_send'] = [
                'success' => false,
                'error_type' => 'SMTP Transport Error',
                'message' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            $diagnostics['email_send'] = [
                'success' => false,
                'error_type' => 'General Error',
                'message' => $e->getMessage()
            ];
        }

        return response()->json([
            'test_email' => $email,
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'diagnostics' => $diagnostics,
            'recommendations' => [
                'Check spam folder in ' . $email,
                'Verify Gmail app password is correct',
                'Ensure 2FA is enabled on sender Gmail account',
                'Check if Gmail is blocking the emails'
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Diagnostic test failed: ' . $e->getMessage(),
            'error_details' => $e->getTraceAsString()
        ]);
    }
});

// Test SMTP connection directly
Route::get('/test-smtp-connection', function() {
    try {
        $config = [
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'username' => config('mail.mailers.smtp.username'),
            'password' => config('mail.mailers.smtp.password'),
            'encryption' => config('mail.mailers.smtp.encryption'),
        ];

        // Test SMTP connection
        $transport = new \Swift_SmtpTransport($config['host'], $config['port'], $config['encryption']);
        $transport->setUsername($config['username']);
        $transport->setPassword($config['password']);

        $mailer = new \Swift_Mailer($transport);

        // Try to start the transport
        $transport->start();

        return response()->json([
            'success' => true,
            'message' => 'SMTP connection successful!',
            'config' => [
                'host' => $config['host'],
                'port' => $config['port'],
                'username' => $config['username'],
                'password_length' => strlen($config['password']),
                'encryption' => $config['encryption'],
            ],
            'note' => 'SMTP server connection established successfully.'
        ]);

    } catch (\Swift_TransportException $e) {
        return response()->json([
            'success' => false,
            'error_type' => 'SMTP Connection Error',
            'message' => $e->getMessage(),
            'config' => [
                'host' => config('mail.mailers.smtp.host'),
                'port' => config('mail.mailers.smtp.port'),
                'username' => config('mail.mailers.smtp.username'),
                'password_set' => !empty(config('mail.mailers.smtp.password')),
                'encryption' => config('mail.mailers.smtp.encryption'),
            ],
            'suggestions' => [
                'Check if Gmail app password is correct',
                'Verify 2FA is enabled on Gmail account',
                'Try regenerating the app password',
                'Check if Gmail account is locked or restricted'
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error_type' => 'General Error',
            'message' => $e->getMessage()
        ]);
    }
});

// Process email queue manually
Route::get('/process-email-queue', function() {
    try {
        // Process any queued emails
        \Illuminate\Support\Facades\Artisan::call('queue:work', [
            '--once' => true,
            '--timeout' => 60
        ]);

        $output = \Illuminate\Support\Facades\Artisan::output();

        return response()->json([
            'success' => true,
            'message' => 'Email queue processed successfully!',
            'output' => $output,
            'note' => 'Any queued verification emails should now be sent.'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to process email queue: ' . $e->getMessage()
        ]);
    }
});

// Cart routes (authenticated and verified users only)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
    Route::post('/cart/add', [CartController::class, 'add'])->name('cart.add');
    Route::patch('/cart/{id}', [CartController::class, 'update'])->name('cart.update');
    Route::delete('/cart/{id}', [CartController::class, 'remove'])->name('cart.remove');
    Route::delete('/cart', [CartController::class, 'clear'])->name('cart.clear');

    // Order routes
    Route::get('/checkout', [OrderController::class, 'checkout'])->name('orders.checkout');
    Route::post('/orders', [OrderController::class, 'place'])->name('orders.place');
    Route::get('/orders/success/{token}', [OrderController::class, 'success'])->name('orders.success');
    Route::get('/orders', [OrderController::class, 'history'])->name('orders.history');
    Route::get('/orders/{id}', [OrderController::class, 'show'])->name('orders.show');
    Route::get('/orders/{id}/receipt-pdf', [OrderController::class, 'receiptPdf'])->name('orders.receipt.pdf');
});

// Public PDF receipt route (no authentication required)
Route::get('/receipt/{token}/download', [OrderController::class, 'downloadReceiptPdf'])->name('orders.receipt.download');

// Check categories structure
Route::get('/check-categories', function() {
    $categories = \App\Models\Category::with('parent', 'children')->get();
    $result = [];

    foreach($categories as $cat) {
        $result[] = [
            'id' => $cat->id,
            'name' => $cat->name,
            'parent' => $cat->parent ? $cat->parent->name : 'None',
            'parent_id' => $cat->parent_id,
            'children_count' => $cat->children->count(),
            'children' => $cat->children->pluck('name')->toArray()
        ];
    }

    return response()->json($result);
});

// Check specific category
Route::get('/check-category/{id}', function($id) {
    $category = \App\Models\Category::find($id);
    if (!$category) {
        return response()->json(['error' => 'Category not found']);
    }

    return response()->json([
        'id' => $category->id,
        'name' => $category->name,
        'description' => $category->description,
        'parent_id' => $category->parent_id,
        'parent_name' => $category->parent ? $category->parent->name : 'None',
        'created_at' => $category->created_at,
        'updated_at' => $category->updated_at
    ]);
});

// Update existing orders with success and PDF tokens
Route::get('/update-order-tokens', function() {
    $updatedSuccess = 0;
    $updatedPdf = 0;

    \App\Models\Order::whereNull('success_token')->each(function($order) use (&$updatedSuccess) {
        $order->update(['success_token' => \Illuminate\Support\Str::random(32)]);
        $updatedSuccess++;
    });

    \App\Models\Order::whereNull('pdf_token')->each(function($order) use (&$updatedPdf) {
        $order->update(['pdf_token' => \Illuminate\Support\Str::random(40)]);
        $updatedPdf++;
    });

    return response()->json([
        'success' => true,
        'message' => "Updated {$updatedSuccess} orders with success tokens and {$updatedPdf} orders with PDF tokens"
    ]);
});

// Simple admin routes
Route::get('/admin', [AdminController::class, 'index']);
Route::get('/admin/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');
Route::get('/admin-test-dashboard', function() {
    return view('admin.dashboard', [
        'totalSales' => 0,
        'totalOrders' => 0,
        'totalProducts' => 0,
        'totalCustomers' => 0,
        'recentOrders' => collect([]),
        'popularProducts' => collect([])
    ]);
});

// Admin routes for products
Route::get('/admin/products', [AdminProductsController::class, 'index'])->name('admin.products.index');
Route::get('/admin/products/create', [AdminProductsController::class, 'create'])->name('admin.products.create');
Route::post('/admin/products', [AdminProductsController::class, 'store'])->name('admin.products.store');
Route::get('/admin/products/{product}/edit', [AdminProductsController::class, 'edit'])->name('admin.products.edit');
Route::put('/admin/products/{product}', [AdminProductsController::class, 'update'])->name('admin.products.update');
Route::delete('/admin/products/{product}', [AdminProductsController::class, 'destroy'])->name('admin.products.destroy');

// Admin routes for categories
Route::get('/admin/categories', [AdminCategoriesController::class, 'index'])->name('admin.categories.index');
Route::get('/admin/categories/create', [AdminCategoriesController::class, 'create'])->name('admin.categories.create');
Route::post('/admin/categories', [AdminCategoriesController::class, 'store'])->name('admin.categories.store');
Route::get('/admin/categories/{id}/edit', [AdminCategoriesController::class, 'edit'])->name('admin.categories.edit');
Route::put('/admin/categories/{id}', [AdminCategoriesController::class, 'update'])->name('admin.categories.update');
Route::delete('/admin/categories/{id}', [AdminCategoriesController::class, 'destroy'])->name('admin.categories.destroy');

// Admin routes for orders
Route::get('/admin/orders', [AdminOrdersController::class, 'index'])->name('admin.orders.index');
Route::get('/admin/orders/{id}', [AdminOrdersController::class, 'show'])->name('admin.orders.show');
Route::get('/admin/orders/{id}/edit', [AdminOrdersController::class, 'edit'])->name('admin.orders.edit');
Route::put('/admin/orders/{id}', [AdminOrdersController::class, 'update'])->name('admin.orders.update');
Route::patch('/admin/orders/{id}/cancel', [AdminOrdersController::class, 'cancel'])->name('admin.orders.cancel');

// Admin routes for customers
Route::get('/admin/customers', [AdminCustomersController::class, 'index'])->name('admin.customers.index');
Route::get('/admin/customers/create', [AdminCustomersController::class, 'create'])->name('admin.customers.create');
Route::post('/admin/customers', [AdminCustomersController::class, 'store'])->name('admin.customers.store');
Route::get('/admin/customers/{id}', [AdminCustomersController::class, 'show'])->name('admin.customers.show');
Route::get('/admin/customers/{id}/edit', [AdminCustomersController::class, 'edit'])->name('admin.customers.edit');
Route::put('/admin/customers/{id}', [AdminCustomersController::class, 'update'])->name('admin.customers.update');
Route::delete('/admin/customers/{id}', [AdminCustomersController::class, 'destroy'])->name('admin.customers.destroy');
Route::post('/admin/customers/{id}/verify-email', [AdminCustomersController::class, 'verifyEmail'])->name('admin.customers.verify-email');

// Admin routes for admin users
Route::get('/admin/admins', [AdminUsersController::class, 'index'])->name('admin.admins.index');
Route::get('/admin/admins/create', [AdminUsersController::class, 'create'])->name('admin.admins.create');
Route::post('/admin/admins', [AdminUsersController::class, 'store'])->name('admin.admins.store');
Route::get('/admin/admins/{id}', [AdminUsersController::class, 'show'])->name('admin.admins.show');
Route::get('/admin/admins/{id}/edit', [AdminUsersController::class, 'edit'])->name('admin.admins.edit');
Route::put('/admin/admins/{id}', [AdminUsersController::class, 'update'])->name('admin.admins.update');
Route::delete('/admin/admins/{id}', [AdminUsersController::class, 'destroy'])->name('admin.admins.destroy');
Route::post('/admin/admins/{id}/verify-email', [AdminUsersController::class, 'verifyEmail'])->name('admin.admins.verify-email');

// Admin settings routes
Route::get('/admin/settings', [App\Http\Controllers\AdminSettingsController::class, 'index'])->name('admin.settings.index');
Route::get('/admin/settings/profile', [App\Http\Controllers\AdminSettingsController::class, 'profile'])->name('admin.settings.profile');
Route::put('/admin/settings/profile', [App\Http\Controllers\AdminSettingsController::class, 'updateProfile'])->name('admin.settings.profile.update');
Route::delete('/admin/settings/profile/picture', [App\Http\Controllers\AdminSettingsController::class, 'removeProfilePicture'])->name('admin.settings.profile.remove-picture');
Route::get('/admin/settings/password', [App\Http\Controllers\AdminSettingsController::class, 'password'])->name('admin.settings.password');
Route::put('/admin/settings/password', [App\Http\Controllers\AdminSettingsController::class, 'updatePassword'])->name('admin.settings.password.update');

// Admin newsletter routes
Route::get('/admin/newsletter', [App\Http\Controllers\Admin\NewsletterController::class, 'index'])->name('admin.newsletter.index');
Route::get('/admin/newsletter/create', [App\Http\Controllers\Admin\NewsletterController::class, 'create'])->name('admin.newsletter.create');
Route::post('/admin/newsletter/send', [App\Http\Controllers\Admin\NewsletterController::class, 'send'])->name('admin.newsletter.send');
Route::get('/admin/newsletter/export', [App\Http\Controllers\Admin\NewsletterController::class, 'export'])->name('admin.newsletter.export');

// Test routes for debugging
Route::get('/test-customer-create', function() {
    return 'Customer create route is working! <a href="' . route('admin.customers.create') . '">Go to actual create page</a>';
});

Route::get('/test-admin-create', function() {
    return 'Admin create route is working! <a href="' . route('admin.admins.create') . '">Go to actual create page</a>';
});

Route::get('/test-images', function() {
    $products = \App\Models\Product::whereNotNull('image')->take(5)->get();
    $html = '<h1>Image Test - Updated</h1>';
    $html .= '<p><strong>Storage link status:</strong> ' . (file_exists(public_path('storage')) ? 'EXISTS' : 'MISSING') . '</p>';

    foreach($products as $product) {
        $imageUrl = asset('storage/' . $product->image);
        $filePath = public_path('storage/' . $product->image);
        $fileExists = file_exists($filePath);

        $html .= '<div style="margin: 20px; padding: 20px; border: 1px solid #ccc; background: ' . ($fileExists ? '#d4edda' : '#f8d7da') . ';">';
        $html .= '<h3>' . $product->name . '</h3>';
        $html .= '<p><strong>Image path in DB:</strong> ' . $product->image . '</p>';
        $html .= '<p><strong>Full URL:</strong> ' . $imageUrl . '</p>';
        $html .= '<p><strong>File path:</strong> ' . $filePath . '</p>';
        $html .= '<p><strong>File exists:</strong> ' . ($fileExists ? 'YES ✅' : 'NO ❌') . '</p>';

        if ($fileExists) {
            $html .= '<img src="' . $imageUrl . '" alt="' . $product->name . '" style="max-width: 200px; max-height: 200px; border: 2px solid green;" onload="console.log(\'Image loaded successfully: ' . $imageUrl . '\');" onerror="console.log(\'Image failed to load: ' . $imageUrl . '\'); this.style.border=\'2px solid red\';">';
        } else {
            $html .= '<p style="color: red;">❌ File not found on disk</p>';
        }
        $html .= '</div>';
    }
    return $html;
});

// Telegram notification route (commented out due to missing controller)
// Route::post('/telegram/send-order-notification', [App\Http\Controllers\TelegramController::class, 'sendOrderNotification'])->name('telegram.send-order-notification');

// Bakong payment routes
Route::get('/bakong/request/{orderId}', [App\Http\Controllers\BakongController::class, 'request'])->name('bakong.request');
Route::get('/bakong/save', [App\Http\Controllers\BakongController::class, 'save'])->name('bakong.save');
Route::get('/bakong/qrcode', [App\Http\Controllers\BakongController::class, 'qrcode'])->name('bakong.qrcode');
Route::post('/bakong/verify', [App\Http\Controllers\BakongController::class, 'verify'])->name('bakong.verify');
Route::post('/bakong/check-transaction', [App\Http\Controllers\BakongController::class, 'checkTransaction'])->name('bakong.check-transaction');
Route::post('/bakong/check-transaction-status', [App\Http\Controllers\BakongController::class, 'checkTransactionStatus'])->name('bakong.check-transaction-status');
Route::post('/bakong/api/check-transaction', [App\Http\Controllers\BakongController::class, 'checkTransactionWithBakongApi'])->name('bakong.api.check-transaction');
Route::post('/bakong/check-transaction-by-order', [App\Http\Controllers\BakongController::class, 'checkTransactionByOrder'])->name('bakong.check.transaction.by.order');
Route::get('/bakong/success/{id}', [App\Http\Controllers\BakongController::class, 'success'])->name('bakong.success');
Route::get('/bakong/api-docs', function() { return view('bakong.api-docs'); })->name('bakong.api-docs');

// Test routes to check database records
Route::get('/bakong/test-payments', function() {
    $payments = \App\Models\BakongPayment::all();
    return response()->json([
        'count' => $payments->count(),
        'payments' => $payments
    ]);
});

Route::get('/bakong/test-orders', function() {
    $orders = \App\Models\Order::all();
    return response()->json([
        'count' => $orders->count(),
        'orders' => $orders
    ]);
});

Route::get('/bakong/test-order/{id}', function($id) {
    $order = \App\Models\Order::find($id);
    $payment = \App\Models\BakongPayment::where('order_id', $id)->first();
    return response()->json([
        'order' => $order,
        'payment' => $payment
    ]);
});

// Test specific MD5 hash
Route::get('/test-md5/{md5}', function($md5) {
    $payment = \App\Models\BakongPayment::where('transaction_id', $md5)->first();
    return response()->json([
        'md5' => $md5,
        'payment_found' => $payment ? true : false,
        'payment' => $payment,
        'all_payments' => \App\Models\BakongPayment::all(['id', 'transaction_id', 'status', 'amount'])
    ]);
});

// Create test payment record for specific MD5
Route::get('/create-test-payment/{md5}', function($md5) {
    try {
        // Check if payment already exists
        $existingPayment = \App\Models\BakongPayment::where('transaction_id', $md5)->first();
        if ($existingPayment) {
            return response()->json([
                'success' => true,
                'message' => 'Payment already exists',
                'payment' => $existingPayment
            ]);
        }

        // Create a test payment record
        $payment = \App\Models\BakongPayment::create([
            'user_id' => 1, // Admin user
            'user_name' => 'Admin User',
            'user_email' => '<EMAIL>',
            'amount' => 0.01,
            'phone_number' => '123456789',
            'shipping_address' => 'Test Address, Phnom Penh',
            'payment_method' => 'khqr',
            'status' => 'pending',
            'transaction_id' => $md5,
            'qr_code' => '00020101021230470022chhunlichhean_kun@wing010712636290206Bakong52045999530384054040.015802KH5910BelteiEcom6010Phnom+Penh62210117ORDER1748419047_19917001317484190494196304A538',
        ]);

        // Store test order data in session
        session([
            'pending_order_data' => [
                'user_id' => 1,
                'total_amount' => 0.01,
                'shipping_address' => 'Test Address, Phnom Penh',
                'phone_number' => '123456789',
                'payment_method' => 'khqr',
                'cart_items' => [
                    [
                        'product_id' => 1,
                        'quantity' => 1,
                        'price' => 0.01,
                        'product_name' => 'Test Product',
                    ]
                ]
            ]
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test payment record created successfully',
            'payment' => $payment,
            'session_data' => session('pending_order_data')
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error creating test payment: ' . $e->getMessage()
        ]);
    }
});

// Manually complete payment for testing
Route::get('/complete-payment/{md5}', function($md5) {
    try {
        $payment = \App\Models\BakongPayment::where('transaction_id', $md5)->first();
        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'Payment not found'
            ]);
        }

        if ($payment->status === 'completed') {
            return response()->json([
                'success' => true,
                'message' => 'Payment already completed',
                'payment' => $payment
            ]);
        }

        // Update payment status
        $payment->update([
            'status' => 'completed',
            'payment_date' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment marked as completed manually',
            'payment' => $payment,
            'note' => 'Now the QR code page should detect this and redirect to success page'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error completing payment: ' . $e->getMessage()
        ]);
    }
});

// Test direct Bakong API call like the original qrcode.php
Route::get('/test-bakong-direct/{md5}', function($md5) {
    try {
        $response = \Illuminate\Support\Facades\Http::withHeaders([
            'Authorization' => 'Bearer ' . env('BAKONG_API_TOKEN'),
            'Content-Type' => 'application/json',
        ])->post('https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5', [
            'md5' => $md5,
        ]);

        $responseData = $response->json();

        return response()->json([
            'md5_tested' => $md5,
            'api_url' => 'https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5',
            'token_used' => substr(env('BAKONG_API_TOKEN'), 0, 20) . '...',
            'http_status' => $response->status(),
            'response_successful' => $response->successful(),
            'bakong_response' => $responseData,
            'is_successful' => isset($responseData['responseCode']) && $responseData['responseCode'] === 0,
            'to_account_id' => $responseData['data']['toAccountId'] ?? 'N/A',
            'expected_account' => 'chhunlichhean_kun@wing',
            'account_matches' => ($responseData['data']['toAccountId'] ?? '') === 'chhunlichhean_kun@wing'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => true,
            'message' => $e->getMessage(),
            'md5_tested' => $md5
        ]);
    }
});

// Test Telegram notification for specific order
Route::get('/test-telegram-order/{orderId}', function($orderId) {
    try {
        $order = \App\Models\Order::with(['user', 'orderItems.product'])->findOrFail($orderId);

        $botToken = env('TELEGRAM_BOT_TOKEN');
        $chatId = env('TELEGRAM_CHAT_ID');

        if (!$botToken || !$chatId) {
            return response()->json([
                'success' => false,
                'message' => 'Telegram bot token or chat ID not configured'
            ]);
        }

        // Format payment method display
        $paymentMethodDisplay = $order->payment_method === 'cash_on_delivery' ? 'Cash on Delivery' : 'Bakong KHQR';

        // Prepare message
        $message = "🛒 *New Order #{$order->id}*\n\n";
        $message .= "👤 *Customer:* {$order->user->name}\n";
        $message .= "📧 *Email:* {$order->user->email}\n";
        $message .= "📱 *Phone:* {$order->phone_number}\n";
        $message .= "📍 *Address:* {$order->shipping_address}\n";
        $message .= "💳 *Payment:* {$paymentMethodDisplay}\n";
        $message .= "📅 *Date:* " . $order->created_at->setTimezone('Asia/Phnom_Penh')->format('Y-m-d H:i:s') . " (Cambodia Time)\n\n";

        $message .= "🛍️ *Products:*\n";
        $totalItems = 0;
        foreach ($order->orderItems as $item) {
            $message .= "• {$item->product->name}\n";
            $message .= "  Qty: {$item->quantity} × \${$item->price} = \$" . number_format($item->quantity * $item->price, 2) . "\n";
            $totalItems += $item->quantity;
        }

        $message .= "\n💰 *Total Amount:* \$" . number_format($order->total_amount, 2) . "\n";
        $message .= "📦 *Total Items:* {$totalItems}\n";
        $message .= "🏷️ *Status:* " . ucfirst($order->status) . "\n\n";

        if ($order->payment_method === 'cash_on_delivery') {
            $message .= "💵 *Cash on Delivery* - Payment will be collected upon delivery\n";
        }

        $message .= "🔗 *Order Details:* " . route('admin.orders.show', $order->id);

        // Send the message
        $response = \Illuminate\Support\Facades\Http::post("https://api.telegram.org/bot{$botToken}/sendMessage", [
            'chat_id' => $chatId,
            'text' => $message,
            'parse_mode' => 'Markdown'
        ]);

        if ($response->successful()) {
            return response()->json([
                'success' => true,
                'message' => 'Telegram notification sent successfully for order: ' . $order->id,
                'telegram_response' => $response->json()
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send Telegram notification',
                'error' => $response->body(),
                'status' => $response->status()
            ]);
        }

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error testing Telegram notification: ' . $e->getMessage()
        ]);
    }
});

// Test Telegram bot
Route::get('/test-telegram', function() {
    try {
        $botToken = env('TELEGRAM_BOT_TOKEN');
        $chatId = env('TELEGRAM_CHAT_ID');

        if (!$botToken || !$chatId) {
            return response()->json([
                'success' => false,
                'message' => 'Telegram bot token or chat ID not configured',
                'bot_token' => $botToken ? 'Set' : 'Not set',
                'chat_id' => $chatId ? 'Set' : 'Not set'
            ]);
        }

        $message = "🧪 *Test Message from Beltei Ecom*\n\n";
        $message .= "✅ Telegram bot is working correctly!\n";
        $message .= "📅 Test sent at: " . now()->format('Y-m-d H:i:s') . "\n";
        $message .= "🤖 Bot Token: " . substr($botToken, 0, 10) . "...\n";
        $message .= "💬 Chat ID: " . $chatId;

        $response = \Illuminate\Support\Facades\Http::post("https://api.telegram.org/bot{$botToken}/sendMessage", [
            'chat_id' => $chatId,
            'text' => $message,
            'parse_mode' => 'Markdown'
        ]);

        if ($response->successful()) {
            return response()->json([
                'success' => true,
                'message' => 'Test message sent successfully to Telegram!',
                'telegram_response' => $response->json()
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message to Telegram',
                'error' => $response->body(),
                'status' => $response->status()
            ]);
        }
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error testing Telegram bot: ' . $e->getMessage()
        ]);
    }
});

Route::get('/test-upload-debug', function() {
    $html = '<h1>Upload Debug Information</h1>';

    // Check PHP upload settings
    $html .= '<h2>PHP Upload Settings</h2>';
    $html .= '<p><strong>upload_max_filesize:</strong> ' . ini_get('upload_max_filesize') . '</p>';
    $html .= '<p><strong>post_max_size:</strong> ' . ini_get('post_max_size') . '</p>';
    $html .= '<p><strong>max_file_uploads:</strong> ' . ini_get('max_file_uploads') . '</p>';
    $html .= '<p><strong>file_uploads:</strong> ' . (ini_get('file_uploads') ? 'ON' : 'OFF') . '</p>';

    // Check storage directories
    $html .= '<h2>Storage Directory Status</h2>';
    $storagePublic = storage_path('app/public');
    $publicStorage = public_path('storage');
    $productsDir = storage_path('app/public/products');
    $profilesDir = storage_path('app/public/profile-pictures');

    $html .= '<p><strong>storage/app/public exists:</strong> ' . (is_dir($storagePublic) ? 'YES' : 'NO') . '</p>';
    $html .= '<p><strong>storage/app/public writable:</strong> ' . (is_writable($storagePublic) ? 'YES' : 'NO') . '</p>';
    $html .= '<p><strong>public/storage exists:</strong> ' . (is_dir($publicStorage) ? 'YES' : 'NO') . '</p>';
    $html .= '<p><strong>products directory exists:</strong> ' . (is_dir($productsDir) ? 'YES' : 'NO') . '</p>';
    $html .= '<p><strong>profile-pictures directory exists:</strong> ' . (is_dir($profilesDir) ? 'YES' : 'NO') . '</p>';

    // Check recent products
    $html .= '<h2>Recent Products</h2>';
    $recentProducts = \App\Models\Product::orderBy('created_at', 'desc')->take(3)->get();
    foreach($recentProducts as $product) {
        $html .= '<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">';
        $html .= '<p><strong>Name:</strong> ' . $product->name . '</p>';
        $html .= '<p><strong>Image:</strong> ' . ($product->image ?: 'NULL') . '</p>';
        $html .= '<p><strong>Created:</strong> ' . $product->created_at . '</p>';
        $html .= '</div>';
    }

    return $html;
});

Route::get('/debug-products', function() {
    $products = \App\Models\Product::whereNotNull('image')->take(10)->get(['id', 'name', 'image']);
    $html = '<h1>Product Image Debug</h1>';

    foreach($products as $product) {
        $imagePath = public_path('storage/' . $product->image);
        $imageUrl = asset('storage/' . $product->image);
        $fileExists = file_exists($imagePath);

        $html .= '<div style="margin: 20px 0; padding: 15px; border: 2px solid ' . ($fileExists ? 'green' : 'red') . '; background: ' . ($fileExists ? '#d4edda' : '#f8d7da') . ';">';
        $html .= '<h3>Product ID: ' . $product->id . ' - ' . $product->name . '</h3>';
        $html .= '<p><strong>Image field in DB:</strong> ' . $product->image . '</p>';
        $html .= '<p><strong>Full file path:</strong> ' . $imagePath . '</p>';
        $html .= '<p><strong>Image URL:</strong> ' . $imageUrl . '</p>';
        $html .= '<p><strong>File exists:</strong> ' . ($fileExists ? 'YES ✅' : 'NO ❌') . '</p>';

        if ($fileExists) {
            $html .= '<img src="' . $imageUrl . '" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; margin: 10px 0;">';
        } else {
            $html .= '<p style="color: red; font-weight: bold;">❌ File not found on disk!</p>';
        }
        $html .= '</div>';
    }

    return $html;
});

Route::get('/test-upload-form', function() {
    return '
    <h1>Test File Upload</h1>
    <form action="/test-upload-submit" method="POST" enctype="multipart/form-data">
        ' . csrf_field() . '
        <p>
            <label>Select Image:</label><br>
            <input type="file" name="test_image" accept="image/*" required>
        </p>
        <p>
            <label>Product Name:</label><br>
            <input type="text" name="test_name" value="Test Product" required>
        </p>
        <p>
            <button type="submit">Test Upload</button>
        </p>
    </form>
    ';
});

Route::post('/test-upload-submit', function(\Illuminate\Http\Request $request) {
    $html = '<h1>Upload Test Results</h1>';

    $html .= '<h2>Request Data</h2>';
    $html .= '<p><strong>Name:</strong> ' . $request->input('test_name') . '</p>';
    $html .= '<p><strong>Has File:</strong> ' . ($request->hasFile('test_image') ? 'YES' : 'NO') . '</p>';

    if ($request->hasFile('test_image')) {
        $file = $request->file('test_image');
        $html .= '<h2>File Information</h2>';
        $html .= '<p><strong>Original Name:</strong> ' . $file->getClientOriginalName() . '</p>';
        $html .= '<p><strong>Size:</strong> ' . $file->getSize() . ' bytes</p>';
        $html .= '<p><strong>MIME Type:</strong> ' . $file->getMimeType() . '</p>';
        $html .= '<p><strong>Extension:</strong> ' . $file->getClientOriginalExtension() . '</p>';
        $html .= '<p><strong>Is Valid:</strong> ' . ($file->isValid() ? 'YES' : 'NO') . '</p>';

        try {
            $path = $file->store('test-uploads', 'public');
            $html .= '<h2>Upload Success</h2>';
            $html .= '<p><strong>Stored Path:</strong> ' . $path . '</p>';
            $html .= '<p><strong>Full URL:</strong> ' . asset('storage/' . $path) . '</p>';
            $html .= '<img src="' . asset('storage/' . $path) . '" style="max-width: 300px; border: 1px solid #ddd;">';
        } catch (\Exception $e) {
            $html .= '<h2>Upload Error</h2>';
            $html .= '<p style="color: red;"><strong>Error:</strong> ' . $e->getMessage() . '</p>';
        }
    }

    return $html;
});

Route::get('/admin-products-debug', function() {
    $products = \App\Models\Product::with('category')->take(10)->get();
    $html = '<h1>Admin Products Debug</h1>';
    $html .= '<style>
        .debug-product { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .debug-image { width: 100px; height: 100px; border: 2px solid #007bff; margin: 10px 0; }
        .debug-info { font-family: monospace; background: #f8f9fa; padding: 10px; margin: 5px 0; }
    </style>';

    foreach($products as $product) {
        $imageUrl = asset('storage/' . $product->image);
        $imagePath = public_path('storage/' . $product->image);
        $fileExists = file_exists($imagePath);

        $html .= '<div class="debug-product">';
        $html .= '<h3>Product #' . $product->id . ': ' . $product->name . '</h3>';
        $html .= '<div class="debug-info"><strong>Image field:</strong> ' . ($product->image ?: 'NULL') . '</div>';
        $html .= '<div class="debug-info"><strong>Image URL:</strong> ' . $imageUrl . '</div>';
        $html .= '<div class="debug-info"><strong>File path:</strong> ' . $imagePath . '</div>';
        $html .= '<div class="debug-info"><strong>File exists:</strong> ' . ($fileExists ? 'YES ✅' : 'NO ❌') . '</div>';

        if ($product->image) {
            $html .= '<div><strong>Image preview:</strong></div>';
            $html .= '<img src="' . $imageUrl . '" class="debug-image" onerror="this.style.border=\'2px solid red\'; this.alt=\'FAILED TO LOAD\';">';
        } else {
            $html .= '<div style="color: #666;">No image uploaded</div>';
        }
        $html .= '</div>';
    }

    return $html;
});

Route::get('/simple-admin-products', function() {
    $products = \App\Models\Product::with('category')->take(10)->get();
    $html = '<!DOCTYPE html><html><head><title>Simple Admin Products</title></head><body>';
    $html .= '<h1>Simple Admin Products Test</h1>';
    $html .= '<p>This page shows products without any complex CSS or JavaScript</p>';

    foreach($products as $product) {
        $html .= '<div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">';
        $html .= '<h3>Product #' . $product->id . ': ' . $product->name . '</h3>';

        if ($product->image) {
            $imageUrl = asset('storage/' . $product->image);
            $html .= '<p><strong>Image URL:</strong> ' . $imageUrl . '</p>';
            $html .= '<img src="' . $imageUrl . '" style="width: 100px; height: 100px; border: 2px solid blue; object-fit: cover;">';
        } else {
            $html .= '<p style="color: red;">No image</p>';
        }

        $html .= '<p><strong>Category:</strong> ' . $product->category->name . '</p>';
        $html .= '<p><strong>Price:</strong> $' . $product->price . '</p>';
        $html .= '</div>';
    }

    $html .= '</body></html>';
    return $html;
});

Route::get('/check-latest-products', function() {
    $products = \App\Models\Product::orderBy('updated_at', 'desc')->take(5)->get();
    $html = '<h1>Latest Products Check</h1>';
    $html .= '<p>Checking the 5 most recently updated products:</p>';

    foreach($products as $product) {
        $html .= '<div style="border: 2px solid #007bff; margin: 15px; padding: 15px; background: #f8f9fa;">';
        $html .= '<h3>Product #' . $product->id . ': ' . $product->name . '</h3>';
        $html .= '<p><strong>Updated:</strong> ' . $product->updated_at . '</p>';
        $html .= '<p><strong>Image field:</strong> ' . ($product->image ?: 'NULL/EMPTY') . '</p>';

        if ($product->image) {
            $imageUrl = asset('storage/' . $product->image);
            $imagePath = public_path('storage/' . $product->image);
            $fileExists = file_exists($imagePath);

            $html .= '<p><strong>Image URL:</strong> <a href="' . $imageUrl . '" target="_blank">' . $imageUrl . '</a></p>';
            $html .= '<p><strong>File exists:</strong> ' . ($fileExists ? 'YES ✅' : 'NO ❌') . '</p>';
            $html .= '<img src="' . $imageUrl . '" style="width: 150px; height: 150px; border: 2px solid ' . ($fileExists ? 'green' : 'red') . '; object-fit: cover;">';
        } else {
            $html .= '<p style="color: red; font-weight: bold;">❌ NO IMAGE IN DATABASE</p>';
        }
        $html .= '</div>';
    }

    return $html;
});

Route::get('/fix-product-52', function() {
    $product = \App\Models\Product::find(52);
    if ($product) {
        // Update to match the actual file that exists
        $product->image = 'products/skibidi4241-1749124011.jpg';
        $product->save();
        return 'Fixed! Product #52 image path updated to: ' . $product->image;
    }
    return 'Product #52 not found';
});

Route::get('/test-images-only', function() {
    $products = \App\Models\Product::whereNotNull('image')->take(10)->get();

    $html = '<!DOCTYPE html>
    <html>
    <head>
        <title>Image Test - No CSS Conflicts</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: white; }
            .product { margin: 20px 0; padding: 20px; border: 2px solid #333; background: #f9f9f9; }
            .product img { border: 5px solid red; background: yellow; }
        </style>
    </head>
    <body>
        <h1>🖼️ Pure Image Test (No Admin Layout)</h1>
        <p>This page tests images without any admin layout interference.</p>';

    foreach($products as $product) {
        $imageUrl = asset('storage/' . $product->image);
        $html .= '<div class="product">';
        $html .= '<h3>Product: ' . $product->name . '</h3>';
        $html .= '<p><strong>Image URL:</strong> <a href="' . $imageUrl . '" target="_blank">' . $imageUrl . '</a></p>';
        $html .= '<img src="' . $imageUrl . '?test=' . time() . '" width="200" height="200" style="object-fit: cover; border: 5px solid red; background: yellow;">';
        $html .= '</div>';
    }

    $html .= '</body></html>';
    return $html;
});

// Review routes
Route::middleware('auth')->group(function () {
    Route::post('/products/{product}/reviews', [App\Http\Controllers\ReviewController::class, 'store'])->name('reviews.store');
    Route::delete('/reviews/{review}', [App\Http\Controllers\ReviewController::class, 'destroy'])->name('reviews.destroy');
});

// Wishlist routes
Route::middleware('auth')->group(function () {
    Route::get('/wishlist', [App\Http\Controllers\WishlistController::class, 'index'])->name('wishlist.index');
    Route::post('/wishlist/add', [App\Http\Controllers\WishlistController::class, 'store'])->name('wishlist.store');
    Route::delete('/wishlist/remove', [App\Http\Controllers\WishlistController::class, 'destroy'])->name('wishlist.destroy');
    Route::post('/wishlist/toggle', [App\Http\Controllers\WishlistController::class, 'toggle'])->name('wishlist.toggle');
    Route::get('/wishlist/count', [App\Http\Controllers\WishlistController::class, 'count'])->name('wishlist.count');
    Route::delete('/wishlist/clear', [App\Http\Controllers\WishlistController::class, 'clear'])->name('wishlist.clear');
});

// AI Recommendations routes
Route::prefix('api/recommendations')->group(function () {
    Route::get('/product/{productId}', [App\Http\Controllers\RecommendationController::class, 'getProductRecommendations'])->name('recommendations.product');
    Route::get('/personalized', [App\Http\Controllers\RecommendationController::class, 'getPersonalizedRecommendations'])->name('recommendations.personalized');
    Route::get('/trending', [App\Http\Controllers\RecommendationController::class, 'getTrendingProducts'])->name('recommendations.trending');
    Route::post('/record-view', [App\Http\Controllers\RecommendationController::class, 'recordView'])->name('recommendations.record-view');

    // Admin routes
    Route::middleware(['auth', 'admin'])->group(function () {
        Route::post('/generate/{productId}', [App\Http\Controllers\RecommendationController::class, 'generateRecommendations'])->name('recommendations.generate');
    });
});

// Admin review management routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/reviews', [App\Http\Controllers\Admin\ReviewManagementController::class, 'index'])->name('reviews.index');
    Route::patch('/reviews/{review}/approve', [App\Http\Controllers\Admin\ReviewManagementController::class, 'approve'])->name('reviews.approve');
    Route::patch('/reviews/{review}/reject', [App\Http\Controllers\Admin\ReviewManagementController::class, 'reject'])->name('reviews.reject');
    Route::patch('/reviews/{review}/toggle-featured', [App\Http\Controllers\Admin\ReviewManagementController::class, 'toggleFeatured'])->name('reviews.toggle-featured');
    Route::delete('/reviews/{review}', [App\Http\Controllers\Admin\ReviewManagementController::class, 'destroy'])->name('reviews.destroy');

    // User management routes
    Route::patch('/users/{user}/ban', [App\Http\Controllers\Admin\ReviewManagementController::class, 'banUser'])->name('users.ban');
    Route::patch('/users/{user}/unban', [App\Http\Controllers\Admin\ReviewManagementController::class, 'unbanUser'])->name('users.unban');
    Route::patch('/users/{user}/badge', [App\Http\Controllers\Admin\ReviewManagementController::class, 'updateUserBadge'])->name('users.badge');

    // Advanced Inventory Management routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\InventoryController::class, 'index'])->name('index');
        Route::get('/warehouse/{warehouse}', [App\Http\Controllers\Admin\InventoryController::class, 'warehouse'])->name('warehouse');
        Route::get('/product/{product}', [App\Http\Controllers\Admin\InventoryController::class, 'product'])->name('product');
        Route::get('/product/{product}/add-stock', [App\Http\Controllers\Admin\InventoryController::class, 'addStockForm'])->name('add-stock-form');
        Route::post('/product/{product}/add-stock', [App\Http\Controllers\Admin\InventoryController::class, 'addStock'])->name('add-stock');
        Route::get('/product/{product}/transfer', [App\Http\Controllers\Admin\InventoryController::class, 'transferForm'])->name('transfer-form');
        Route::post('/product/{product}/transfer', [App\Http\Controllers\Admin\InventoryController::class, 'transfer'])->name('transfer');
        Route::patch('/product/{product}/warehouse/{warehouse}/adjust', [App\Http\Controllers\Admin\InventoryController::class, 'adjust'])->name('adjust');
        Route::get('/movements', [App\Http\Controllers\Admin\InventoryController::class, 'movements'])->name('movements');
        Route::get('/export', [App\Http\Controllers\Admin\InventoryController::class, 'export'])->name('export');
    });

    // Live Chat Management routes
    Route::prefix('chat')->name('chat.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ChatManagementController::class, 'index'])->name('index');
        Route::get('/agent', [App\Http\Controllers\Admin\ChatManagementController::class, 'agent'])->name('agent');
        Route::get('/conversations', [App\Http\Controllers\Admin\ChatManagementController::class, 'conversations'])->name('conversations');
        Route::get('/conversation/{id}', [App\Http\Controllers\Admin\ChatManagementController::class, 'conversation'])->name('conversation');
        Route::post('/conversation/{id}/message', [App\Http\Controllers\Admin\ChatManagementController::class, 'sendMessage'])->name('send-message');
        Route::post('/conversation/{id}/assign', [App\Http\Controllers\Admin\ChatManagementController::class, 'assign'])->name('assign');
        Route::post('/conversation/{id}/take', [App\Http\Controllers\Admin\ChatManagementController::class, 'take'])->name('take');
        Route::post('/conversation/{id}/transfer', [App\Http\Controllers\Admin\ChatManagementController::class, 'transfer'])->name('transfer');
        Route::post('/conversation/{id}/close', [App\Http\Controllers\Admin\ChatManagementController::class, 'close'])->name('close');
        Route::post('/status', [App\Http\Controllers\Admin\ChatManagementController::class, 'updateStatus'])->name('update-status');
        Route::get('/messages/new', [App\Http\Controllers\Admin\ChatManagementController::class, 'getNewMessages'])->name('new-messages');
        Route::get('/waiting/count', [App\Http\Controllers\Admin\ChatManagementController::class, 'getWaitingCount'])->name('waiting-count');
    });
});

// Customer Chat routes
Route::prefix('chat')->name('chat.')->group(function () {
    Route::get('/', [App\Http\Controllers\ChatController::class, 'index'])->name('index');
    Route::post('/start', [App\Http\Controllers\ChatController::class, 'start'])->name('start');
    Route::get('/conversation/{id}', [App\Http\Controllers\ChatController::class, 'conversation'])->name('conversation');
    Route::post('/conversation/{id}/message', [App\Http\Controllers\ChatController::class, 'sendMessage'])->name('send-message');
    Route::get('/conversation/{id}/messages', [App\Http\Controllers\ChatController::class, 'getMessages'])->name('get-messages');
    Route::post('/conversation/{id}/close', [App\Http\Controllers\ChatController::class, 'close'])->name('close');
    Route::get('/widget', [App\Http\Controllers\ChatController::class, 'widget'])->name('widget');
});


