<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Warehouse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'latitude',
        'longitude',
        'phone',
        'email',
        'manager_name',
        'is_active',
        'is_default',
        'capacity',
        'operating_hours',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'operating_hours' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Get the inventory items for this warehouse.
     */
    public function inventory()
    {
        return $this->hasMany(WarehouseInventory::class);
    }

    /**
     * Get the inventory movements for this warehouse.
     */
    public function movements()
    {
        return $this->hasMany(InventoryMovement::class);
    }

    /**
     * Get the products available in this warehouse.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'warehouse_inventory')
                    ->withPivot(['quantity', 'reserved_quantity', 'available_quantity', 'reorder_level', 'location_code'])
                    ->withTimestamps();
    }

    /**
     * Get the default warehouse.
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Get active warehouses.
     */
    public static function getActive()
    {
        return static::where('is_active', true)->get();
    }

    /**
     * Get total inventory value for this warehouse.
     */
    public function getTotalInventoryValue()
    {
        return $this->inventory()
                    ->join('products', 'warehouse_inventory.product_id', '=', 'products.id')
                    ->selectRaw('SUM(warehouse_inventory.quantity * products.price) as total_value')
                    ->value('total_value') ?: 0;
    }

    /**
     * Get low stock items for this warehouse.
     */
    public function getLowStockItems()
    {
        return $this->inventory()
                    ->whereRaw('quantity <= reorder_level')
                    ->with('product')
                    ->get();
    }

    /**
     * Get total products count in this warehouse.
     */
    public function getTotalProductsCount()
    {
        return $this->inventory()->sum('quantity');
    }

    /**
     * Get unique products count in this warehouse.
     */
    public function getUniqueProductsCount()
    {
        return $this->inventory()->where('quantity', '>', 0)->count();
    }

    /**
     * Check if warehouse has sufficient stock for a product.
     */
    public function hasStock($productId, $quantity = 1)
    {
        $inventory = $this->inventory()->where('product_id', $productId)->first();
        return $inventory && $inventory->available_quantity >= $quantity;
    }

    /**
     * Get available quantity for a product.
     */
    public function getAvailableQuantity($productId)
    {
        $inventory = $this->inventory()->where('product_id', $productId)->first();
        return $inventory ? $inventory->available_quantity : 0;
    }

    /**
     * Calculate distance to a given location.
     */
    public function distanceTo($latitude, $longitude)
    {
        if (!$this->latitude || !$this->longitude) {
            return null;
        }

        $earthRadius = 6371; // Earth's radius in kilometers

        $latDiff = deg2rad($latitude - $this->latitude);
        $lonDiff = deg2rad($longitude - $this->longitude);

        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($latitude)) *
             sin($lonDiff / 2) * sin($lonDiff / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Find nearest warehouse to a location.
     */
    public static function findNearest($latitude, $longitude, $limit = 1)
    {
        $warehouses = static::where('is_active', true)
                           ->whereNotNull('latitude')
                           ->whereNotNull('longitude')
                           ->get();

        $warehousesWithDistance = $warehouses->map(function ($warehouse) use ($latitude, $longitude) {
            $warehouse->distance = $warehouse->distanceTo($latitude, $longitude);
            return $warehouse;
        })->filter(function ($warehouse) {
            return $warehouse->distance !== null;
        })->sortBy('distance');

        return $limit === 1 ? $warehousesWithDistance->first() : $warehousesWithDistance->take($limit);
    }
}
