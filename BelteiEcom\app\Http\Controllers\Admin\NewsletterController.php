<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class NewsletterController extends Controller
{
    /**
     * Display newsletter subscribers
     */
    public function index()
    {
        $subscribers = Newsletter::active()
            ->orderBy('subscribed_at', 'desc')
            ->paginate(20);

        $stats = [
            'total_subscribers' => Newsletter::active()->count(),
            'total_unsubscribed' => Newsletter::where('is_active', false)->count(),
            'recent_subscribers' => Newsletter::active()->where('subscribed_at', '>=', now()->subDays(7))->count(),
        ];

        return view('admin.newsletter.index', compact('subscribers', 'stats'));
    }

    /**
     * Show send newsletter form
     */
    public function create()
    {
        $subscriberCount = Newsletter::active()->count();
        return view('admin.newsletter.create', compact('subscriberCount'));
    }

    /**
     * Send newsletter to all subscribers
     */
    public function send(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'send_to' => 'required|in:all,test'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            if ($request->send_to === 'test') {
                // Send test email to admin
                $this->sendTestEmail($request->subject, $request->message);
                return back()->with('success', 'Test email sent successfully!');
            } else {
                // Send to all subscribers
                $subscribers = Newsletter::active()->get();
                $sentCount = 0;

                foreach ($subscribers as $subscriber) {
                    try {
                        $this->sendNewsletterEmail($subscriber, $request->subject, $request->message);
                        $sentCount++;
                    } catch (\Exception $e) {
                        \Log::error('Failed to send newsletter to ' . $subscriber->email . ': ' . $e->getMessage());
                    }
                }

                return back()->with('success', "Newsletter sent successfully to {$sentCount} subscribers!");
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to send newsletter: ' . $e->getMessage());
        }
    }

    /**
     * Send test email to admin
     */
    private function sendTestEmail($subject, $message)
    {
        $adminEmail = auth()->user()->email;

        Mail::send('emails.newsletter-broadcast', [
            'subject' => $subject,
            'content' => $message,
            'subscriber' => (object) ['email' => $adminEmail, 'name' => 'Admin Test']
        ], function ($mail) use ($subject, $adminEmail) {
            $mail->to($adminEmail)
                 ->subject('[TEST] ' . $subject)
                 ->from(config('mail.from.address'), config('mail.from.name'));
        });
    }

    /**
     * Send newsletter email to subscriber
     */
    private function sendNewsletterEmail($subscriber, $subject, $message)
    {
        Mail::send('emails.newsletter-broadcast', [
            'subject' => $subject,
            'content' => $message,
            'subscriber' => $subscriber
        ], function ($mail) use ($subject, $subscriber) {
            $mail->to($subscriber->email, $subscriber->name)
                 ->subject($subject)
                 ->from(config('mail.from.address'), config('mail.from.name'));
        });
    }

    /**
     * Export subscribers
     */
    public function export()
    {
        $subscribers = Newsletter::active()->get();

        $filename = 'newsletter_subscribers_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($subscribers) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Email', 'Name', 'Subscribed Date', 'Status']);

            foreach ($subscribers as $subscriber) {
                fputcsv($file, [
                    $subscriber->email,
                    $subscriber->name ?: 'N/A',
                    $subscriber->subscribed_at->format('Y-m-d H:i:s'),
                    $subscriber->is_active ? 'Active' : 'Inactive'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
