<?php

namespace App\Http\Controllers;

use App\Models\Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class NewsletterController extends Controller
{
    /**
     * Subscribe to newsletter
     */
    public function subscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please enter a valid email address.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Check if already subscribed
            $existing = Newsletter::where('email', $request->email)->first();

            if ($existing) {
                if ($existing->is_active) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You are already subscribed to our newsletter!'
                    ]);
                } else {
                    // Reactivate subscription and update name if user is logged in
                    $existing->update([
                        'is_active' => true,
                        'unsubscribed_at' => null,
                        'name' => $this->getSubscriberName($request)
                    ]);
                    return response()->json([
                        'success' => true,
                        'message' => 'Welcome back! Your subscription has been reactivated.'
                    ]);
                }
            }

            // Create new subscription (inactive until confirmed)
            $newsletter = Newsletter::create([
                'email' => $request->email,
                'name' => $this->getSubscriberName($request),
                'is_active' => false,
                'email_confirmed' => false,
                'subscribed_at' => now()
            ]);

            // Send confirmation email
            $this->sendConfirmationEmail($newsletter);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for subscribing! Check your email for confirmation.',
                'refresh_page' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong. Please try again later.'
            ], 500);
        }
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribe($token)
    {
        $newsletter = Newsletter::where('subscription_token', $token)->first();

        if (!$newsletter) {
            return view('newsletter.unsubscribe-error');
        }

        if (!$newsletter->is_active) {
            return view('newsletter.already-unsubscribed', compact('newsletter'));
        }

        $newsletter->unsubscribe();

        return view('newsletter.unsubscribed', compact('newsletter'));
    }

    /**
     * Confirm newsletter subscription
     */
    public function confirm($token)
    {
        $newsletter = Newsletter::where('confirmation_token', $token)->first();

        if (!$newsletter) {
            return view('newsletter.confirmation-error');
        }

        if ($newsletter->email_confirmed) {
            return view('newsletter.already-confirmed', compact('newsletter'));
        }

        // Check if confirmation link is expired (24 hours)
        if ($newsletter->created_at->diffInHours(now()) > 24) {
            return view('newsletter.confirmation-expired', compact('newsletter'));
        }

        // Confirm the subscription
        $newsletter->confirmEmail();

        // Send welcome email after confirmation
        $this->sendWelcomeEmail($newsletter);

        return view('newsletter.confirmed', compact('newsletter'));
    }

    /**
     * Get subscriber name from authenticated user or request
     */
    private function getSubscriberName($request)
    {
        // If user is logged in, use their name
        if (auth()->check()) {
            return auth()->user()->name;
        }

        // If user provided name in request, use that
        if ($request->has('name') && !empty($request->name)) {
            return $request->name;
        }

        // If email matches a registered user, use their name
        if ($request->has('email')) {
            $user = \App\Models\User::where('email', $request->email)->first();
            if ($user) {
                return $user->name;
            }
        }

        // Default to null
        return null;
    }

    /**
     * Send confirmation email to new subscriber
     */
    private function sendConfirmationEmail($newsletter)
    {
        try {
            Mail::send('emails.newsletter-confirmation', ['newsletter' => $newsletter], function ($message) use ($newsletter) {
                $message->to($newsletter->email, $newsletter->name)
                        ->subject('📧 Please Confirm Your Newsletter Subscription - ' . config('app.name'))
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });
        } catch (\Exception $e) {
            // Log error but don't fail the subscription
            \Log::error('Failed to send confirmation email: ' . $e->getMessage());
        }
    }

    /**
     * Send welcome email to confirmed subscriber
     */
    private function sendWelcomeEmail($newsletter)
    {
        try {
            Mail::send('emails.newsletter-welcome', ['newsletter' => $newsletter], function ($message) use ($newsletter) {
                $message->to($newsletter->email, $newsletter->name)
                        ->subject('🎉 Welcome to ' . config('app.name') . ' Newsletter!')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });
        } catch (\Exception $e) {
            // Log error but don't fail the subscription
            \Log::error('Failed to send welcome email: ' . $e->getMessage());
        }
    }
}
