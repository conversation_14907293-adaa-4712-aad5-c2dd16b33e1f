<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class AdminUsersController extends Controller
{
    /**
     * Display a listing of the admin users.
     */
    public function index(Request $request)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $query = User::where('is_admin', true);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Email verification filter
        if ($request->has('email_verified') && $request->email_verified !== '') {
            if ($request->email_verified == '1') {
                $query->whereNotNull('email_verified_at');
            } else {
                $query->whereNull('email_verified_at');
            }
        }

        $admins = $query->orderBy('created_at', 'desc')->paginate(12);
        $adminsPaginator = $admins;

        // Get statistics
        $totalAdmins = User::where('is_admin', true)->count();
        $newAdminsToday = User::where('is_admin', true)->whereDate('created_at', Carbon::today())->count();
        $newAdminsThisMonth = User::where('is_admin', true)
                                ->whereMonth('created_at', Carbon::now()->month)
                                ->whereYear('created_at', Carbon::now()->year)
                                ->count();
        $unverifiedAdmins = User::where('is_admin', true)
                               ->whereNull('email_verified_at')
                               ->count();

        return view('admin.admins.index', compact(
            'admins',
            'adminsPaginator',
            'totalAdmins',
            'newAdminsToday',
            'newAdminsThisMonth',
            'unverifiedAdmins'
        ));
    }

    /**
     * Display the specified admin.
     */
    public function show($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $admin = User::where('is_admin', true)->findOrFail($id);
        return view('admin.admins.show', compact('admin'));
    }

    /**
     * Show the form for editing the specified admin.
     */
    public function edit($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $admin = User::where('is_admin', true)->findOrFail($id);
        return view('admin.admins.edit', compact('admin'));
    }

    /**
     * Update the specified admin in storage.
     */
    public function update(Request $request, $id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $admin = User::where('is_admin', true)->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $data = $request->only(['name', 'email', 'phone']);

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $admin->update($data);

        return redirect()->route('admin.admins.index')->with('success', 'Admin updated successfully.');
    }

    /**
     * Remove the specified admin from storage.
     */
    public function destroy($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $admin = User::where('is_admin', true)->findOrFail($id);

        // Prevent deleting the current logged-in admin
        if ($admin->id === Auth::id()) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'You cannot delete your own admin account.');
        }

        // Check if this is the last admin
        if (User::where('is_admin', true)->count() <= 1) {
            return redirect()->route('admin.admins.index')
                           ->with('error', 'Cannot delete the last admin account.');
        }

        $admin->delete();

        return redirect()->route('admin.admins.index')->with('success', 'Admin deleted successfully.');
    }

    /**
     * Show the form for creating a new admin.
     */
    public function create()
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        return view('admin.admins.create');
    }

    /**
     * Store a newly created admin in storage.
     */
    public function store(Request $request)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'is_admin' => true,
        ]);

        return redirect()->route('admin.admins.index')->with('success', 'Admin created successfully.');
    }

    /**
     * Manually verify an admin's email
     */
    public function verifyEmail($id)
    {
        // Check if user is authenticated and is an admin
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect('/login');
        }

        $admin = User::where('is_admin', true)->findOrFail($id);

        // Check if email is already verified
        if ($admin->hasVerifiedEmail()) {
            return redirect()->route('admin.admins.index')
                ->with('info', 'Admin email is already verified.');
        }

        // Mark email as verified
        $admin->markEmailAsVerified();

        return redirect()->route('admin.admins.index')
            ->with('success', 'Admin email has been verified successfully.');
    }
}
