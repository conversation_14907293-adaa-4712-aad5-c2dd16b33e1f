<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\ChatMessage;
use App\Models\ChatAgentStatus;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ChatService
{
    /**
     * Start a new chat conversation.
     */
    public function startConversation($data)
    {
        $userId = Auth::id();
        $sessionId = $data['session_id'] ?? session()->getId();
        
        // Check if user already has an active conversation
        $existingConversation = ChatConversation::where(function ($query) use ($userId, $sessionId) {
            if ($userId) {
                $query->where('user_id', $userId);
            } else {
                $query->where('session_id', $sessionId);
            }
        })
        ->whereIn('status', ['waiting', 'active'])
        ->first();

        if ($existingConversation) {
            return $existingConversation;
        }

        // Create new conversation
        $conversation = ChatConversation::create([
            'user_id' => $userId,
            'guest_name' => $data['guest_name'] ?? null,
            'guest_email' => $data['guest_email'] ?? null,
            'session_id' => $sessionId,
            'status' => 'waiting',
            'priority' => $data['priority'] ?? 'normal',
            'subject' => $data['subject'] ?? null,
            'initial_message' => $data['message'] ?? null,
            'customer_info' => [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'url' => request()->headers->get('referer'),
                'timestamp' => now()->toISOString(),
            ],
        ]);

        // Add initial message if provided
        if (!empty($data['message'])) {
            $this->sendMessage($conversation->id, $data['message'], 'customer');
        }

        // Try to auto-assign to available agent
        $conversation->autoAssign();

        return $conversation;
    }

    /**
     * Send a message in a conversation.
     */
    public function sendMessage($conversationId, $message, $senderType = null, $userId = null, $attachments = [])
    {
        $conversation = ChatConversation::findOrFail($conversationId);
        
        // Determine sender type if not provided
        if (!$senderType) {
            $senderType = $this->determineSenderType($conversation, $userId);
        }

        // Create message
        $chatMessage = ChatMessage::create([
            'conversation_id' => $conversationId,
            'user_id' => $userId ?: Auth::id(),
            'sender_type' => $senderType,
            'message' => $message,
            'attachments' => $attachments,
            'message_type' => empty($attachments) ? 'text' : 'file',
        ]);

        // Update conversation activity
        $conversation->updateActivity();

        // If conversation is waiting and customer sends message, try auto-assign again
        if ($conversation->isWaiting() && $senderType === 'customer') {
            $conversation->autoAssign();
        }

        return $chatMessage;
    }

    /**
     * Get conversation messages.
     */
    public function getMessages($conversationId, $limit = 50, $offset = 0)
    {
        return ChatMessage::where('conversation_id', $conversationId)
                          ->with('user')
                          ->orderBy('created_at', 'asc')
                          ->limit($limit)
                          ->offset($offset)
                          ->get();
    }

    /**
     * Mark messages as read.
     */
    public function markAsRead($conversationId, $userId = null)
    {
        $userId = $userId ?: Auth::id();
        $conversation = ChatConversation::findOrFail($conversationId);
        
        $conversation->markAsReadForUser($userId);
        
        return true;
    }

    /**
     * Get user's conversations.
     */
    public function getUserConversations($userId = null, $sessionId = null, $limit = 20)
    {
        $query = ChatConversation::with(['latestMessage', 'agent']);
        
        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        } else {
            return collect();
        }
        
        return $query->orderBy('last_activity_at', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Get agent's conversations.
     */
    public function getAgentConversations($agentId, $status = null, $limit = 20)
    {
        $query = ChatConversation::with(['customer', 'latestMessage'])
                                ->where('agent_id', $agentId);
        
        if ($status) {
            $query->where('status', $status);
        }
        
        return $query->orderBy('last_activity_at', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Assign conversation to agent.
     */
    public function assignToAgent($conversationId, $agentId)
    {
        $conversation = ChatConversation::findOrFail($conversationId);
        $agent = User::findOrFail($agentId);
        
        // Check if agent is available
        $agentStatus = ChatAgentStatus::where('user_id', $agentId)->first();
        if (!$agentStatus || !$agentStatus->isAvailable()) {
            throw new \Exception('Agent is not available for new conversations.');
        }
        
        $conversation->assignAgent($agentId);
        
        return $conversation;
    }

    /**
     * Transfer conversation to another agent.
     */
    public function transferConversation($conversationId, $newAgentId, $reason = null)
    {
        $conversation = ChatConversation::findOrFail($conversationId);
        $oldAgentId = $conversation->agent_id;
        
        // Check if new agent is available
        $newAgentStatus = ChatAgentStatus::where('user_id', $newAgentId)->first();
        if (!$newAgentStatus || !$newAgentStatus->isAvailable()) {
            throw new \Exception('Target agent is not available for new conversations.');
        }
        
        // Update old agent's conversation count
        if ($oldAgentId) {
            $oldAgentStatus = ChatAgentStatus::where('user_id', $oldAgentId)->first();
            if ($oldAgentStatus && $oldAgentStatus->current_conversations > 0) {
                $oldAgentStatus->decrement('current_conversations');
            }
        }
        
        // Assign to new agent
        $conversation->update([
            'agent_id' => $newAgentId,
            'status' => 'transferred',
        ]);
        
        // Update new agent's conversation count
        $newAgentStatus->increment('current_conversations');
        
        // Add system message
        $transferMessage = $reason 
            ? "Conversation transferred to another agent. Reason: {$reason}"
            : "Conversation has been transferred to another agent.";
            
        $this->sendMessage($conversationId, $transferMessage, 'system');
        
        // Set status back to active
        $conversation->update(['status' => 'active']);
        
        return $conversation;
    }

    /**
     * Close conversation.
     */
    public function closeConversation($conversationId, $reason = null, $rating = null, $feedback = null)
    {
        $conversation = ChatConversation::findOrFail($conversationId);
        
        $conversation->close($reason);
        
        // Add rating and feedback if provided
        if ($rating || $feedback) {
            $conversation->update([
                'rating' => $rating,
                'feedback' => $feedback,
            ]);
        }
        
        return $conversation;
    }

    /**
     * Update agent status.
     */
    public function updateAgentStatus($agentId, $status, $statusMessage = null)
    {
        $agentStatus = ChatAgentStatus::updateOrCreateForUser($agentId);
        
        switch ($status) {
            case 'online':
                $agentStatus->setOnline($statusMessage);
                break;
            case 'away':
                $agentStatus->setAway($statusMessage);
                break;
            case 'busy':
                $agentStatus->setBusy($statusMessage);
                break;
            case 'offline':
                $agentStatus->setOffline();
                break;
        }
        
        return $agentStatus;
    }

    /**
     * Get waiting conversations count.
     */
    public function getWaitingCount()
    {
        return ChatConversation::waiting()->count();
    }

    /**
     * Get online agents count.
     */
    public function getOnlineAgentsCount()
    {
        return ChatAgentStatus::online()->count();
    }

    /**
     * Get chat statistics.
     */
    public function getStatistics($days = 30)
    {
        $startDate = now()->subDays($days);
        
        $conversations = ChatConversation::where('created_at', '>=', $startDate)->get();
        
        return [
            'total_conversations' => $conversations->count(),
            'active_conversations' => ChatConversation::active()->count(),
            'waiting_conversations' => ChatConversation::waiting()->count(),
            'closed_conversations' => $conversations->where('status', 'closed')->count(),
            'average_rating' => $conversations->whereNotNull('rating')->avg('rating'),
            'online_agents' => $this->getOnlineAgentsCount(),
            'total_messages' => ChatMessage::where('created_at', '>=', $startDate)->count(),
            'response_time' => $this->calculateAverageResponseTime($conversations),
        ];
    }

    /**
     * Search conversations.
     */
    public function searchConversations($query, $filters = [])
    {
        $conversations = ChatConversation::with(['customer', 'agent', 'latestMessage']);
        
        // Search in customer names, emails, and messages
        if ($query) {
            $conversations->where(function ($q) use ($query) {
                $q->whereHas('customer', function ($userQuery) use ($query) {
                    $userQuery->where('name', 'like', "%{$query}%")
                             ->orWhere('email', 'like', "%{$query}%");
                })
                ->orWhere('guest_name', 'like', "%{$query}%")
                ->orWhere('guest_email', 'like', "%{$query}%")
                ->orWhereHas('messages', function ($msgQuery) use ($query) {
                    $msgQuery->where('message', 'like', "%{$query}%");
                });
            });
        }
        
        // Apply filters
        if (isset($filters['status'])) {
            $conversations->where('status', $filters['status']);
        }
        
        if (isset($filters['agent_id'])) {
            $conversations->where('agent_id', $filters['agent_id']);
        }
        
        if (isset($filters['priority'])) {
            $conversations->where('priority', $filters['priority']);
        }
        
        if (isset($filters['date_from'])) {
            $conversations->where('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $conversations->where('created_at', '<=', $filters['date_to']);
        }
        
        return $conversations->orderBy('last_activity_at', 'desc')->paginate(20);
    }

    /**
     * Determine sender type based on conversation and user.
     */
    private function determineSenderType($conversation, $userId)
    {
        if (!$userId) {
            return 'customer';
        }
        
        if ($conversation->agent_id === $userId) {
            return 'agent';
        }
        
        if ($conversation->user_id === $userId) {
            return 'customer';
        }
        
        // Check if user is an admin/agent
        $user = User::find($userId);
        if ($user && $user->is_admin) {
            return 'agent';
        }
        
        return 'customer';
    }

    /**
     * Calculate average response time.
     */
    private function calculateAverageResponseTime($conversations)
    {
        $responseTimes = [];
        
        foreach ($conversations as $conversation) {
            $messages = $conversation->messages()->orderBy('created_at')->get();
            $lastCustomerMessage = null;
            
            foreach ($messages as $message) {
                if ($message->sender_type === 'customer') {
                    $lastCustomerMessage = $message;
                } elseif ($message->sender_type === 'agent' && $lastCustomerMessage) {
                    $responseTime = $lastCustomerMessage->created_at->diffInMinutes($message->created_at);
                    $responseTimes[] = $responseTime;
                    $lastCustomerMessage = null;
                }
            }
        }
        
        return empty($responseTimes) ? 0 : round(array_sum($responseTimes) / count($responseTimes), 2);
    }
}
