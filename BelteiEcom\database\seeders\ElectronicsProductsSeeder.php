<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ElectronicsProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find or create Electronics category
        $category = Category::firstOrCreate(
            ['name' => 'Electronics'],
            [
                'description' => 'Electronic devices and gadgets',
                'slug' => 'electronics'
            ]
        );

        // Create sample electronics products
        $products = [
            [
                'name' => 'Smartphone X',
                'description' => 'Latest smartphone with advanced features and high-resolution camera.',
                'price' => 699.99,
                'stock' => 50,
                'category_id' => $category->id,
            ],
            [
                'name' => 'Laptop Pro',
                'description' => 'Powerful laptop with fast processor and high-quality display.',
                'price' => 1299.99,
                'stock' => 25,
                'category_id' => $category->id,
            ],
            [
                'name' => 'Wireless Headphones',
                'description' => 'Noise-cancelling wireless headphones with long battery life.',
                'price' => 199.99,
                'stock' => 100,
                'category_id' => $category->id,
            ],
            [
                'name' => 'Smart Watch',
                'description' => 'Fitness tracker and smartwatch with heart rate monitor and GPS.',
                'price' => 249.99,
                'stock' => 75,
                'category_id' => $category->id,
            ],
            [
                'name' => 'Bluetooth Speaker',
                'description' => 'Portable Bluetooth speaker with excellent sound quality and waterproof design.',
                'price' => 89.99,
                'stock' => 120,
                'category_id' => $category->id,
            ],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(
                ['name' => $productData['name']],
                $productData
            );
        }
    }
}
