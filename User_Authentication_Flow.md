# 🔐 User Authentication Flow - BelteiEcom

## 📋 Authentication System Overview

BelteiEcom implements a comprehensive authentication system supporting multiple login methods, role-based access control, and secure user management.

## 🚪 Registration Flow

### 1. User Registration Process

```mermaid
graph TD
    A[User visits registration page] --> B[Fill registration form]
    B --> C[Submit form data]
    C --> D{Validation check}
    D -->|Valid| E[Create user account]
    D -->|Invalid| F[Show validation errors]
    F --> B
    E --> G[Send verification email]
    G --> H[Redirect to login page]
    H --> I[User checks email]
    I --> J[Click verification link]
    J --> K[Account verified]
    K --> L[User can login]
```

### Registration Form Fields
- **Full Name** (required)
- **Email Address** (required, unique)
- **Password** (required, min 8 characters)
- **Password Confirmation** (required, must match)
- **Terms & Conditions** (required checkbox)

### Registration Validation Rules
```php
'name' => 'required|string|max:255',
'email' => 'required|string|email|max:255|unique:users',
'password' => 'required|string|min:8|confirmed',
'terms' => 'required|accepted'
```

### Email Verification Process
1. **Account Creation** - User account created with `email_verified_at = null`
2. **Verification Email** - Automated email sent with verification link
3. **Email Template** - Professional HTML email with company branding
4. **Link Expiration** - Verification links expire after 24 hours
5. **Account Activation** - Email verification updates `email_verified_at` timestamp

## 🔑 Login Flow

### 1. Standard Email/Password Login

```mermaid
graph TD
    A[User visits login page] --> B[Enter email & password]
    B --> C[Submit login form]
    C --> D{Credentials valid?}
    D -->|Valid| E{Email verified?}
    D -->|Invalid| F[Show error message]
    F --> B
    E -->|Verified| G[Create user session]
    E -->|Not verified| H[Redirect to verification notice]
    G --> I{User is admin?}
    I -->|Yes| J[Redirect to admin dashboard]
    I -->|No| K[Redirect to home page]
    H --> L[Resend verification option]
```

### Login Form Features
- **Email Address** field with validation
- **Password** field with show/hide toggle
- **Remember Me** checkbox for persistent sessions
- **Forgot Password** link
- **Social Login** buttons (Google OAuth)

### Session Management
- **Session Duration** - 2 hours for regular users, 8 hours for admins
- **Session Security** - CSRF token protection
- **Auto Logout** - Inactive sessions automatically expire
- **Multiple Sessions** - Users can login from multiple devices

## 🌐 Google OAuth Integration

### 1. Google OAuth Flow

```mermaid
graph TD
    A[User clicks 'Login with Google'] --> B[Redirect to Google OAuth]
    B --> C[User authorizes application]
    C --> D[Google returns authorization code]
    D --> E[Exchange code for access token]
    E --> F[Fetch user profile from Google]
    F --> G{User exists in database?}
    G -->|Yes| H[Login existing user]
    G -->|No| I[Create new user account]
    I --> J[Auto-verify email]
    J --> H
    H --> K[Create user session]
    K --> L[Redirect to intended page]
```

### Google OAuth Configuration
```php
// config/services.php
'google' => [
    'client_id' => env('GOOGLE_CLIENT_ID'),
    'client_secret' => env('GOOGLE_CLIENT_SECRET'),
    'redirect' => env('GOOGLE_REDIRECT_URL'),
],
```

### OAuth User Data Handling
- **Profile Information** - Name, email, profile picture
- **Email Verification** - OAuth emails are auto-verified
- **Account Linking** - Link OAuth accounts to existing email accounts
- **Profile Pictures** - Store Google profile picture URLs

## 🔒 Password Management

### 1. Password Reset Flow

```mermaid
graph TD
    A[User clicks 'Forgot Password'] --> B[Enter email address]
    B --> C[Submit reset request]
    C --> D{Email exists?}
    D -->|Yes| E[Generate reset token]
    D -->|No| F[Show success message anyway]
    E --> G[Send reset email]
    G --> H[User clicks reset link]
    H --> I[Validate token]
    I --> J{Token valid?}
    J -->|Valid| K[Show new password form]
    J -->|Invalid| L[Show error message]
    K --> M[Submit new password]
    M --> N[Update password]
    N --> O[Auto-login user]
    O --> P[Redirect to dashboard]
```

### Password Security Features
- **Strong Password Requirements** - Minimum 8 characters
- **Password Hashing** - Bcrypt encryption
- **Reset Token Expiration** - 1 hour expiration time
- **Single Use Tokens** - Reset tokens can only be used once
- **Password History** - Prevent reusing recent passwords

## 👤 User Profile Management

### Profile Information
- **Personal Details** - Name, email, phone number
- **Profile Picture** - Upload or use Google OAuth picture
- **Address Information** - Shipping and billing addresses
- **Account Settings** - Password change, email preferences

### Profile Update Flow
1. **Access Profile** - Navigate to profile page
2. **Edit Information** - Update personal details
3. **Validation** - Server-side validation of changes
4. **Save Changes** - Update database records
5. **Confirmation** - Success message displayed

## 🛡️ Role-Based Access Control

### User Roles
- **Customer** (`is_admin = false`)
  - Browse products and categories
  - Manage shopping cart
  - Place and track orders
  - Update profile information

- **Admin** (`is_admin = true`)
  - All customer permissions
  - Access admin dashboard
  - Manage products and categories
  - Process orders
  - View analytics and reports

### Middleware Protection
```php
// Admin routes protection
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin/dashboard', [AdminController::class, 'dashboard']);
    Route::resource('/admin/products', ProductController::class);
    Route::resource('/admin/orders', OrderController::class);
});
```

## 🔐 Security Features

### Authentication Security
- **CSRF Protection** - All forms include CSRF tokens
- **Rate Limiting** - Login attempt throttling
- **Session Fixation Protection** - Session regeneration on login
- **Secure Cookies** - HTTPOnly and Secure flags

### Account Security
- **Email Verification** - Required for account activation
- **Password Strength** - Enforced minimum requirements
- **Account Lockout** - Temporary lockout after failed attempts
- **Audit Logging** - Track login attempts and account changes

## 📱 Mobile Authentication

### Mobile-Responsive Design
- **Touch-Friendly Forms** - Optimized for mobile input
- **Social Login Buttons** - Large, easy-to-tap buttons
- **Auto-Focus** - Automatic focus on input fields
- **Keyboard Optimization** - Appropriate input types

### Mobile-Specific Features
- **Biometric Login** - Support for fingerprint/face recognition
- **Auto-Fill Support** - Compatible with password managers
- **Progressive Web App** - Offline authentication capabilities

## 🔄 Session Management

### Session Lifecycle
1. **Session Creation** - On successful login
2. **Session Validation** - On each request
3. **Session Refresh** - Extend session on activity
4. **Session Destruction** - On logout or expiration

### Security Measures
- **Session Encryption** - All session data encrypted
- **IP Validation** - Optional IP address checking
- **User Agent Validation** - Browser fingerprinting
- **Concurrent Session Limits** - Maximum active sessions per user

## 📊 Authentication Analytics

### Login Metrics
- **Login Success Rate** - Track successful vs failed attempts
- **OAuth Usage** - Monitor social login adoption
- **Session Duration** - Average user session length
- **Device Analytics** - Track login devices and browsers

### Security Monitoring
- **Failed Login Attempts** - Monitor brute force attacks
- **Suspicious Activity** - Detect unusual login patterns
- **Account Takeover Prevention** - Alert on suspicious logins
- **Compliance Reporting** - Generate security audit reports
