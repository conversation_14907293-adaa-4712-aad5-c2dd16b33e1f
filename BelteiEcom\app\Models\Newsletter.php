<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Newsletter extends Model
{
    protected $fillable = [
        'email',
        'name',
        'is_active',
        'email_confirmed',
        'subscribed_at',
        'subscription_token',
        'confirmation_token'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'email_confirmed' => 'boolean',
        'subscribed_at' => 'datetime',
        'unsubscribed_at' => 'datetime',
        'email_confirmed_at' => 'datetime',
    ];

    /**
     * Generate subscription token when creating
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($newsletter) {
            if (empty($newsletter->subscription_token)) {
                $newsletter->subscription_token = Str::random(32);
            }
            if (empty($newsletter->confirmation_token)) {
                $newsletter->confirmation_token = Str::random(32);
            }
        });
    }

    /**
     * Scope for active subscribers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribe()
    {
        $this->update([
            'is_active' => false,
            'unsubscribed_at' => now()
        ]);
    }

    /**
     * Resubscribe to newsletter
     */
    public function resubscribe()
    {
        $this->update([
            'is_active' => true,
            'unsubscribed_at' => null
        ]);
    }

    /**
     * Confirm email subscription
     */
    public function confirmEmail()
    {
        $this->update([
            'email_confirmed' => true,
            'email_confirmed_at' => now(),
            'is_active' => true
        ]);
    }
}
