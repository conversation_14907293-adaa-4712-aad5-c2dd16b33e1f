<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main categories
        $categories = [
            [
                'name' => 'Electronics',
                'description' => 'Electronic devices and accessories',
            ],
            [
                'name' => 'Clothing',
                'description' => 'Apparel and fashion items',
            ],
            [
                'name' => 'Home & Kitchen',
                'description' => 'Home appliances and kitchen essentials',
            ],
            [
                'name' => 'Books',
                'description' => 'Books, e-books, and publications',
            ],
            [
                'name' => 'Sports & Outdoors',
                'description' => 'Sports equipment and outdoor gear',
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }

        // Create subcategories
        $subcategories = [
            // Electronics subcategories
            [
                'name' => 'Smartphones',
                'description' => 'Mobile phones and accessories',
                'parent_id' => 1,
            ],
            [
                'name' => 'Laptops',
                'description' => 'Notebook computers and accessories',
                'parent_id' => 1,
            ],
            [
                'name' => 'Audio',
                'description' => 'Headphones, speakers, and audio equipment',
                'parent_id' => 1,
            ],

            // Clothing subcategories
            [
                'name' => 'Men\'s Clothing',
                'description' => 'Clothing for men',
                'parent_id' => 2,
            ],
            [
                'name' => 'Women\'s Clothing',
                'description' => 'Clothing for women',
                'parent_id' => 2,
            ],
            [
                'name' => 'Accessories',
                'description' => 'Fashion accessories',
                'parent_id' => 2,
            ],

            // Home & Kitchen subcategories
            [
                'name' => 'Kitchen Appliances',
                'description' => 'Appliances for the kitchen',
                'parent_id' => 3,
            ],
            [
                'name' => 'Furniture',
                'description' => 'Home furniture',
                'parent_id' => 3,
            ],
            [
                'name' => 'Decor',
                'description' => 'Home decoration items',
                'parent_id' => 3,
            ],

            // Books subcategories
            [
                'name' => 'Fiction',
                'description' => 'Fiction books',
                'parent_id' => 4,
            ],
            [
                'name' => 'Non-Fiction',
                'description' => 'Non-fiction books',
                'parent_id' => 4,
            ],
            [
                'name' => 'Educational',
                'description' => 'Educational and academic books',
                'parent_id' => 4,
            ],

            // Sports & Outdoors subcategories
            [
                'name' => 'Fitness',
                'description' => 'Fitness equipment and accessories',
                'parent_id' => 5,
            ],
            [
                'name' => 'Outdoor Recreation',
                'description' => 'Outdoor recreation equipment',
                'parent_id' => 5,
            ],
            [
                'name' => 'Team Sports',
                'description' => 'Equipment for team sports',
                'parent_id' => 5,
            ],
        ];

        foreach ($subcategories as $subcategory) {
            Category::create($subcategory);
        }
    }
}
