<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

class UserProfileController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the user profile page.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get user's order statistics
        $totalOrders = $user->orders()->count();
        $totalSpent = $user->orders()->sum('total_amount');
        $recentOrders = $user->orders()->latest()->take(5)->get();

        return view('profile.index', compact('user', 'totalOrders', 'totalSpent', 'recentOrders'));
    }

    /**
     * Show the profile edit page.
     */
    public function edit()
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['name', 'email', 'phone', 'address']);

        // Handle profile picture upload
        if ($request->hasFile('profile_picture')) {
            // Delete old profile picture if exists
            if ($user->profile_picture && Storage::disk('public')->exists($user->profile_picture)) {
                Storage::disk('public')->delete($user->profile_picture);
            }

            // Store new profile picture
            $path = $request->file('profile_picture')->store('profile-pictures', 'public');
            $data['profile_picture'] = $path;
        }

        $user->update($data);

        return redirect()->route('profile.index')->with('success', 'Profile updated successfully!');
    }

    /**
     * Remove the user's profile picture.
     */
    public function removeProfilePicture()
    {
        $user = Auth::user();

        if ($user->profile_picture && Storage::disk('public')->exists($user->profile_picture)) {
            Storage::disk('public')->delete($user->profile_picture);
            $user->update(['profile_picture' => null]);
        }

        return redirect()->route('profile.edit')->with('success', 'Profile picture removed successfully!');
    }

    /**
     * Show the password change page.
     */
    public function password()
    {
        return view('profile.password');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('profile.index')->with('success', 'Password updated successfully!');
    }

    /**
     * Show the QR scanner page.
     */
    public function qrScanner()
    {
        $user = Auth::user();
        return view('profile.qr-scanner', compact('user'));
    }

    /**
     * Get user's profile picture URL with fallback logic
     */
    public static function getProfilePictureUrl($user)
    {
        // If user has a profile picture, use it
        if ($user->profile_picture) {
            return asset('storage/' . $user->profile_picture);
        }
        
        // If user is admin and has profile picture from admin dashboard, use it
        if ($user->is_admin && $user->profile_picture) {
            return asset('storage/' . $user->profile_picture);
        }
        
        // If user has Google avatar, use it
        if ($user->avatar) {
            return $user->avatar;
        }
        
        // Return null for fallback to initials
        return null;
    }

    /**
     * Get user's display name initials
     */
    public static function getUserInitials($user)
    {
        $names = explode(' ', trim($user->name));
        if (count($names) >= 2) {
            return strtoupper(substr($names[0], 0, 1) . substr($names[1], 0, 1));
        }
        return strtoupper(substr($user->name, 0, 2));
    }
}
