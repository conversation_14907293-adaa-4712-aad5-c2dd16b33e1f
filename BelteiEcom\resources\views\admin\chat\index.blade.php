@extends('admin.layouts.app')

@section('title', 'Live Chat Management')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-comments text-primary"></i>
            Live Chat Management
        </h1>
        <div class="btn-group">
            <a href="{{ route('admin.chat.agent') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-headset"></i> Agent Dashboard
            </a>
            <a href="{{ route('admin.chat.conversations') }}" class="btn btn-info btn-sm">
                <i class="fas fa-list"></i> All Conversations
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Conversations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($statistics['total_conversations']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comments fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Waiting for Agent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($statistics['waiting_conversations']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Chats
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($statistics['active_conversations']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comment-dots fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Online Agents
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($statistics['online_agents']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                Avg. Rating
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @if($statistics['average_rating'])
                                    {{ number_format($statistics['average_rating'], 1) }}/5
                                    <div class="text-xs text-warning">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star{{ $i <= round($statistics['average_rating']) ? '' : '-o' }}"></i>
                                        @endfor
                                    </div>
                                @else
                                    <span class="text-muted">No ratings</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-dark shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                Avg. Response Time
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $statistics['response_time'] }} min
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-stopwatch fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Closed Today
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($statistics['closed_conversations']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Messages
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($statistics['total_messages']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Waiting Conversations -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock"></i>
                        Waiting for Agent ({{ $waitingConversations->count() }})
                    </h6>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-warning btn-sm" onclick="refreshWaitingList()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if($waitingConversations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Subject</th>
                                        <th>Priority</th>
                                        <th>Waiting Time</th>
                                        <th>Last Message</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="waiting-conversations-list">
                                    @foreach($waitingConversations as $conversation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm mr-2">
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <strong>{{ $conversation->customer_name }}</strong><br>
                                                        <small class="text-muted">{{ $conversation->customer_email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>{{ $conversation->subject ?: 'General Inquiry' }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $conversation->priority_color }}">
                                                    {{ ucfirst($conversation->priority) }}
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ $conversation->created_at->diffForHumans() }}
                                                </small>
                                            </td>
                                            <td>
                                                @if($conversation->latestMessage)
                                                    <small>{{ Str::limit($conversation->latestMessage->message, 50) }}</small>
                                                @else
                                                    <small class="text-muted">No messages</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-success btn-sm" 
                                                            onclick="takeConversation({{ $conversation->id }})"
                                                            title="Take Conversation">
                                                        <i class="fas fa-hand-paper"></i>
                                                    </button>
                                                    <a href="{{ route('admin.chat.conversation', $conversation->id) }}" 
                                                       class="btn btn-info btn-sm" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">No conversations waiting!</h5>
                            <p class="text-muted">All customers are being helped or there are no active chats.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Online Agents -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-users"></i>
                        Online Agents ({{ $onlineAgents->count() }})
                    </h6>
                </div>
                <div class="card-body">
                    @if($onlineAgents->count() > 0)
                        @foreach($onlineAgents as $agentStatus)
                            <div class="d-flex align-items-center mb-3 p-2 border rounded">
                                <div class="avatar-sm mr-3">
                                    <div class="bg-{{ $agentStatus->status_color }} rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <strong>{{ $agentStatus->user->name }}</strong>
                                        <span class="badge badge-{{ $agentStatus->status_color }}">
                                            <i class="{{ $agentStatus->status_icon }}"></i>
                                            {{ ucfirst($agentStatus->status) }}
                                        </span>
                                    </div>
                                    <small class="text-muted">
                                        {{ $agentStatus->current_conversations }}/{{ $agentStatus->max_conversations }} chats
                                    </small>
                                    @if($agentStatus->status_message)
                                        <br><small class="text-info">{{ $agentStatus->status_message }}</small>
                                    @endif
                                    <div class="progress mt-1" style="height: 4px;">
                                        <div class="progress-bar bg-{{ $agentStatus->workload_percentage > 80 ? 'danger' : ($agentStatus->workload_percentage > 60 ? 'warning' : 'success') }}" 
                                             style="width: {{ $agentStatus->workload_percentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No agents are currently online.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.border-left-danger { border-left: 0.25rem solid #e74a3b !important; }
.border-left-secondary { border-left: 0.25rem solid #858796 !important; }
.border-left-dark { border-left: 0.25rem solid #5a5c69 !important; }

.avatar-sm {
    flex-shrink: 0;
}

.table-responsive {
    max-height: 500px;
    overflow-y: auto;
}

.progress {
    background-color: #e9ecef;
}
</style>

<script>
// Auto-refresh waiting conversations every 30 seconds
setInterval(function() {
    refreshWaitingList();
}, 30000);

function refreshWaitingList() {
    // You can implement AJAX refresh here
    location.reload();
}

function takeConversation(conversationId) {
    if (confirm('Take this conversation?')) {
        fetch(`/admin/chat/conversation/${conversationId}/take`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect;
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while taking the conversation.');
        });
    }
}
</script>
@endsection
