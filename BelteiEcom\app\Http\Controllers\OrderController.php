<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\BakongPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the checkout page.
     */
    public function checkout()
    {
        $cartItems = Auth::user()->cartItems()->with('product')->get();

        if ($cartItems->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty!');
        }

        $total = $cartItems->sum(function ($item) {
            return $item->product->price * $item->quantity;
        });

        return view('orders.checkout', compact('cartItems', 'total'));
    }

    /**
     * Place a new order.
     */
    public function place(Request $request)
    {
        try {
            \Log::info('Order placement started');

            $request->validate([
                'shipping_address' => 'required|string|max:255',
                'phone_number' => 'required|string|max:20',
                'payment_method' => 'required|in:cash_on_delivery,khqr',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
            ]);

            \Log::info('Order validation passed');

            $user = Auth::user();

            if (!$user) {
                \Log::error('User not authenticated');
                return redirect()->route('login')->with('error', 'Please login to place an order.');
            }

            \Log::info('User authenticated: ' . $user->id);

            $cartItems = $user->cartItems()->with('product')->get();
            \Log::info('Cart items retrieved: ' . $cartItems->count());

            if ($cartItems->isEmpty()) {
                \Log::warning('Cart is empty');
                return redirect()->route('cart.index')->with('error', 'Your cart is empty!');
            }

            // Check if all products have sufficient stock
            foreach ($cartItems as $cartItem) {
                if ($cartItem->quantity > $cartItem->product->stock) {
                    \Log::warning('Insufficient stock for product: ' . $cartItem->product->id);
                    return redirect()->route('cart.index')->with('error', 'Sorry, ' . $cartItem->product->name . ' only has ' . $cartItem->product->stock . ' items in stock.');
                }
            }

            \Log::info('Stock validation passed');

            $total = $cartItems->sum(function ($item) {
                return $item->product->price * $item->quantity;
            });

            \Log::info('Order total calculated: ' . $total);

            // For KHQR payments, check if payment is already verified (AJAX request)
            if ($request->payment_method === 'khqr') {
                \Log::info('KHQR payment selected');

                // Check if this is an AJAX request with payment verification
                if ($request->ajax() && $request->has('payment_verified') && $request->payment_verified == '1') {
                    \Log::info('KHQR payment already verified via AJAX - creating order immediately');

                    // Payment is already verified, create order immediately
                    DB::beginTransaction();

                    try {
                        // Create the order
                        $order = Order::create([
                            'user_id' => $user->id,
                            'total_amount' => $total,
                            'status' => 'new', // Set as new since payment is verified (paid status not in enum)
                            'shipping_address' => $request->shipping_address,
                            'phone_number' => $request->phone_number,
                            'payment_method' => $request->payment_method,
                            'latitude' => $request->latitude,
                            'longitude' => $request->longitude,
                        ]);

                        \Log::info('Order created with ID: ' . $order->id);

                        // Create order items
                        foreach ($cartItems as $cartItem) {
                            $order->orderItems()->create([
                                'product_id' => $cartItem->product_id,
                                'quantity' => $cartItem->quantity,
                                'price' => $cartItem->product->price,
                            ]);

                            // Reduce product stock
                            $cartItem->product->decrement('stock', $cartItem->quantity);
                        }

                        // Clear the cart
                        $user->cartItems()->delete();

                        // Send Telegram notification
                        $this->sendTelegramNotification($order);

                        DB::commit();
                        \Log::info('KHQR order completed successfully');

                        return response()->json([
                            'success' => true,
                            'redirect_url' => route('orders.success', $order->success_token),
                            'message' => 'Order created successfully!'
                        ]);

                    } catch (\Exception $e) {
                        DB::rollBack();
                        \Log::error('KHQR order creation error: ' . $e->getMessage());
                        return response()->json([
                            'success' => false,
                            'message' => 'Error creating order: ' . $e->getMessage()
                        ]);
                    }
                } else {
                    // Regular KHQR flow - store data in session and redirect to payment
                    \Log::info('KHQR payment selected - storing order data in session');

                    // Store order data in session for later use after payment verification
                    session([
                        'pending_order_data' => [
                            'user_id' => $user->id,
                            'total_amount' => $total,
                            'shipping_address' => $request->shipping_address,
                            'phone_number' => $request->phone_number,
                            'payment_method' => $request->payment_method,
                            'latitude' => $request->latitude,
                            'longitude' => $request->longitude,
                            'cart_items' => $cartItems->map(function ($item) {
                                return [
                                    'product_id' => $item->product_id,
                                    'quantity' => $item->quantity,
                                    'price' => $item->product->price,
                                    'product_name' => $item->product->name,
                                ];
                            })->toArray()
                        ]
                    ]);

                    \Log::info('Order data stored in session, redirecting to Bakong payment');
                    // Create a temporary order ID for payment processing (we'll use timestamp + user_id)
                    $tempOrderId = time() . '_' . $user->id;
                    return redirect()->route('bakong.request', $tempOrderId);
                }
            }

            // For cash on delivery, create order immediately and process normally
            DB::beginTransaction();
            \Log::info('Database transaction started for cash on delivery');

            try {
                // Create the order
                \Log::info('Creating order for cash on delivery');
                $order = Order::create([
                    'user_id' => $user->id,
                    'total_amount' => $total,
                    'status' => 'new',
                    'shipping_address' => $request->shipping_address,
                    'phone_number' => $request->phone_number,
                    'payment_method' => $request->payment_method,
                    'latitude' => $request->latitude,
                    'longitude' => $request->longitude,
                ]);

                \Log::info('Order created with ID: ' . $order->id);

                // Create order items from cart items
                \Log::info('Creating order items');
                foreach ($cartItems as $cartItem) {
                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $cartItem->product_id,
                        'quantity' => $cartItem->quantity,
                        'price' => $cartItem->product->price,
                    ]);

                    // Update product stock
                    $product = $cartItem->product;
                    $product->stock -= $cartItem->quantity;
                    $product->save();

                    \Log::info('Product stock updated for product: ' . $product->id);
                }

                \Log::info('Order items created');

                // Clear the cart
                \Log::info('Clearing cart');
                CartItem::where('user_id', $user->id)->delete();
                \Log::info('Cart cleared');

                DB::commit();
                \Log::info('Database transaction committed');

                // Send Telegram notification for cash on delivery
                $this->sendTelegramNotification($order);

                // For cash on delivery, redirect to success page using success token
                \Log::info('Redirecting to success page for order: ' . $order->id . ' with token: ' . $order->success_token);
                return redirect()->route('orders.success', $order->success_token);
            } catch (\Exception $e) {
                DB::rollBack();
                \Log::error('Order creation error: ' . $e->getMessage());
                \Log::error('Stack trace: ' . $e->getTraceAsString());
                return redirect()->back()->with('error', 'An error occurred while placing your order. Please try again. Error: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            \Log::error('Order validation error: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return redirect()->back()->with('error', 'An error occurred while validating your order. Please try again. Error: ' . $e->getMessage());
        }
    }

    /**
     * Display the order success page.
     */
    public function success($token)
    {
        $order = Order::where('success_token', $token)
                     ->with('orderItems.product', 'user')
                     ->firstOrFail();

        // Get payment information if available
        $payment = BakongPayment::where('order_id', $order->id)->first();

        return view('orders.success', compact('order', 'payment'));
    }

    /**
     * Create order after payment verification from session data.
     */
    public function createOrderFromPayment($tempOrderId)
    {
        try {
            \Log::info('Creating order after payment verification for temp ID: ' . $tempOrderId);

            // Get order data from session
            $orderData = session('pending_order_data');
            if (!$orderData) {
                \Log::error('No pending order data found in session for temp ID: ' . $tempOrderId);
                return false;
            }

            \Log::info('Found pending order data in session');

            DB::beginTransaction();

            try {
                // Create the order
                $order = Order::create([
                    'user_id' => $orderData['user_id'],
                    'total_amount' => $orderData['total_amount'],
                    'status' => 'new',
                    'shipping_address' => $orderData['shipping_address'],
                    'phone_number' => $orderData['phone_number'],
                    'payment_method' => $orderData['payment_method'],
                    'latitude' => $orderData['latitude'] ?? null,
                    'longitude' => $orderData['longitude'] ?? null,
                ]);

                \Log::info('Order created with ID: ' . $order->id);

                // Create order items and reduce stock
                foreach ($orderData['cart_items'] as $cartItem) {
                    // Get the product to check stock
                    $product = \App\Models\Product::find($cartItem['product_id']);

                    if (!$product) {
                        \Log::error('Product not found: ' . $cartItem['product_id']);
                        DB::rollBack();
                        return false;
                    }

                    // Check if there's still enough stock
                    if ($cartItem['quantity'] > $product->stock) {
                        \Log::error('Insufficient stock for product ' . $product->id . ' when creating order');
                        DB::rollBack();
                        return false;
                    }

                    // Create order item
                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $cartItem['product_id'],
                        'quantity' => $cartItem['quantity'],
                        'price' => $cartItem['price'],
                    ]);

                    // Reduce stock
                    $product->stock -= $cartItem['quantity'];
                    $product->save();

                    \Log::info('Created order item and reduced stock for product ' . $product->id . ' by ' . $cartItem['quantity']);
                }

                // Clear the user's cart
                CartItem::where('user_id', $orderData['user_id'])->delete();
                \Log::info('Cleared cart for user: ' . $orderData['user_id']);

                // Clear the session data
                session()->forget('pending_order_data');
                \Log::info('Cleared pending order data from session');

                DB::commit();
                \Log::info('Order creation transaction committed for order: ' . $order->id);

                return $order->id;
            } catch (\Exception $e) {
                DB::rollBack();
                \Log::error('Error creating order from payment: ' . $e->getMessage());
                return false;
            }
        } catch (\Exception $e) {
            \Log::error('Error in createOrderFromPayment: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display the user's order history.
     */
    public function history()
    {
        $orders = Order::where('user_id', Auth::id())
                      ->orderBy('created_at', 'desc')
                      ->paginate(10);

        return view('orders.history', compact('orders'));
    }

    /**
     * Display a specific order.
     */
    public function show($id)
    {
        $order = Order::where('user_id', Auth::id())
                     ->where('id', $id)
                     ->with('orderItems.product')
                     ->firstOrFail();

        return view('orders.show', compact('order'));
    }

    /**
     * Generate and download PDF receipt for an order.
     */
    public function receiptPdf($id)
    {
        // Require authentication for PDF receipts
        $order = Order::where('user_id', Auth::id())
                     ->where('id', $id)
                     ->with('orderItems.product', 'user')
                     ->firstOrFail();

        // Get payment information if available
        $payment = BakongPayment::where('order_id', $id)->first();

        // Generate PDF
        $pdf = Pdf::loadView('orders.receipt-pdf', compact('order', 'payment'));

        // Set paper size and orientation
        $pdf->setPaper('A4', 'portrait');

        // Set options for better rendering
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
            'defaultFont' => 'Arial'
        ]);

        // Return PDF download
        return $pdf->download('receipt-order-' . $order->id . '.pdf');
    }

    /**
     * Download PDF receipt using secure token (no authentication required).
     */
    public function downloadReceiptPdf($token)
    {
        // Find order by PDF token (no authentication required)
        $order = Order::where('pdf_token', $token)
                     ->with('orderItems.product', 'user')
                     ->firstOrFail();

        // Get payment information if available
        $payment = BakongPayment::where('order_id', $order->id)->first();

        // Generate PDF
        $pdf = Pdf::loadView('orders.receipt-pdf', compact('order', 'payment'));

        // Set paper size and orientation
        $pdf->setPaper('A4', 'portrait');

        // Set options for better rendering
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
            'defaultFont' => 'Arial'
        ]);

        // Return PDF download
        return $pdf->download('receipt-order-' . $order->id . '.pdf');
    }

    /**
     * Send Telegram notification for new order
     */
    private function sendTelegramNotification($order)
    {
        try {
            $botToken = env('TELEGRAM_BOT_TOKEN');
            $chatId = env('TELEGRAM_CHAT_ID');

            if (!$botToken || !$chatId) {
                Log::warning('Telegram bot token or chat ID not configured');
                return;
            }

            // Load order relationships
            $order->load(['user', 'orderItems.product']);

            // Format payment method display
            $paymentMethodDisplay = $order->payment_method === 'cash_on_delivery' ? 'Cash on Delivery' : 'Bakong KHQR';

            // Prepare message
            $message = "🛒 *New Order #{$order->id}*\n\n";
            $message .= "👤 *Customer:* {$order->user->name}\n";
            $message .= "📧 *Email:* {$order->user->email}\n";
            $message .= "📱 *Phone:* {$order->phone_number}\n";
            $message .= "📍 *Address:* {$order->shipping_address}\n";
            $message .= "💳 *Payment:* {$paymentMethodDisplay}\n";
            $message .= "📅 *Date:* " . $order->created_at->setTimezone('Asia/Phnom_Penh')->format('Y-m-d H:i:s') . " (Cambodia Time)\n\n";

            $message .= "🛍️ *Products:*\n";
            $totalItems = 0;
            foreach ($order->orderItems as $item) {
                $message .= "• {$item->product->name}\n";
                $message .= "  Qty: {$item->quantity} × \${$item->price} = \$" . number_format($item->quantity * $item->price, 2) . "\n";
                $totalItems += $item->quantity;
            }

            $message .= "\n💰 *Total Amount:* \$" . number_format($order->total_amount, 2) . "\n";
            $message .= "📦 *Total Items:* {$totalItems}\n";
            $message .= "🏷️ *Status:* " . ucfirst($order->status) . "\n\n";

            if ($order->payment_method === 'cash_on_delivery') {
                $message .= "💵 *Cash on Delivery* - Payment will be collected upon delivery\n";
            }

            $message .= "🔗 *Order Details:* " . route('admin.orders.show', $order->id);

            // Send the message
            $response = Http::post("https://api.telegram.org/bot{$botToken}/sendMessage", [
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => 'Markdown'
            ]);

            if ($response->successful()) {
                Log::info('Telegram notification sent successfully for order: ' . $order->id);
            } else {
                Log::error('Failed to send Telegram notification for order: ' . $order->id . '. Response: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('Telegram notification error for order ' . $order->id . ': ' . $e->getMessage());
        }
    }
}
