<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use App\Models\WarehouseInventory;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class WarehouseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create warehouses
        $warehouses = [
            [
                'name' => 'Main Distribution Center',
                'code' => 'MDC-001',
                'address' => '123 Industrial Blvd',
                'city' => 'Phnom Penh',
                'state' => 'Phnom Penh',
                'country' => 'Cambodia',
                'postal_code' => '12000',
                'latitude' => 11.5564,
                'longitude' => 104.9282,
                'phone' => '+855-23-123456',
                'email' => '<EMAIL>',
                'manager_name' => 'Sophea Chan',
                'is_active' => true,
                'is_default' => true,
                'capacity' => 10000,
                'operating_hours' => [
                    'monday' => ['open' => '08:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '18:00'],
                    'thursday' => ['open' => '08:00', 'close' => '18:00'],
                    'friday' => ['open' => '08:00', 'close' => '18:00'],
                    'saturday' => ['open' => '09:00', 'close' => '17:00'],
                    'sunday' => ['closed' => true]
                ]
            ],
            [
                'name' => 'North Regional Warehouse',
                'code' => 'NRW-002',
                'address' => '456 Commerce Street',
                'city' => 'Siem Reap',
                'state' => 'Siem Reap',
                'country' => 'Cambodia',
                'postal_code' => '17000',
                'latitude' => 13.3671,
                'longitude' => 103.8448,
                'phone' => '+855-63-789012',
                'email' => '<EMAIL>',
                'manager_name' => 'Dara Pich',
                'is_active' => true,
                'is_default' => false,
                'capacity' => 5000,
                'operating_hours' => [
                    'monday' => ['open' => '08:00', 'close' => '17:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '17:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '17:00'],
                    'thursday' => ['open' => '08:00', 'close' => '17:00'],
                    'friday' => ['open' => '08:00', 'close' => '17:00'],
                    'saturday' => ['open' => '09:00', 'close' => '16:00'],
                    'sunday' => ['closed' => true]
                ]
            ],
            [
                'name' => 'South Coast Distribution',
                'code' => 'SCD-003',
                'address' => '789 Port Road',
                'city' => 'Sihanoukville',
                'state' => 'Preah Sihanouk',
                'country' => 'Cambodia',
                'postal_code' => '18000',
                'latitude' => 10.6104,
                'longitude' => 103.5390,
                'phone' => '+855-34-345678',
                'email' => '<EMAIL>',
                'manager_name' => 'Maly Sok',
                'is_active' => true,
                'is_default' => false,
                'capacity' => 3000,
                'operating_hours' => [
                    'monday' => ['open' => '07:00', 'close' => '16:00'],
                    'tuesday' => ['open' => '07:00', 'close' => '16:00'],
                    'wednesday' => ['open' => '07:00', 'close' => '16:00'],
                    'thursday' => ['open' => '07:00', 'close' => '16:00'],
                    'friday' => ['open' => '07:00', 'close' => '16:00'],
                    'saturday' => ['open' => '08:00', 'close' => '15:00'],
                    'sunday' => ['closed' => true]
                ]
            ]
        ];

        foreach ($warehouses as $warehouseData) {
            Warehouse::create($warehouseData);
        }

        // Create sample inventory for existing products
        $warehouses = Warehouse::all();
        $products = Product::take(20)->get(); // Get first 20 products

        foreach ($warehouses as $warehouse) {
            foreach ($products as $product) {
                // Random stock levels with some low stock items
                $quantity = rand(0, 100);
                $reorderLevel = rand(5, 15);

                WarehouseInventory::create([
                    'warehouse_id' => $warehouse->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'reserved_quantity' => rand(0, min(5, $quantity)),
                    'reorder_level' => $reorderLevel,
                    'max_stock_level' => $reorderLevel * 10,
                    'cost_price' => $product->price * 0.7, // 30% markup
                    'location_code' => chr(65 + rand(0, 4)) . rand(1, 10) . '-' . chr(65 + rand(0, 4)) . rand(1, 5),
                    'last_restocked_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }

        $this->command->info('Warehouses and inventory created successfully!');
    }
}
