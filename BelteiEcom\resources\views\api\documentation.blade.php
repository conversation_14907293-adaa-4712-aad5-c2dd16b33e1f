<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BelteiEcom Partner API Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
            margin-bottom: 3rem;
            border-radius: 15px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
        }

        .section h3 {
            color: #333;
            margin: 2rem 0 1rem 0;
            font-size: 1.5rem;
        }

        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }

        .method {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 5px;
            font-weight: bold;
            font-size: 0.8rem;
            margin-right: 1rem;
        }

        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            overflow-x: auto;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }

        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .feature-card i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .toc {
            background: #e3f2fd;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .toc h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }

        .toc ul {
            list-style: none;
            padding-left: 0;
        }

        .toc li {
            margin-bottom: 0.5rem;
        }

        .toc a {
            color: #1976d2;
            text-decoration: none;
            font-weight: 500;
        }

        .toc a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 1rem;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-code"></i> BelteiEcom Partner API</h1>
            <p>Integrate our products into your website and start selling today!</p>
            <div style="margin-top: 2rem;">
                <a href="#getting-started" class="btn">Get Started</a>
                <a href="{{ route('api.partner-application') }}" class="btn" style="margin-left: 1rem;">Become a Partner</a>
            </div>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h3><i class="fas fa-list"></i> Table of Contents</h3>
            <ul>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#getting-started">Getting Started</a></li>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#products-api">Products API</a></li>
                <li><a href="#orders-api">Orders API</a></li>
                <li><a href="#webhooks">Webhooks</a></li>
                <li><a href="#examples">Code Examples</a></li>
            </ul>
        </div>

        <!-- Overview -->
        <div class="section" id="overview">
            <h2><i class="fas fa-info-circle"></i> Overview</h2>
            <p>The BelteiEcom Partner API allows businesses to integrate our product catalog into their websites and sell our products with their own pricing. This is perfect for:</p>
            
            <div class="grid">
                <div class="feature-card">
                    <i class="fas fa-store"></i>
                    <h3>E-commerce Stores</h3>
                    <p>Add our products to your existing online store</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-handshake"></i>
                    <h3>Resellers</h3>
                    <p>Become an authorized reseller with API access</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-shipping-fast"></i>
                    <h3>Dropshipping</h3>
                    <p>We handle fulfillment while you focus on sales</p>
                </div>
            </div>

            <div class="highlight">
                <strong>How it works:</strong> You set your own prices, we handle inventory and shipping. You earn commission on every sale!
            </div>
        </div>

        <!-- Getting Started -->
        <div class="section" id="getting-started">
            <h2><i class="fas fa-rocket"></i> Getting Started</h2>
            
            <h3>1. Become a Partner</h3>
            <p>Apply to become an authorized partner to get API access.</p>
            
            <h3>2. Get API Credentials</h3>
            <p>Once approved, you'll receive:</p>
            <ul>
                <li><strong>API Key:</strong> Your unique identifier</li>
                <li><strong>API Secret:</strong> For secure authentication</li>
                <li><strong>Commission Rate:</strong> Your earnings percentage</li>
            </ul>

            <h3>3. Base URL</h3>
            <div class="code-block">
https://{{ request()->getHost() }}/api/v1/partner/
            </div>

            <h3>4. Test Your Integration</h3>
            <div class="code-block">
curl -X GET "{{ url('/api/v1/public/status') }}" \
  -H "Accept: application/json"
            </div>
        </div>

        <!-- Authentication -->
        <div class="section" id="authentication">
            <h2><i class="fas fa-lock"></i> Authentication</h2>
            
            <p>All API requests require authentication using your API key in the header:</p>
            
            <div class="code-block">
X-API-Key: your_api_key_here
            </div>

            <h3>Rate Limits</h3>
            <ul>
                <li><strong>Basic Tier:</strong> 60 requests/minute, 1,000 requests/hour</li>
                <li><strong>Premium Tier:</strong> 120 requests/minute, 5,000 requests/hour</li>
                <li><strong>Enterprise Tier:</strong> 300 requests/minute, 20,000 requests/hour</li>
            </ul>
        </div>

        <!-- Products API -->
        <div class="section" id="products-api">
            <h2><i class="fas fa-box"></i> Products API</h2>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/products</strong> - Get all products
            </div>
            <div class="code-block">
curl -X GET "{{ url('/api/v1/partner/products') }}" \
  -H "X-API-Key: your_api_key" \
  -H "Accept: application/json"
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/products/{id}</strong> - Get specific product
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/categories</strong> - Get all categories
            </div>

            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/inventory/check</strong> - Check inventory levels
            </div>
        </div>

        <!-- Orders API -->
        <div class="section" id="orders-api">
            <h2><i class="fas fa-shopping-cart"></i> Orders API</h2>

            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/orders</strong> - Create new order
            </div>
            <div class="code-block">
{
  "partner_order_id": "YOUR-ORDER-123",
  "customer": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "shipping_address": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "postal_code": "12345",
    "country": "US"
  },
  "items": [
    {
      "product_id": 1,
      "quantity": 2,
      "partner_price": 29.99
    }
  ],
  "webhook_url": "https://yoursite.com/webhook"
}
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/orders</strong> - Get your orders
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <strong>/orders/{id}</strong> - Get specific order
            </div>

            <div class="endpoint">
                <span class="method post">POST</span>
                <strong>/orders/{id}/cancel</strong> - Cancel order
            </div>
        </div>

        <!-- Webhooks -->
        <div class="section" id="webhooks">
            <h2><i class="fas fa-bell"></i> Webhooks</h2>
            
            <p>Receive real-time notifications about order updates:</p>
            
            <h3>Events</h3>
            <ul>
                <li><strong>order.created</strong> - New order placed</li>
                <li><strong>order.status_updated</strong> - Order status changed</li>
                <li><strong>order.shipped</strong> - Order shipped with tracking</li>
                <li><strong>order.delivered</strong> - Order delivered</li>
            </ul>

            <div class="code-block">
{
  "event": "order.status_updated",
  "data": {
    "external_order_id": "BEC-ABC12345",
    "partner_order_id": "YOUR-ORDER-123",
    "old_status": "confirmed",
    "new_status": "shipped",
    "tracking_number": "1Z999AA1234567890"
  }
}
            </div>
        </div>

        <!-- Examples -->
        <div class="section" id="examples">
            <h2><i class="fas fa-code"></i> Code Examples</h2>
            
            <h3>PHP Example</h3>
            <div class="code-block">
$apiKey = 'your_api_key_here';
$baseUrl = '{{ url("/api/v1/partner") }}';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/products');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-API-Key: ' . $apiKey,
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$products = json_decode($response, true);
curl_close($ch);
            </div>

            <h3>JavaScript Example</h3>
            <div class="code-block">
const apiKey = 'your_api_key_here';
const baseUrl = '{{ url("/api/v1/partner") }}';

fetch(`${baseUrl}/products`, {
  headers: {
    'X-API-Key': apiKey,
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));
            </div>
        </div>

        <div class="section">
            <h2><i class="fas fa-question-circle"></i> Need Help?</h2>
            <p>Ready to get started? <a href="{{ route('api.partner-application') }}" class="btn">Apply to Become a Partner</a></p>
            <p style="margin-top: 1rem;">Questions? Contact our API support team at <strong><EMAIL></strong></p>
        </div>
    </div>
</body>
</html>
