<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Remove auth middleware to allow guests to view the home page
        // $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Get featured products (latest 8 products)
        $products = Product::where('stock', '>', 0)
                          ->orderBy('created_at', 'desc')
                          ->take(8)
                          ->get();

        // Get featured categories for the home page
        $featuredCategories = Category::whereIn('name', ['Electronics', 'Home & Living'])
                                    ->get(['id', 'slug', 'name']);

        return view('home', compact('products', 'featuredCategories'));
    }
}
