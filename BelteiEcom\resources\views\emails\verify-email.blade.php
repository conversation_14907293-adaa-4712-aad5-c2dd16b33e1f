<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - {{ $appName }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .content {
            padding: 50px 40px;
            text-align: center;
        }
        
        .welcome-message {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .user-name {
            color: #667eea;
            font-weight: 700;
        }
        
        .description {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.8;
        }
        
        .verification-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #dee2e6;
        }
        
        .verification-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .verification-text {
            font-size: 1.2rem;
            color: #495057;
            margin-bottom: 30px;
            font-weight: 500;
        }
        
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .verify-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
            text-decoration: none;
            color: white !important;
        }
        
        .security-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }
        
        .security-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .security-text {
            color: #856404;
            font-size: 0.95rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .feature-text {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer-logo {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .footer-text {
            opacity: 0.8;
            margin-bottom: 20px;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .social-link {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            line-height: 40px;
            text-align: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .copyright {
            font-size: 0.9rem;
            opacity: 0.6;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .welcome-message {
                font-size: 1.5rem;
            }
            
            .verify-button {
                padding: 15px 30px;
                font-size: 1.1rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛒 {{ $appName }}</div>
            <div class="header-subtitle">Your Premium Ecommerce Experience</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <h1 class="welcome-message">
                Welcome, <span class="user-name">{{ $user->name }}</span>! 🎉
            </h1>
            
            <p class="description">
                Thank you for joining {{ $appName }}! We're thrilled to have you as part of our community. 
                To unlock your full shopping experience and access all our premium features, 
                please verify your email address.
            </p>
            
            <!-- Verification Section -->
            <div class="verification-section">
                <div class="verification-icon">🔐</div>
                <div class="verification-text">
                    Click the button below to verify your email address
                </div>
                <a href="{{ $verificationUrl }}" class="verify-button">
                    ✨ Verify My Email Address
                </a>
            </div>
            
            <!-- Features -->
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🛍️</div>
                    <div class="feature-title">Shop Premium Products</div>
                    <div class="feature-text">Access our curated collection</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🚚</div>
                    <div class="feature-title">Fast Delivery</div>
                    <div class="feature-text">Quick & reliable shipping</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">💳</div>
                    <div class="feature-title">Secure Payments</div>
                    <div class="feature-text">Safe & encrypted transactions</div>
                </div>
            </div>
            
            <!-- Security Info -->
            <div class="security-info">
                <div class="security-title">
                    🔒 Security Information
                </div>
                <div class="security-text">
                    • This verification link will expire in {{ $expireMinutes }} minutes<br>
                    • If you didn't create this account, please ignore this email<br>
                    • Never share this email with anyone for security reasons
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-logo">{{ $appName }}</div>
            <div class="footer-text">
                Your trusted ecommerce partner for premium shopping experiences
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link">📘</a>
                <a href="#" class="social-link">📷</a>
                <a href="#" class="social-link">🐦</a>
                <a href="#" class="social-link">📧</a>
            </div>
            
            <div class="copyright">
                © {{ date('Y') }} {{ $appName }}. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
