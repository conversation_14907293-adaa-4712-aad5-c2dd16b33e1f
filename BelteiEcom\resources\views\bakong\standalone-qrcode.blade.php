<!DOCTYPE html>
<html lang="en">

<head>
  <title>KHQR Payment - BelteiEcom</title>
  <meta property="og:title" content="KHQR Payment - BelteiEcom" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta charset="utf-8" />
  <meta property="twitter:card" content="summary_large_image" />

  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }

    .container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }

    .qr-card {
      width: 280px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }

    .qr-header {
      background-color: #e30613;
      color: white;
      padding: 10px 15px;
      font-weight: bold;
      text-align: center;
      position: relative;
    }

    .qr-header::after {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 20px 20px 0;
      border-color: transparent white transparent transparent;
    }

    .qr-content {
      padding: 15px;
    }

    .merchant-info {
      border-bottom: 1px dotted #ddd;
      padding-bottom: 15px;
      margin-bottom: 15px;
    }

    .merchant-name {
      font-size: 14px;
      color: #333;
      margin-bottom: 5px;
    }

    .amount {
      font-size: 24px;
      font-weight: bold;
      color: #000;
    }

    .qr-code-container {
      display: flex;
      justify-content: center;
      margin: 15px 0;
      position: relative;
    }

    .qr-code {
      width: 190px;
      height: 190px;
    }

    .qr-logo {
      position: absolute;
      width: 40px;
      height: 40px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: white;
      border-radius: 50%;
      padding: 5px;
    }

    .payment-methods {
      text-align: center;
      margin-top: 15px;
    }

    .payment-methods img {
      max-width: 200px;
      margin: 0 auto;
    }

    .status-message {
      text-align: center;
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 14px;
      display: none;
    }

    .status-pending {
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeeba;
      display: block;
    }

    .status-success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .timer {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 15px;
      padding: 5px 10px;
      font-size: 14px;
      font-weight: bold;
      display: flex;
      align-items: center;
    }

    .timer-icon {
      width: 16px;
      height: 16px;
      margin-right: 5px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="qr-card">
      <div class="timer">
        <img src="{{ asset('bakong/img/unnamed.png') }}" alt="Timer" class="timer-icon">
        <span id="countdown">2:58</span>
      </div>

      <div class="qr-header">
        KHQR
      </div>

      <div class="qr-content">
        <div class="merchant-info">
          <div class="merchant-name">BelteiEcom - By CamboTeam</div>
          <div class="amount">${{ $amount }}</div>
        </div>

        <div class="qr-code-container">
          <img class="qr-code" src="https://api.qrserver.com/v1/create-qr-code/?size=190x190&data={{ $qrData }}" alt="QR Code">
          <img class="qr-logo" src="{{ asset('bakong/img/unnamed.png') }}" alt="KHQR Logo">
        </div>

        <div class="payment-methods">
          <img src="{{ asset('bakong/img/payment_icons-cd5e952dde3b886dea1fd1b983d43ce372f1692dec253808ec654096d2feb701-200h.png') }}" alt="Payment Methods">
        </div>

        <div id="statusMessage" class="status-message status-pending">
          Waiting for payment... Please scan the QR code with your Bakong app.
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Get the status message element
    const statusMessage = document.getElementById("statusMessage");

    // Function to update status message
    function updateStatusMessage(message, type) {
      statusMessage.textContent = message;
      statusMessage.className = `status-message status-${type}`;
    }

    // Function to check transaction status directly with Bakong API (like original qrcode.php)
    async function checkTransactionStatus() {
      const md5FromUrl = "{{ $md5 }}";
      console.log("Checking transaction status with Bakong API for MD5:", md5FromUrl);

      // Update status message to checking
      updateStatusMessage("Checking payment status with Bakong...", "pending");

      try {
        // Call Bakong API directly like the original qrcode.php
        const response = await fetch('https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiMDYwYjNkYTFjYTJiNDNhNiJ9LCJpYXQiOjE3NDc3NDgzMzcsImV4cCI6MTc1NTUyNDMzN30.nv8PasXDB64Mos3WPu3L62Tfo2_LurtjAcvN4AuFlsY',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            md5: md5FromUrl
          })
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log("Bakong API response:", responseData);

          // Check if Bakong returned success (responseCode === 0)
          if (responseData.responseCode === 0) {
            // Check if payment is to the correct account
            if (responseData.data.toAccountId === "chhunlichhean_kun@wing") {
              console.log("Payment successful! Bakong confirmed transaction.");

              // Update status message to success
              updateStatusMessage("Payment confirmed by Bakong! Processing order...", "success");

              // Now call our backend to complete the order
              try {
                const orderResponse = await fetch('/bakong/check-transaction-by-order', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                  },
                  body: JSON.stringify({
                    order: md5FromUrl,
                    bakong_response: responseData  // Pass the real Bakong response
                  })
                });

                if (orderResponse.ok) {
                  const orderData = await orderResponse.json();
                  console.log("Order creation response:", orderData);

                  // Redirect to success page using success token
                  setTimeout(() => {
                    if (orderData.order_details && orderData.order_details.success_token) {
                      window.location.href = '/orders/success/' + orderData.order_details.success_token;
                    } else {
                      window.location.href = '/orders/history';
                    }
                  }, 1000);
                } else {
                  console.error('Failed to create order:', orderResponse.status);
                  updateStatusMessage("Payment confirmed but order creation failed. Please contact support.", "error");
                }
              } catch (orderError) {
                console.error('Error creating order:', orderError);
                updateStatusMessage("Payment confirmed but order creation failed. Please contact support.", "error");
              }
            } else {
              console.error('Payment to wrong account:', responseData.data.toAccountId);
              updateStatusMessage("Payment detected but to wrong account. Please contact support.", "error");
            }
          } else {
            // Payment not yet completed, check again
            console.log("Payment not yet completed. Bakong responseCode:", responseData.responseCode);
            updateStatusMessage("Waiting for payment... Please scan the QR code with your Bakong app.", "pending");
            setTimeout(checkTransactionStatus, 2000);
          }
        } else {
          console.error('Failed to fetch from Bakong API:', response.status);
          updateStatusMessage("Error checking with Bakong API. Retrying...", "error");
          setTimeout(checkTransactionStatus, 2000);
        }
      } catch (error) {
        console.error('Error during Bakong API call:', error);
        updateStatusMessage("Network error connecting to Bakong. Retrying...", "error");
        setTimeout(checkTransactionStatus, 2000);
      }
    }

    // Start checking for payment every 2 seconds
    checkTransactionStatus();

    // Set the countdown duration in seconds
    let countdownDuration = 180; // 3 minutes * 60 seconds

    // Get the countdown element
    const countdownElement = document.getElementById("countdown");

    // Update the countdown every second
    const countdownInterval = setInterval(function () {
      const minutes = Math.floor(countdownDuration / 60);
      const seconds = countdownDuration % 60;

      // Display the countdown in the format MM:SS
      countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

      // Decrease the countdown duration
      countdownDuration--;

      // Check if the countdown has reached 0
      if (countdownDuration < 0) {
        // Redirect to checkout page when the countdown reaches 0
        window.location.href = '/orders/checkout?error=expired';

        // Clear the interval to stop the countdown
        clearInterval(countdownInterval);
      }
    }, 1000); // Update every 1000 milliseconds (1 second)
  });
</script>

</html>
