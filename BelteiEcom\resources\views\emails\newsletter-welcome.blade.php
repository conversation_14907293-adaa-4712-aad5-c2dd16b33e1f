<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{ config('app.name') }} Newsletter!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .content {
            padding: 50px 40px;
            text-align: center;
        }
        
        .welcome-message {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .description {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.8;
        }
        
        .benefits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .benefit {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .benefit:hover {
            border-color: #00b894;
            transform: translateY(-5px);
        }
        
        .benefit-icon {
            font-size: 2.5rem;
            color: #00b894;
            margin-bottom: 15px;
        }
        
        .benefit-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
        
        .benefit-text {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #dee2e6;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
            text-decoration: none;
            color: white !important;
        }
        
        .unsubscribe-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        
        .unsubscribe-text {
            color: #856404;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .unsubscribe-link {
            color: #856404;
            text-decoration: underline;
            font-weight: 600;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer-logo {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .footer-text {
            opacity: 0.8;
            margin-bottom: 20px;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .social-link {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            line-height: 40px;
            text-align: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .copyright {
            font-size: 0.9rem;
            opacity: 0.6;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .welcome-message {
                font-size: 1.5rem;
            }
            
            .cta-button {
                padding: 15px 30px;
                font-size: 1.1rem;
            }
            
            .benefits {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🎉 {{ config('app.name') }}</div>
            <div class="header-subtitle">Welcome to Our Newsletter Community!</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <h1 class="welcome-message">
                Welcome to the VIP Club! 🌟
            </h1>
            
            <p class="description">
                Thank you for subscribing to {{ config('app.name') }} newsletter! 
                You're now part of our exclusive community and will be the first to know about 
                amazing deals, new products, and special promotions.
            </p>
            
            <!-- Benefits -->
            <div class="benefits">
                <div class="benefit">
                    <div class="benefit-icon">🎁</div>
                    <div class="benefit-title">Exclusive Deals</div>
                    <div class="benefit-text">Get access to subscriber-only discounts</div>
                </div>
                <div class="benefit">
                    <div class="benefit-icon">🚀</div>
                    <div class="benefit-title">Early Access</div>
                    <div class="benefit-text">Be first to see new products</div>
                </div>
                <div class="benefit">
                    <div class="benefit-icon">💡</div>
                    <div class="benefit-title">Shopping Tips</div>
                    <div class="benefit-text">Insider tips and recommendations</div>
                </div>
            </div>
            
            <!-- Call to Action -->
            <div class="cta-section">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">Ready to Start Shopping?</h3>
                <a href="{{ url('/') }}" class="cta-button">
                    🛍️ Explore Our Store
                </a>
                <a href="{{ url('/products') }}" class="cta-button">
                    🔥 View Hot Deals
                </a>
            </div>
            
            <!-- Unsubscribe Section -->
            <div class="unsubscribe-section">
                <div class="unsubscribe-text">
                    Don't want to receive these emails anymore?
                </div>
                <a href="{{ route('newsletter.unsubscribe', $newsletter->subscription_token) }}" class="unsubscribe-link">
                    Unsubscribe here
                </a>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-logo">{{ config('app.name') }}</div>
            <div class="footer-text">
                Your trusted ecommerce partner for premium shopping experiences
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link">📘</a>
                <a href="#" class="social-link">📷</a>
                <a href="#" class="social-link">🐦</a>
                <a href="#" class="social-link">📧</a>
            </div>
            
            <div class="copyright">
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
