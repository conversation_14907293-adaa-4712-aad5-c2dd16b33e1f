<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductRecommendation;
use App\Services\RecommendationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RecommendationController extends Controller
{
    protected $recommendationService;

    public function __construct(RecommendationService $recommendationService)
    {
        $this->recommendationService = $recommendationService;
    }

    /**
     * Get recommendations for a specific product.
     */
    public function getProductRecommendations(Request $request, $productId)
    {
        $type = $request->get('type', 'mixed'); // mixed, collaborative, content_based, similar, trending
        $limit = $request->get('limit', 6);

        if ($type === 'mixed') {
            $recommendations = ProductRecommendation::getMixedRecommendations($productId, $limit);
        } else {
            $recommendations = ProductRecommendation::getRecommendations($productId, $type, $limit);
        }

        $products = $recommendations->map(function ($rec) {
            return [
                'id' => $rec->recommendedProduct->id,
                'name' => $rec->recommendedProduct->name,
                'slug' => $rec->recommendedProduct->slug,
                'price' => $rec->recommendedProduct->price,
                'image' => $rec->recommendedProduct->image ? asset('storage/' . $rec->recommendedProduct->image) : null,
                'rating' => $rec->recommendedProduct->average_rating,
                'review_count' => $rec->recommendedProduct->review_count,
                'confidence' => $rec->confidence_score,
                'reason' => $rec->metadata['reason'] ?? 'Recommended for you',
                'type' => $rec->recommendation_type
            ];
        });

        return response()->json([
            'success' => true,
            'recommendations' => $products,
            'total' => $products->count()
        ]);
    }

    /**
     * Get personalized recommendations for the authenticated user.
     */
    public function getPersonalizedRecommendations(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required for personalized recommendations'
            ], 401);
        }

        $limit = $request->get('limit', 12);
        $recommendations = $this->recommendationService->getPersonalizedRecommendations(Auth::id(), $limit);

        $products = $recommendations->map(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'image' => $product->image ? asset('storage/' . $product->image) : null,
                'rating' => $product->average_rating,
                'review_count' => $product->review_count,
                'category' => $product->category->name ?? 'Uncategorized'
            ];
        });

        return response()->json([
            'success' => true,
            'recommendations' => $products,
            'total' => $products->count()
        ]);
    }

    /**
     * Record a product view for recommendation tracking.
     */
    public function recordView(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'view_duration' => 'nullable|integer|min:0'
        ]);

        $userId = Auth::id();
        $viewDuration = $request->get('view_duration', 0);

        $this->recommendationService->recordProductView(
            $request->product_id,
            $userId,
            $viewDuration
        );

        return response()->json([
            'success' => true,
            'message' => 'View recorded successfully'
        ]);
    }

    /**
     * Generate recommendations for a product (admin only).
     */
    public function generateRecommendations(Request $request, $productId)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => 'Admin access required'
            ], 403);
        }

        $result = $this->recommendationService->generateRecommendations($productId);

        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'Recommendations generated successfully'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to generate recommendations'
        ], 500);
    }

    /**
     * Get trending products.
     */
    public function getTrendingProducts(Request $request)
    {
        $limit = $request->get('limit', 10);

        // Get products with high recent activity
        $trending = Product::select('products.*')
            ->selectRaw('
                (
                    COALESCE(recent_views.view_count, 0) * 0.4 +
                    COALESCE(recent_orders.order_count, 0) * 0.3 +
                    COALESCE(recent_reviews.review_count, 0) * 0.2 +
                    COALESCE(recent_wishlists.wishlist_count, 0) * 0.1
                ) as trend_score
            ')
            ->withCount('reviews')
            ->withAvg('reviews', 'rating')
            ->leftJoin(\DB::raw('(
                SELECT product_id, COUNT(*) as view_count
                FROM product_views
                WHERE viewed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY product_id
            ) as recent_views'), 'products.id', '=', 'recent_views.product_id')
            ->leftJoin(\DB::raw('(
                SELECT oi.product_id, COUNT(*) as order_count
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND o.status = "completed"
                GROUP BY oi.product_id
            ) as recent_orders'), 'products.id', '=', 'recent_orders.product_id')
            ->leftJoin(\DB::raw('(
                SELECT product_id, COUNT(*) as review_count
                FROM reviews
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY product_id
            ) as recent_reviews'), 'products.id', '=', 'recent_reviews.product_id')
            ->leftJoin(\DB::raw('(
                SELECT product_id, COUNT(*) as wishlist_count
                FROM wishlists
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY product_id
            ) as recent_wishlists'), 'products.id', '=', 'recent_wishlists.product_id')
            ->where('products.stock', '>', 0)
            ->orderByDesc('trend_score')
            ->limit($limit)
            ->get();

        $products = $trending->map(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'image' => $product->image ? asset('storage/' . $product->image) : null,
                'rating' => $product->average_rating,
                'review_count' => $product->review_count,
                'trend_score' => round($product->trend_score, 2),
                'category' => $product->category->name ?? 'Uncategorized'
            ];
        });

        return response()->json([
            'success' => true,
            'trending' => $products,
            'total' => $products->count()
        ]);
    }
}
