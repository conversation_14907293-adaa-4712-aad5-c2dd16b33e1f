@extends('admin.layouts.app')

@section('title', 'Agent Dashboard - Live Chat')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-headset text-primary"></i>
            Agent Dashboard
        </h1>
        <div class="btn-group">
            <a href="{{ route('admin.chat.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Overview
            </a>
            <button class="btn btn-info btn-sm" onclick="toggleNotifications()">
                <i class="fas fa-bell"></i> Notifications
            </button>
        </div>
    </div>

    <!-- Agent Status Card -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-circle"></i>
                        Your Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-lg mb-3">
                            <div class="bg-{{ $agentStatus->status_color }} rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                        </div>
                        <h5>{{ Auth::user()->name }}</h5>
                        <span class="badge badge-{{ $agentStatus->status_color }} badge-lg">
                            <i class="{{ $agentStatus->status_icon }}"></i>
                            {{ ucfirst($agentStatus->status) }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status-select">Change Status:</label>
                        <select id="status-select" class="form-control" onchange="updateStatus()">
                            <option value="online" {{ $agentStatus->status === 'online' ? 'selected' : '' }}>Online</option>
                            <option value="away" {{ $agentStatus->status === 'away' ? 'selected' : '' }}>Away</option>
                            <option value="busy" {{ $agentStatus->status === 'busy' ? 'selected' : '' }}>Busy</option>
                            <option value="offline" {{ $agentStatus->status === 'offline' ? 'selected' : '' }}>Offline</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status-message">Status Message:</label>
                        <input type="text" id="status-message" class="form-control" 
                               value="{{ $agentStatus->status_message }}" 
                               placeholder="Optional status message..."
                               onchange="updateStatus()">
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ $agentStatus->current_conversations }}</h4>
                            <small class="text-muted">Active Chats</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ $agentStatus->max_conversations }}</h4>
                            <small class="text-muted">Max Capacity</small>
                        </div>
                    </div>
                    
                    <div class="progress mt-3">
                        <div class="progress-bar bg-{{ $agentStatus->workload_percentage > 80 ? 'danger' : ($agentStatus->workload_percentage > 60 ? 'warning' : 'success') }}" 
                             style="width: {{ $agentStatus->workload_percentage }}%">
                            {{ $agentStatus->workload_percentage }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Active Conversations
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $activeConversations->count() }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-comment-dots fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Waiting Queue
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $waitingConversations->count() }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-success btn-block" onclick="takeNextWaiting()">
                                <i class="fas fa-hand-paper"></i> Take Next Waiting
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ route('admin.chat.conversations') }}" class="btn btn-info btn-block">
                                <i class="fas fa-list"></i> View All Conversations
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Active Conversations -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-comment-dots"></i>
                        Your Active Conversations ({{ $activeConversations->count() }})
                    </h6>
                    <button class="btn btn-success btn-sm" onclick="refreshActiveChats()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    @if($activeConversations->count() > 0)
                        <div class="row">
                            @foreach($activeConversations as $conversation)
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-primary">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">{{ $conversation->customer_name }}</h6>
                                                <span class="badge badge-{{ $conversation->priority_color }}">
                                                    {{ ucfirst($conversation->priority) }}
                                                </span>
                                            </div>
                                            <p class="card-text">
                                                <small class="text-muted">{{ $conversation->subject ?: 'General Inquiry' }}</small>
                                            </p>
                                            @if($conversation->latestMessage)
                                                <p class="card-text">
                                                    <small>{{ Str::limit($conversation->latestMessage->message, 60) }}</small>
                                                </p>
                                            @endif
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">{{ $conversation->last_activity_at->diffForHumans() }}</small>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.chat.conversation', $conversation->id) }}" 
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-comments"></i> Chat
                                                    </a>
                                                    <button class="btn btn-secondary btn-sm" 
                                                            onclick="closeConversation({{ $conversation->id }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No active conversations</h5>
                            <p class="text-muted">You don't have any active chats right now.</p>
                            @if($waitingConversations->count() > 0)
                                <button class="btn btn-success" onclick="takeNextWaiting()">
                                    <i class="fas fa-hand-paper"></i> Take Next Waiting Customer
                                </button>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Waiting Queue -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock"></i>
                        Waiting Queue ({{ $waitingConversations->count() }})
                    </h6>
                </div>
                <div class="card-body">
                    @if($waitingConversations->count() > 0)
                        @foreach($waitingConversations->take(5) as $conversation)
                            <div class="d-flex align-items-center mb-3 p-2 border rounded">
                                <div class="avatar-sm mr-2">
                                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <strong class="small">{{ $conversation->customer_name }}</strong>
                                        <span class="badge badge-{{ $conversation->priority_color }} badge-sm">
                                            {{ ucfirst($conversation->priority) }}
                                        </span>
                                    </div>
                                    <small class="text-muted">{{ $conversation->created_at->diffForHumans() }}</small>
                                    <div class="mt-1">
                                        <button class="btn btn-success btn-xs" 
                                                onclick="takeConversation({{ $conversation->id }})">
                                            <i class="fas fa-hand-paper"></i> Take
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        
                        @if($waitingConversations->count() > 5)
                            <div class="text-center">
                                <small class="text-muted">{{ $waitingConversations->count() - 5 }} more waiting...</small>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">No customers waiting!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.badge-sm {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.15rem;
}

.avatar-lg {
    flex-shrink: 0;
}

.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
</style>

<script>
// Auto-refresh every 15 seconds
setInterval(function() {
    refreshActiveChats();
    checkNewMessages();
}, 15000);

function updateStatus() {
    const status = document.getElementById('status-select').value;
    const statusMessage = document.getElementById('status-message').value;
    
    fetch('{{ route("admin.chat.update-status") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: status,
            status_message: statusMessage
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI to reflect new status
            location.reload();
        } else {
            alert('Error updating status: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function takeConversation(conversationId) {
    fetch(`/admin/chat/conversation/${conversationId}/take`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect;
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while taking the conversation.');
    });
}

function takeNextWaiting() {
    // Find the first waiting conversation and take it
    const waitingConversations = @json($waitingConversations->pluck('id'));
    if (waitingConversations.length > 0) {
        takeConversation(waitingConversations[0]);
    } else {
        alert('No conversations waiting!');
    }
}

function closeConversation(conversationId) {
    if (confirm('Are you sure you want to close this conversation?')) {
        const reason = prompt('Reason for closing (optional):');
        
        fetch(`/admin/chat/conversation/${conversationId}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while closing the conversation.');
        });
    }
}

function refreshActiveChats() {
    // Implement AJAX refresh if needed
    // For now, just reload the page
    location.reload();
}

function checkNewMessages() {
    // Check for new messages and show notifications
    fetch('{{ route("admin.chat.new-messages") }}')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.count > 0) {
            // Show notification or update UI
            console.log(`${data.count} new messages`);
        }
    })
    .catch(error => {
        console.error('Error checking messages:', error);
    });
}

function toggleNotifications() {
    // Toggle browser notifications
    if (Notification.permission === 'granted') {
        alert('Notifications are already enabled!');
    } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                new Notification('Chat notifications enabled!', {
                    body: 'You will now receive notifications for new messages.',
                    icon: '/favicon.ico'
                });
            }
        });
    } else {
        alert('Notifications are blocked. Please enable them in your browser settings.');
    }
}
</script>
@endsection
