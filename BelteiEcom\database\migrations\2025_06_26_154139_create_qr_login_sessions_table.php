<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qr_login_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('token', 64)->unique(); // Unique token for QR code
            $table->string('one_time_code', 8)->unique()->nullable(); // Alternative 8-digit code
            $table->enum('status', ['pending', 'scanned', 'authenticated', 'expired', 'cancelled'])->default('pending');
            $table->unsignedBigInteger('user_id')->nullable(); // User who scanned the QR code
            $table->string('device_info')->nullable(); // Device information
            $table->string('ip_address')->nullable(); // IP address of the device requesting login
            $table->string('user_agent')->nullable(); // User agent of the device
            $table->timestamp('expires_at'); // When the QR code expires
            $table->timestamp('scanned_at')->nullable(); // When the QR code was scanned
            $table->timestamp('authenticated_at')->nullable(); // When authentication was completed
            $table->timestamps();

            // Indexes for performance
            $table->index(['token', 'status']);
            $table->index(['one_time_code', 'status']);
            $table->index(['expires_at', 'status']);
            $table->index('user_id');

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qr_login_sessions');
    }
};
