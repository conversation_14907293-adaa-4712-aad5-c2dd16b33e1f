<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_recommendations', function (Blueprint $table) {
            // Add the missing constraints and indexes
            $table->unique(['product_id', 'recommended_product_id', 'recommendation_type'], 'unique_product_recommendation');
            $table->index(['product_id', 'recommendation_type', 'confidence_score'], 'idx_product_rec_type_score');
            $table->index(['recommended_product_id'], 'idx_recommended_product');
            $table->index('calculated_at', 'idx_calculated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_recommendations', function (Blueprint $table) {
            $table->dropUnique('unique_product_recommendation');
            $table->dropIndex('idx_product_rec_type_score');
            $table->dropIndex('idx_recommended_product');
            $table->dropIndex('idx_calculated_at');
        });
    }
};
