<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify the status enum to include 'pending_payment'
        DB::statement("ALTER TABLE orders MODIFY COLUMN status ENUM('pending_payment', 'new', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'new'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE orders MODIFY COLUMN status ENUM('new', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'new'");
    }
};
