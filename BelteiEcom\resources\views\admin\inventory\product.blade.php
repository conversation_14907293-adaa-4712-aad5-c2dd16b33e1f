@extends('admin.layouts.app')

@section('title', 'Product Inventory - ' . $product->name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.inventory.index') }}">Inventory</a>
                    </li>
                    <li class="breadcrumb-item active">{{ $product->name }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-box text-primary"></i>
                {{ $product->name }}
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.inventory.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back
            </a>
            <a href="{{ route('admin.inventory.add-stock-form', $product->id) }}" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i> Add Stock
            </a>
            <a href="{{ route('admin.inventory.transfer-form', $product->id) }}" class="btn btn-info btn-sm">
                <i class="fas fa-exchange-alt"></i> Transfer
            </a>
        </div>
    </div>

    <!-- Product Info -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Product Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            @if($product->image)
                                <img src="{{ asset('storage/' . $product->image) }}" 
                                     alt="{{ $product->name }}" 
                                     class="img-fluid rounded">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-9">
                            <h5>{{ $product->name }}</h5>
                            <p><strong>SKU:</strong> {{ $product->sku }}</p>
                            <p><strong>Category:</strong> {{ $product->category->name ?? 'N/A' }}</p>
                            <p><strong>Price:</strong> ${{ number_format($product->price, 2) }}</p>
                            <p><strong>Description:</strong> {{ Str::limit($product->description, 200) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">Inventory Summary</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">{{ $inventory->sum('quantity') }}</h4>
                            <small class="text-muted">Total Stock</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-warning">{{ $inventory->sum('reserved_quantity') }}</h4>
                            <small class="text-muted">Reserved</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-success">{{ $inventory->sum('quantity') - $inventory->sum('reserved_quantity') }}</h4>
                            <small class="text-muted">Available</small>
                        </div>
                        <div class="mb-3">
                            <h4 class="text-info">{{ $inventory->count() }}</h4>
                            <small class="text-muted">Warehouses</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouse Inventory -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-warehouse"></i>
                        Warehouse Inventory
                    </h6>
                </div>
                <div class="card-body">
                    @if($inventory->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Warehouse</th>
                                        <th>Location</th>
                                        <th>Quantity</th>
                                        <th>Reserved</th>
                                        <th>Available</th>
                                        <th>Reorder Level</th>
                                        <th>Cost Price</th>
                                        <th>Status</th>
                                        <th>Last Restocked</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($inventory as $item)
                                        <tr class="{{ $item->isOutOfStock() ? 'table-danger' : ($item->isLowStock() ? 'table-warning' : '') }}">
                                            <td>
                                                <strong>{{ $item->warehouse->name }}</strong><br>
                                                <small class="text-muted">{{ $item->warehouse->code }}</small>
                                            </td>
                                            <td>
                                                <code>{{ $item->location_code ?? 'N/A' }}</code>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $item->quantity > 0 ? 'primary' : 'danger' }} badge-lg">
                                                    {{ $item->quantity }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($item->reserved_quantity > 0)
                                                    <span class="badge badge-warning">{{ $item->reserved_quantity }}</span>
                                                @else
                                                    <span class="text-muted">0</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $item->available_quantity > 0 ? 'success' : 'danger' }}">
                                                    {{ $item->available_quantity }}
                                                </span>
                                            </td>
                                            <td>{{ $item->reorder_level }}</td>
                                            <td>
                                                @if($item->cost_price)
                                                    ${{ number_format($item->cost_price, 2) }}
                                                @else
                                                    <span class="text-muted">N/A</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $item->getStockStatusColor() }}">
                                                    {{ ucfirst(str_replace('_', ' ', $item->getStockStatus())) }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($item->last_restocked_at)
                                                    <small>{{ $item->last_restocked_at->format('M j, Y') }}</small>
                                                @else
                                                    <small class="text-muted">Never</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.inventory.warehouse', $item->warehouse_id) }}" 
                                                       class="btn btn-info btn-sm" title="View Warehouse">
                                                        <i class="fas fa-warehouse"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-warning btn-sm" 
                                                            title="Adjust Stock" 
                                                            onclick="adjustStock({{ $product->id }}, {{ $item->warehouse_id }}, {{ $item->quantity }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No inventory found</h5>
                            <p class="text-muted">This product is not stocked in any warehouse yet.</p>
                            <a href="{{ route('admin.inventory.add-stock-form', $product->id) }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Stock
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Movements -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-history"></i>
                        Recent Movements ({{ $movements->count() }})
                    </h6>
                    <a href="{{ route('admin.inventory.movements', ['product_id' => $product->id]) }}" class="btn btn-sm btn-info">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    @if($movements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Warehouse</th>
                                        <th>Type</th>
                                        <th>Quantity</th>
                                        <th>Previous</th>
                                        <th>New</th>
                                        <th>User</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($movements as $movement)
                                        <tr>
                                            <td>
                                                <small>{{ $movement->created_at->format('M j, Y H:i') }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ $movement->warehouse->name }}</strong><br>
                                                <small class="text-muted">{{ $movement->warehouse->code }}</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $movement->type_color }}">
                                                    <i class="{{ $movement->type_icon }}"></i>
                                                    {{ $movement->type_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-{{ $movement->quantity > 0 ? 'success' : 'danger' }}">
                                                    {{ $movement->quantity > 0 ? '+' : '' }}{{ $movement->quantity }}
                                                </span>
                                            </td>
                                            <td>{{ $movement->previous_quantity }}</td>
                                            <td>{{ $movement->new_quantity }}</td>
                                            <td>
                                                @if($movement->user)
                                                    {{ $movement->user->name }}
                                                @else
                                                    <span class="text-muted">System</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $movement->notes }}</small>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No inventory movements recorded for this product.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Adjust Stock Modal -->
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adjust Stock Quantity</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="adjustStockForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newQuantity">New Quantity</label>
                        <input type="number" class="form-control" id="newQuantity" min="0" required>
                        <small class="form-text text-muted">Current quantity: <span id="currentQuantity"></span></small>
                    </div>
                    <div class="form-group">
                        <label for="adjustNotes">Reason for Adjustment *</label>
                        <textarea class="form-control" id="adjustNotes" rows="3" required 
                                  placeholder="Explain why you're adjusting the stock quantity..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Adjust Stock</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}

.table-responsive {
    max-height: 60vh;
    overflow-y: auto;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>

<script>
let adjustProductId, adjustWarehouseId;

function adjustStock(productId, warehouseId, currentQty) {
    adjustProductId = productId;
    adjustWarehouseId = warehouseId;
    
    document.getElementById('currentQuantity').textContent = currentQty;
    document.getElementById('newQuantity').value = currentQty;
    document.getElementById('adjustNotes').value = '';
    
    $('#adjustStockModal').modal('show');
}

document.getElementById('adjustStockForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const newQuantity = document.getElementById('newQuantity').value;
    const notes = document.getElementById('adjustNotes').value;
    
    fetch(`{{ route('admin.inventory.adjust', ['product' => ':productId', 'warehouse' => ':warehouseId']) }}`
          .replace(':productId', adjustProductId)
          .replace(':warehouseId', adjustWarehouseId), {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            new_quantity: parseInt(newQuantity),
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#adjustStockModal').modal('hide');
            location.reload(); // Refresh the page to show updated data
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adjusting stock.');
    });
});
</script>
@endsection
