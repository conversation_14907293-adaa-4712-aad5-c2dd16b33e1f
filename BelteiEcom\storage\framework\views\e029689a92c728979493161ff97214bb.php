<?php $__env->startSection('title', 'Partner Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Partner Management</h1>
            <p class="mb-0 text-muted">Manage partner applications and API access</p>
        </div>
        <div>
            <span class="badge badge-info"><?php echo e($partners->total()); ?> Total Partners</span>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Applications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(App\Models\ApiPartner::where('status', 'pending')->count()); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Partners</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(App\Models\ApiPartner::where('status', 'approved')->count()); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-handshake fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Suspended</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(App\Models\ApiPartner::where('status', 'suspended')->count()); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ban fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(App\Models\ApiOrder::count()); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.partners.index')); ?>">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                                <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>Approved</option>
                                <option value="rejected" <?php echo e(request('status') === 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                                <option value="suspended" <?php echo e(request('status') === 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Company name, contact name, or email..." 
                                   value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Partners Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Partner Applications</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Company</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Tier</th>
                            <th>Commission</th>
                            <th>Applied</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $partners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="font-weight-bold"><?php echo e($partner->company_name); ?></div>
                                <small class="text-muted"><?php echo e($partner->email); ?></small>
                                <?php if($partner->website): ?>
                                    <br><a href="<?php echo e($partner->website); ?>" target="_blank" class="text-info">
                                        <i class="fas fa-external-link-alt"></i> Website
                                    </a>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo e($partner->contact_name); ?></div>
                                <?php if($partner->phone): ?>
                                    <small class="text-muted"><?php echo e($partner->phone); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($partner->status === 'pending'): ?>
                                    <span class="badge badge-warning">Pending</span>
                                <?php elseif($partner->status === 'approved'): ?>
                                    <span class="badge badge-success">Approved</span>
                                <?php elseif($partner->status === 'rejected'): ?>
                                    <span class="badge badge-danger">Rejected</span>
                                <?php elseif($partner->status === 'suspended'): ?>
                                    <span class="badge badge-secondary">Suspended</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($partner->tier): ?>
                                    <span class="badge badge-info"><?php echo e(ucfirst($partner->tier)); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($partner->commission_rate): ?>
                                    <?php echo e($partner->commission_rate); ?>%
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo e($partner->created_at->format('M j, Y')); ?></div>
                                <small class="text-muted"><?php echo e($partner->created_at->diffForHumans()); ?></small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.partners.show', $partner)); ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    <?php if($partner->status === 'pending'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                data-toggle="modal" data-target="#approveModal<?php echo e($partner->id); ?>">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                data-toggle="modal" data-target="#rejectModal<?php echo e($partner->id); ?>">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php elseif($partner->status === 'approved'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                data-toggle="modal" data-target="#suspendModal<?php echo e($partner->id); ?>">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                    <?php elseif($partner->status === 'suspended'): ?>
                                        <form method="POST" action="<?php echo e(route('admin.partners.reactivate', $partner)); ?>" 
                                              style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-success" 
                                                    onclick="return confirm('Reactivate this partner?')">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>

                        <!-- Approve Modal -->
                        <?php if($partner->status === 'pending'): ?>
                        <div class="modal fade" id="approveModal<?php echo e($partner->id); ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="<?php echo e(route('admin.partners.approve', $partner)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <div class="modal-header">
                                            <h5 class="modal-title">Approve Partner Application</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Approve <strong><?php echo e($partner->company_name); ?></strong> as a partner?</p>
                                            
                                            <div class="form-group">
                                                <label>Partner Tier</label>
                                                <select name="tier" class="form-control" required>
                                                    <option value="basic">Basic (60 req/min)</option>
                                                    <option value="premium">Premium (120 req/min)</option>
                                                    <option value="enterprise">Enterprise (300 req/min)</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Commission Rate (%)</label>
                                                <input type="number" name="commission_rate" class="form-control" 
                                                       value="15" min="0" max="50" step="0.01" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Minimum Order ($)</label>
                                                <input type="number" name="minimum_order" class="form-control" 
                                                       value="0" min="0" step="0.01">
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-success">Approve Partner</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Reject Modal -->
                        <div class="modal fade" id="rejectModal<?php echo e($partner->id); ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="<?php echo e(route('admin.partners.reject', $partner)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <div class="modal-header">
                                            <h5 class="modal-title">Reject Partner Application</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Reject <strong><?php echo e($partner->company_name); ?></strong>'s application?</p>
                                            
                                            <div class="form-group">
                                                <label>Reason for Rejection</label>
                                                <textarea name="rejection_reason" class="form-control" rows="4" 
                                                          placeholder="Please provide a clear reason for rejection..." required></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-danger">Reject Application</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Suspend Modal -->
                        <?php if($partner->status === 'approved'): ?>
                        <div class="modal fade" id="suspendModal<?php echo e($partner->id); ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="<?php echo e(route('admin.partners.suspend', $partner)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <div class="modal-header">
                                            <h5 class="modal-title">Suspend Partner</h5>
                                            <button type="button" class="close" data-dismiss="modal">
                                                <span>&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Suspend <strong><?php echo e($partner->company_name); ?></strong>?</p>
                                            
                                            <div class="form-group">
                                                <label>Reason for Suspension</label>
                                                <textarea name="suspension_reason" class="form-control" rows="4" 
                                                          placeholder="Please provide a reason for suspension..." required></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-warning">Suspend Partner</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>No partner applications found.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($partners->hasPages()): ?>
                <div class="d-flex justify-content-center">
                    <?php echo e($partners->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/admin/partners/index.blade.php ENDPATH**/ ?>