<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <script
        src="https://github.com/davidhuotkeo/bakong-khqr/releases/download/bakong-khqr-1.0.6/khqr-1.0.6.min.js"></script>
</head>

<body>
    <div id="response"></div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const { BakongKHQR, khqrData, MerchantInfo } = window.BakongKHQR;

            const merchantInfo = {
                bakongAccountID: "bundavit@wing",
                merchantName: "BUNDAVIT.COM",
                merchantCity: "Phnom Penh",
                merchantId: "1263629",
                acquiringBank: "Bakong",
            };

            const optionalData = {
                currency: khqrData.currency.usd,
                amount: getAmountFromUrl() || 0.1,
                billNumber: generateBillNumber(),
            };

            function getAmountFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return parseFloat(urlParams.get("amount"));
            }

            function generateBillNumber() {
                const currentDate = new Date();
                const formattedDate = currentDate.toISOString().replace(/[-:.TZ]/g, "");
                const day = formattedDate.substr(0, 8);
                const randomNumber = Math.floor(Math.random() * 1000);
                return `KHQR${day}${randomNumber}`;
            }

            function getUsernameFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get("username");
            }

            const merchantInfoInstance = new MerchantInfo(
                merchantInfo.bakongAccountID,
                merchantInfo.merchantName,
                merchantInfo.merchantCity,
                merchantInfo.merchantId,
                merchantInfo.acquiringBank,
                optionalData
            );

            const khqr = new BakongKHQR();
            const response = khqr.generateMerchant(merchantInfoInstance);

            // Display the response data on the frontend
            const responseElement = document.getElementById("response");
            // responseElement.textContent = JSON.stringify(response, null, 2);

            // Check if the response has the necessary data
            if (response && response.status && response.status.code === 0 && response.data && response.data.qr && response.data.md5) {
                const qrData = response.data.qr;
                const md5 = response.data.md5;
                const username = getUsernameFromUrl();

                // Redirect to qrcode.html with the specified parameters
                window.location.href = `save.php?qr=${qrData}&amount=${optionalData.amount}&md5=${md5}&username=${username}`;
            }
        });
    </script>


</body>

</html>