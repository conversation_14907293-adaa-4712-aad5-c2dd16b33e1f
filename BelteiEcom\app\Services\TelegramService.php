<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramService
{
    protected $botToken;
    protected $chatId;

    /**
     * Set the Telegram bot token
     *
     * @param string $token
     * @return $this
     */
    public function setBotToken($token)
    {
        $this->botToken = $token;
        return $this;
    }

    /**
     * Set the Telegram chat ID
     *
     * @param string $chatId
     * @return $this
     */
    public function setChatId($chatId)
    {
        $this->chatId = $chatId;
        return $this;
    }

    /**
     * Send a message to Telegram
     *
     * @param string $message
     * @return bool
     */
    public function sendMessage($message)
    {
        try {
            if (empty($this->botToken) || empty($this->chatId)) {
                Log::warning('Telegram notification not sent: Bot token or chat ID is missing');
                return false;
            }

            $response = Http::post("https://api.telegram.org/bot{$this->botToken}/sendMessage", [
                'chat_id' => $this->chatId,
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);

            if ($response->successful()) {
                Log::info('Telegram notification sent successfully');
                return true;
            } else {
                Log::error('Failed to send Telegram notification: ' . $response->body());
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Error sending Telegram notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send an order notification to Telegram
     *
     * @param Order $order
     * @return bool
     */
    public function sendOrderNotification(Order $order)
    {
        // Format the order details
        $message = $this->formatOrderMessage($order);
        
        // Send the message
        return $this->sendMessage($message);
    }

    /**
     * Format an order as a Telegram message
     *
     * @param Order $order
     * @return string
     */
    protected function formatOrderMessage(Order $order)
    {
        $message = "<b>🛒 New Order #{$order->id}</b>\n\n";
        
        // Customer information
        $message .= "<b>👤 Customer:</b> {$order->user->name}\n";
        $message .= "<b>📧 Email:</b> {$order->user->email}\n";
        $message .= "<b>📱 Phone:</b> {$order->phone_number}\n";
        $message .= "<b>🏠 Address:</b> {$order->shipping_address}\n\n";
        
        // Order details
        $message .= "<b>💰 Total Amount:</b> $" . number_format($order->total_amount, 2) . "\n";
        $message .= "<b>💳 Payment Method:</b> " . ucfirst(str_replace('_', ' ', $order->payment_method)) . "\n";
        $message .= "<b>📅 Order Date:</b> " . $order->created_at->format('Y-m-d H:i:s') . "\n\n";
        
        // Order items
        $message .= "<b>📦 Order Items:</b>\n";
        foreach ($order->orderItems as $item) {
            $message .= "- {$item->quantity}x {$item->product->name} ($" . number_format($item->price, 2) . ")\n";
        }
        
        return $message;
    }
}
