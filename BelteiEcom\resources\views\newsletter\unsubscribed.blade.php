@extends('layouts.app')

@section('title', 'Unsubscribed Successfully')

@section('content')
<style>
    .unsubscribe-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }

    .unsubscribe-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
        background-size: cover;
        opacity: 0.3;
    }

    .unsubscribe-card {
        background: white;
        border-radius: 30px;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        max-width: 600px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: slideUp 0.8s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .unsubscribe-header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        text-align: center;
        padding: 3rem 2rem;
        position: relative;
    }

    .unsubscribe-header::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 20px;
        background: white;
        border-radius: 20px 20px 0 0;
    }

    .emoji-icon {
        font-size: 5rem;
        margin-bottom: 1rem;
        display: block;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .unsubscribe-title {
        font-size: 2.8rem;
        font-weight: 800;
        margin: 0 0 1rem 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .unsubscribe-subtitle {
        font-size: 1.3rem;
        opacity: 0.95;
        margin: 0;
        font-weight: 300;
    }

    .unsubscribe-content {
        padding: 3rem 2.5rem;
        text-align: center;
    }

    .success-badge {
        background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 0 auto 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 30px rgba(0, 210, 255, 0.3);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            box-shadow: 0 10px 30px rgba(0, 210, 255, 0.3);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 210, 255, 0.4);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 10px 30px rgba(0, 210, 255, 0.3);
        }
    }

    .success-badge i {
        font-size: 2.5rem;
        color: white;
    }

    .main-message {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        line-height: 1.3;
    }

    .sub-message {
        font-size: 1.1rem;
        color: #6c757d;
        line-height: 1.7;
        margin-bottom: 2.5rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .email-highlight {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    .feedback-section {
        background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 2.5rem;
        margin: 2rem 0;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .feedback-title {
        color: #2c3e50;
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .feedback-description {
        color: #6c757d;
        margin-bottom: 2rem;
        font-size: 1rem;
    }

    .feedback-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.8rem;
        justify-content: center;
    }

    .feedback-tag {
        background: white;
        color: #495057;
        padding: 0.8rem 1.5rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
        border: 2px solid #e9ecef;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .feedback-tag:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: transparent;
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin: 2.5rem 0;
    }

    .btn-modern {
        padding: 1rem 2.5rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        min-width: 180px;
        justify-content: center;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success-modern {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
    }

    .btn-success-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(86, 171, 47, 0.4);
        color: white;
        text-decoration: none;
    }

    .resubscribe-section {
        background: linear-gradient(135deg, rgba(86, 171, 47, 0.1) 0%, rgba(168, 230, 207, 0.1) 100%);
        border-radius: 20px;
        padding: 2.5rem;
        margin: 2rem 0;
        border: 2px solid rgba(86, 171, 47, 0.2);
    }

    .resubscribe-title {
        color: #2c3e50;
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .contact-section {
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #f1f3f4;
        text-align: center;
    }

    .contact-text {
        color: #6c757d;
        font-size: 0.95rem;
        margin: 0;
    }

    .contact-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .contact-link:hover {
        color: #764ba2;
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .unsubscribe-container {
            padding: 1rem;
        }

        .unsubscribe-content {
            padding: 2rem 1.5rem;
        }

        .unsubscribe-title {
            font-size: 2.2rem;
        }

        .main-message {
            font-size: 1.5rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-modern {
            width: 100%;
            max-width: 280px;
        }

        .feedback-tags {
            flex-direction: column;
            align-items: center;
        }

        .feedback-tag {
            width: 100%;
            max-width: 200px;
            text-align: center;
        }
    }
</style>

<div class="unsubscribe-container">
    <div class="unsubscribe-card">
        <!-- Header -->
        <div class="unsubscribe-header">
            <span class="emoji-icon">👋</span>
            <h1 class="unsubscribe-title">We'll Miss You!</h1>
            <p class="unsubscribe-subtitle">Your unsubscription has been processed successfully</p>
        </div>

        <!-- Content -->
        <div class="unsubscribe-content">
            <div class="success-badge">
                <i class="fas fa-check"></i>
            </div>

            <h2 class="main-message">Unsubscription Confirmed</h2>

            <p class="sub-message">
                <span class="email-highlight">{{ $newsletter->email }}</span> has been successfully removed from our newsletter list.
                <br><br>
                You will no longer receive promotional emails from {{ config('app.name') }}. We respect your decision and thank you for the time you spent with us.
            </p>

            <!-- Feedback Section -->
            <div class="feedback-section">
                <h3 class="feedback-title">
                    <i class="fas fa-heart"></i>
                    Help Us Improve
                </h3>
                <p class="feedback-description">
                    Your feedback is valuable! Let us know why you unsubscribed so we can serve our community better:
                </p>

                <div class="feedback-tags">
                    <span class="feedback-tag" onclick="selectFeedback(this)">📧 Too many emails</span>
                    <span class="feedback-tag" onclick="selectFeedback(this)">🎯 Content not relevant</span>
                    <span class="feedback-tag" onclick="selectFeedback(this)">📱 Changed email address</span>
                    <span class="feedback-tag" onclick="selectFeedback(this)">🔒 Privacy concerns</span>
                    <span class="feedback-tag" onclick="selectFeedback(this)">⏰ Too frequent</span>
                    <span class="feedback-tag" onclick="selectFeedback(this)">❓ Other reason</span>
                </div>
            </div>

            <!-- Resubscribe Section -->
            <div class="resubscribe-section">
                <h3 class="resubscribe-title">
                    <i class="fas fa-undo"></i>
                    Changed Your Mind?
                </h3>
                <p style="color: #6c757d; margin-bottom: 2rem; font-size: 1rem;">
                    No worries! You can resubscribe anytime and continue enjoying our exclusive deals and updates.
                </p>

                <button onclick="resubscribe()" class="btn-modern btn-success-modern">
                    <i class="fas fa-plus"></i>
                    Resubscribe Now
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{ route('home') }}" class="btn-modern btn-primary-modern">
                    <i class="fas fa-home"></i>
                    Back to Store
                </a>

                <a href="{{ route('products.index') }}" class="btn-modern btn-success-modern">
                    <i class="fas fa-shopping-bag"></i>
                    Continue Shopping
                </a>
            </div>

            <!-- Contact Section -->
            <div class="contact-section">
                <p class="contact-text">
                    Questions or concerns? We're here to help!
                    <br>
                    Contact us at
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <EMAIL>
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
// Feedback selection
function selectFeedback(element) {
    // Remove selection from all tags
    document.querySelectorAll('.feedback-tag').forEach(tag => {
        tag.classList.remove('selected');
        tag.style.background = 'white';
        tag.style.color = '#495057';
        tag.style.borderColor = '#e9ecef';
    });

    // Add selection to clicked tag
    element.classList.add('selected');
    element.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    element.style.color = 'white';
    element.style.borderColor = 'transparent';

    // Show thank you message
    setTimeout(() => {
        showNotification('Thank you for your feedback! This helps us improve our service.', 'success');
    }, 300);
}

// Resubscribe function
function resubscribe() {
    const confirmation = confirm('🎉 Welcome back! Would you like to resubscribe to our newsletter and start receiving exclusive deals again?');

    if (confirmation) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resubscribing...';
        button.disabled = true;

        // Send resubscribe request
        fetch('{{ route("newsletter.subscribe") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                email: '{{ $newsletter->email }}',
                name: '{{ $newsletter->name }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('🎉 Welcome back! You have been resubscribed to our newsletter.', 'success');
                setTimeout(() => {
                    window.location.href = '{{ route("home") }}';
                }, 2000);
            } else {
                showNotification('❌ ' + (data.message || 'Something went wrong. Please try again.'), 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('❌ Something went wrong. Please try again.', 'error');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)' :
                     type === 'error' ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' :
                     'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-weight: 600;
        max-width: 400px;
        animation: slideInRight 0.5s ease-out;
    `;

    notification.innerHTML = message;
    document.body.appendChild(notification);

    // Add slide in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);

    // Remove notification after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideInRight 0.5s ease-out reverse';
        setTimeout(() => {
            notification.remove();
            style.remove();
        }, 500);
    }, 5000);
}

// Add floating particles animation
document.addEventListener('DOMContentLoaded', function() {
    createFloatingParticles();
});

function createFloatingParticles() {
    const container = document.querySelector('.unsubscribe-container');
    const particles = ['💌', '📧', '✨', '💫', '🌟'];

    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.innerHTML = particles[Math.floor(Math.random() * particles.length)];
            particle.style.cssText = `
                position: absolute;
                font-size: ${Math.random() * 20 + 15}px;
                left: ${Math.random() * 100}%;
                top: 100%;
                pointer-events: none;
                z-index: 0;
                opacity: 0.7;
                animation: floatUp ${Math.random() * 3 + 4}s linear forwards;
            `;

            container.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 7000);
        }, i * 500);
    }

    // Add CSS for floating animation
    if (!document.querySelector('#floating-animation')) {
        const style = document.createElement('style');
        style.id = 'floating-animation';
        style.textContent = `
            @keyframes floatUp {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}
</script>
@endsection
