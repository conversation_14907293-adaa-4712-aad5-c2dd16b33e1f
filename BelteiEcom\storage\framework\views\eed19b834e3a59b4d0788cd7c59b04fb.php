<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e(config('app.name', 'Laravel')); ?> - Register</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/sass/app.scss', 'resources/js/app.js']); ?>

<style>
    /* Override main layout for register page */
    body {
        background: #ffffff !important;
        min-height: 100vh;
        margin: 0;
        padding: 0;
        font-family: 'Figtree', sans-serif;
    }

    .register-page {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    /* Header Styles - Match main site exactly */
    .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        color: #2d3436;
        padding: 0.75rem 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        position: sticky;
        top: 0;
        z-index: 1000;
    }

    .header-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .logo {
        font-size: 1.5rem;
        font-weight: bold;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .logo a {
        color: inherit;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .logo a i {
        font-size: 1.2rem;
        color: #ffd700;
        -webkit-text-fill-color: #ffd700;
        background: none;
    }

    .nav-wrapper {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    nav ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 1.5rem;
    }

    nav a {
        color: #333;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    nav a:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
    }

    .auth-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .auth-buttons .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        font-size: 0.9rem;
    }

    .btn-login {
        color: #667eea;
        border-color: #667eea;
        background: transparent;
    }

    .btn-login:hover {
        background: #667eea;
        color: white;
    }

    .btn-register {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: transparent;
    }

    .btn-register:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    /* Main Content */
    .main-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .register-container {
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        padding: 2.5rem;
        width: 100%;
        max-width: 500px;
        border: 1px solid #e0e0e0;
    }

    .register-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .register-title {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .register-subtitle {
        color: #718096;
        font-size: 1rem;
    }

    /* Social Register Buttons */
    .social-register {
        margin-bottom: 2rem;
    }

    .social-buttons {
        display: flex;
        flex-direction: column;
        gap: 0;
        margin-bottom: 1.5rem;
    }

    .social-btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border: 2px solid #e0e0e0;
        border-radius: 15px;
        text-decoration: none;
        color: #333;
        font-weight: 600;
        transition: all 0.3s ease;
        background: white;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
        min-height: 48px;
    }

    .social-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        text-decoration: none;
    }

    .social-btn i {
        width: 20px;
        text-align: center;
        flex-shrink: 0;
    }

    .social-btn:hover.google-btn {
        border-color: #db4437;
        color: #db4437;
    }

    .social-btn:hover.facebook-btn {
        border-color: #4267B2;
        color: #4267B2;
    }

    .divider {
        display: flex;
        align-items: center;
        margin: 1.5rem 0;
        color: #a0aec0;
        font-size: 0.9rem;
    }

    .divider::before,
    .divider::after {
        content: '';
        flex: 1;
        height: 1px;
        background: #e2e8f0;
    }

    .divider span {
        padding: 0 1rem;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Form Styles */
    .register-form {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group.row {
        flex-direction: row;
        gap: 1rem;
    }

    .form-group.row .form-group {
        flex: 1;
        margin: 0;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-label i {
        color: #667eea;
        font-size: 0.9rem;
        width: 16px;
        text-align: center;
    }

    .form-input {
        padding: 0.875rem 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-input.is-invalid {
        border-color: #e53e3e;
    }

    .invalid-feedback {
        color: #e53e3e;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    textarea.form-input {
        resize: vertical;
        min-height: 80px;
    }

    .register-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .register-btn i {
        font-size: 0.9rem;
    }

    .register-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .register-btn:active {
        transform: translateY(0);
    }

    .login-link {
        text-align: center;
        margin-top: 1.5rem;
        color: #718096;
        font-size: 0.9rem;
    }

    .login-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
    }

    .login-link a:hover {
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .header-container {
            padding: 0 1rem;
        }

        .logo {
            font-size: 1.3rem;
        }

        .logo i {
            font-size: 1.5rem;
        }

        .main-content {
            padding: 1rem;
        }

        .register-container {
            padding: 2rem 1.5rem;
        }

        .register-title {
            font-size: 1.75rem;
        }

        .social-buttons {
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group.row {
            flex-direction: column;
            gap: 1.25rem;
        }
    }

    @media (max-width: 480px) {
        .register-container {
            padding: 1.5rem 1rem;
        }

        .register-title {
            font-size: 1.5rem;
        }

        .social-btn {
            padding: 0.625rem;
            font-size: 0.9rem;
        }

        .social-btn i {
            margin-right: 0.375rem;
        }

        .back-home {
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
        }

        .header-container {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
</head>
<body>
<div class="register-page">
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <a href="<?php echo e(route('home')); ?>">
                    <i class="fas fa-store"></i>
                    <?php echo e(config('app.name', 'BelteiEcom')); ?>

                </a>
            </div>

            <div class="nav-wrapper">
                <nav>
                    <ul>
                        <li><a href="<?php echo e(route('home')); ?>"><i class="fas fa-home"></i> Home</a></li>
                        <li><a href="<?php echo e(route('products.index')); ?>"><i class="fas fa-box"></i> Products</a></li>
                    </ul>
                </nav>

                <div class="auth-buttons">
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-login">Login</a>
                    <a href="<?php echo e(route('register')); ?>" class="btn btn-register">Register</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <div class="register-container">
            <!-- Header -->
            <div class="register-header">
                <h1 class="register-title">Create Account</h1>
                <p class="register-subtitle">Join BelteiEcom and start shopping today</p>
            </div>

            <!-- Social Register -->
            <div class="social-register">
                <div class="social-buttons">
                    <a href="<?php echo e(route('auth.google')); ?>" class="social-btn google-btn">
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </a>
                    <a href="#" class="social-btn facebook-btn" onclick="alert('Facebook registration coming soon!')">
                        <i class="fab fa-facebook-f"></i>
                        Continue with Facebook
                    </a>
                </div>

                <div class="divider">
                    <span>or register with email</span>
                </div>
            </div>

            <!-- Register Form -->
            <form method="POST" action="<?php echo e(route('register')); ?>" class="register-form">
                <?php echo csrf_field(); ?>

                <!-- Name -->
                <div class="form-group">
                    <label for="name" class="form-label">
                        <i class="fas fa-user"></i> Full Name
                    </label>
                    <input id="name" type="text" class="form-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           name="name" value="<?php echo e(old('name')); ?>" required autocomplete="name" autofocus
                           placeholder="Enter your full name">
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope"></i> Email Address
                    </label>
                    <input id="email" type="email" class="form-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email"
                           placeholder="Enter your email address">
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Password Row -->
                <div class="form-group row">
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input id="password" type="password" class="form-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               name="password" required autocomplete="new-password"
                               placeholder="Create a password">
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="password-confirm" class="form-label">
                            <i class="fas fa-lock"></i> Confirm Password
                        </label>
                        <input id="password-confirm" type="password" class="form-input"
                               name="password_confirmation" required autocomplete="new-password"
                               placeholder="Confirm your password">
                    </div>
                </div>

                <!-- Phone -->
                <div class="form-group">
                    <label for="phone" class="form-label">
                        <i class="fas fa-phone"></i> Phone Number
                    </label>
                    <input id="phone" type="text" class="form-input <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           name="phone" value="<?php echo e(old('phone')); ?>" autocomplete="phone"
                           placeholder="Enter your phone number">
                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Address -->
                <div class="form-group">
                    <label for="address" class="form-label">
                        <i class="fas fa-map-marker-alt"></i> Address
                    </label>
                    <textarea id="address" class="form-input <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                              name="address" autocomplete="address"
                              placeholder="Enter your address"><?php echo e(old('address')); ?></textarea>
                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="register-btn">
                    <i class="fas fa-user-plus"></i> Create Account
                </button>

                <!-- Login Link -->
                <div class="login-link">
                    Already have an account? <a href="<?php echo e(route('login')); ?>">Sign in here</a>
                </div>
            </form>
        </div>
    </div>
</div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\apiecom\BelteiEcom\resources\views/auth/register.blade.php ENDPATH**/ ?>