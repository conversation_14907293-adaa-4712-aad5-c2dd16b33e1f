<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Notifications\CustomVerifyEmail;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'address',
        'phone',
        'profile_picture',
        'is_admin',
        'google_id',
        'avatar',
        'badge',
        'is_banned',
        'banned_at',
        'ban_reason',
        'banned_by',
        'ban_expires_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
            'is_banned' => 'boolean',
            'banned_at' => 'datetime',
            'ban_expires_at' => 'datetime',
        ];
    }

    /**
     * Get the orders for the user.
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the cart items for the user.
     */
    public function cartItems()
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the reviews for the user.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the wishlist items for the user.
     */
    public function wishlistItems()
    {
        return $this->hasMany(Wishlist::class);
    }

    /**
     * Get the wishlisted products for the user.
     */
    public function wishlistedProducts()
    {
        return $this->belongsToMany(Product::class, 'wishlists')->withTimestamps();
    }

    /**
     * Get the user who banned this user.
     */
    public function bannedBy()
    {
        return $this->belongsTo(User::class, 'banned_by');
    }

    /**
     * Check if user is currently banned.
     */
    public function isBanned()
    {
        if (!$this->is_banned) return false;

        // Check if ban has expired
        if ($this->ban_expires_at && $this->ban_expires_at->isPast()) {
            $this->update([
                'is_banned' => false,
                'banned_at' => null,
                'ban_reason' => null,
                'banned_by' => null,
                'ban_expires_at' => null,
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get the user's badge display.
     */
    public function getBadgeDisplayAttribute()
    {
        if ($this->is_admin) {
            return '<span class="badge badge-admin"><i class="fas fa-crown"></i> Admin</span>';
        }

        if ($this->badge) {
            $badgeClass = 'badge-' . strtolower($this->badge);
            $icon = $this->getBadgeIcon();
            return '<span class="badge ' . $badgeClass . '">' . $icon . ' ' . ucfirst($this->badge) . '</span>';
        }

        return '<span class="badge badge-customer"><i class="fas fa-user"></i> Customer</span>';
    }

    /**
     * Get the icon for the user's badge.
     */
    private function getBadgeIcon()
    {
        return match($this->badge) {
            'verified' => '<i class="fas fa-check-circle"></i>',
            'premium' => '<i class="fas fa-star"></i>',
            'vip' => '<i class="fas fa-gem"></i>',
            default => '<i class="fas fa-user"></i>',
        };
    }

    /**
     * Send the email verification notification.
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new CustomVerifyEmail);
    }
}
