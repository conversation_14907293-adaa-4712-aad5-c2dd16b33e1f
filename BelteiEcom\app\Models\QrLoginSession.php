<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Carbon\Carbon;

class QrLoginSession extends Model
{
    protected $fillable = [
        'token',
        'one_time_code',
        'status',
        'user_id',
        'device_info',
        'ip_address',
        'user_agent',
        'expires_at',
        'scanned_at',
        'authenticated_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'scanned_at' => 'datetime',
        'authenticated_at' => 'datetime',
    ];

    /**
     * Get the user who scanned the QR code
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the session is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the session is valid for authentication
     */
    public function isValid(): bool
    {
        return $this->status === 'pending' && !$this->isExpired();
    }

    /**
     * Generate a unique token for the QR code
     */
    public static function generateToken(): string
    {
        do {
            $token = Str::random(64);
        } while (self::where('token', $token)->exists());

        return $token;
    }

    /**
     * Generate a unique one-time code
     */
    public static function generateOneTimeCode(): string
    {
        do {
            $code = str_pad(random_int(0, 99999999), 8, '0', STR_PAD_LEFT);
        } while (self::where('one_time_code', $code)->exists());

        return $code;
    }

    /**
     * Create a new QR login session
     */
    public static function createSession(array $deviceInfo = []): self
    {
        return self::create([
            'token' => self::generateToken(),
            'one_time_code' => self::generateOneTimeCode(),
            'status' => 'pending',
            'device_info' => json_encode($deviceInfo),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'expires_at' => Carbon::now()->addMinutes(5), // 5 minutes expiry
        ]);
    }

    /**
     * Mark session as scanned
     */
    public function markAsScanned(User $user): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        $this->update([
            'status' => 'scanned',
            'user_id' => $user->id,
            'scanned_at' => Carbon::now(),
        ]);

        return true;
    }

    /**
     * Mark session as authenticated
     */
    public function markAsAuthenticated(): bool
    {
        if ($this->status !== 'scanned' || $this->isExpired()) {
            return false;
        }

        $this->update([
            'status' => 'authenticated',
            'authenticated_at' => Carbon::now(),
        ]);

        return true;
    }

    /**
     * Clean up expired sessions
     */
    public static function cleanupExpired(): int
    {
        return self::where('expires_at', '<', Carbon::now())
            ->whereIn('status', ['pending', 'scanned'])
            ->update(['status' => 'expired']);
    }
}
