<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateProductSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:generate-slugs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate unique slugs for all products';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating slugs for products...');

        $products = Product::whereNull('slug')->orWhere('slug', '')->get();

        if ($products->isEmpty()) {
            $this->info('All products already have slugs!');
            return;
        }

        $bar = $this->output->createProgressBar($products->count());
        $bar->start();

        foreach ($products as $product) {
            do {
                $slug = Str::random(12);
            } while (Product::where('slug', $slug)->exists());

            $product->slug = $slug;
            $product->save();

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Generated slugs for {$products->count()} products!");
    }
}
