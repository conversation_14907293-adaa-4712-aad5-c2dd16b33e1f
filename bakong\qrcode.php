<?php

namespace BelteiEcom\Bakong;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// This file generates and displays the KHQR code for payment

// Get the payment ID from the request
$paymentId = $_GET['payment_id'] ?? null;

if (!$paymentId) {
    // Redirect back to checkout if no payment ID is provided
    header('Location: /checkout');
    exit;
}

try {
    // Connect to the database
    $pdo = DB::connection()->getPdo();

    // Get payment details
    $stmt = $pdo->prepare("SELECT * FROM bakong_payments WHERE id = ?");
    $stmt->execute([$paymentId]);
    $payment = $stmt->fetch(\PDO::FETCH_ASSOC);

    if (!$payment) {
        throw new \Exception("Payment not found");
    }

    // Get order details
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$payment['order_id']]);
    $order = $stmt->fetch(\PDO::FETCH_ASSOC);

    if (!$order) {
        throw new \Exception("Order not found");
    }

    // In a real implementation, you would call the Bakong API here to generate a QR code
    // For this example, we'll simulate it

    // Generate a fake QR code (in a real implementation, this would come from the Bakong API)
    $qrCodeData = "KHQR|BelteiEcom|ORDER#{$order['id']}|{$payment['amount']}|{$payment['user_name']}";

    // Generate a QR code image URL (using a public QR code generator service)
    $qrCodeUrl = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($qrCodeData);

    // Update the payment record with the QR code
    $stmt = $pdo->prepare("UPDATE bakong_payments SET qr_code = ?, updated_at = ? WHERE id = ?");
    $stmt->execute([$qrCodeUrl, date('Y-m-d H:i:s'), $paymentId]);

    // In a real implementation, you would also set up a webhook to receive payment notifications
    // For this example, we'll simulate a successful payment after a delay

    // Send notification to Telegram
    // In a real implementation, you would use the actual bot token and chat ID
    $botToken = 'YOUR_BOT_TOKEN'; // Replace with actual bot token
    $chatId = 'YOUR_CHAT_ID'; // Replace with actual chat ID

    // Call the Telegram notification route
    $telegramUrl = 'http://localhost:8000/telegram/send-order-notification';
    $telegramData = [
        'order_id' => $order['id'],
        'bot_token' => $botToken,
        'chat_id' => $chatId
    ];

    // Send the request
    $ch = curl_init($telegramUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $telegramData);
    $response = curl_exec($ch);
    curl_close($ch);

    // Redirect to success page (simulating a successful payment)
    // In a real implementation, this would happen after receiving a webhook notification
    header("Location: /orders/success/{$order['id']}");
    exit;

} catch (\Exception $e) {
    // Log the error
    Log::error('KHQR QR Code Generation Error: ' . $e->getMessage());

    // Redirect back to checkout with error
    header('Location: /checkout?error=' . urlencode('Failed to generate KHQR code. Please try again.'));
    exit;
}

// This function has been replaced by the TelegramController
