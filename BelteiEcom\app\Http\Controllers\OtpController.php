<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use App\Models\OtpCode;
use App\Models\User;
use App\Mail\OtpMail;

class OtpController extends Controller
{
    /**
     * Send OTP code to email
     */
    public function sendOtp(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|max:255'
        ]);

        $email = $request->email;

        // Rate limiting: 3 attempts per minute per email
        $key = 'otp-send:' . $email;
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => "Too many attempts. Please try again in {$seconds} seconds."
            ], 429);
        }

        RateLimiter::hit($key, 60); // 1 minute

        try {
            // Clean up expired OTPs
            OtpCode::cleanupExpired();

            // Create new OTP
            $otp = OtpCode::createForLogin($email);

            // Always show success message (security feature)
            $response = [
                'success' => true,
                'message' => 'Code has been sent to your email address.',
                'expires_in' => 60, // 1 minute
            ];

            // Only send email if user exists in system
            if ($otp->hasUser()) {
                try {
                    Mail::to($email)->send(new OtpMail($otp));
                    Log::info("OTP sent to registered user: {$email}");
                } catch (\Exception $e) {
                    Log::error("Failed to send OTP email to {$email}: " . $e->getMessage());
                    // Still return success to prevent email enumeration
                }
            } else {
                Log::info("OTP requested for non-existent user: {$email}");
                // Don't send email, but still return success message
            }

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('OTP generation failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send OTP. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify OTP and login user
     */
    public function verifyOtp(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required|string|size:6'
        ]);

        $email = $request->email;
        $code = $request->code;

        // Rate limiting: 5 attempts per minute per email
        $key = 'otp-verify:' . $email;
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => "Too many verification attempts. Please try again in {$seconds} seconds."
            ], 429);
        }

        RateLimiter::hit($key, 60); // 1 minute

        try {
            $otp = OtpCode::verify($email, $code);

            if (!$otp) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired code.'
                ], 400);
            }

            // Check if user exists in system
            if (!$otp->hasUser()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No account found with this email address.'
                ], 404);
            }

            // Log in the user
            Auth::login($otp->user);

            // Clear rate limiting on successful login
            RateLimiter::clear($key);
            RateLimiter::clear('otp-send:' . $email);

            return response()->json([
                'success' => true,
                'message' => 'Login successful!',
                'redirect_url' => route('home'),
                'user' => [
                    'name' => $otp->user->name,
                    'email' => $otp->user->email,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('OTP verification failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Verification failed. Please try again.'
            ], 500);
        }
    }

    /**
     * Resend OTP code
     */
    public function resendOtp(Request $request): JsonResponse
    {
        // Same as sendOtp but with different rate limiting
        $request->validate([
            'email' => 'required|email|max:255'
        ]);

        return $this->sendOtp($request);
    }
}
