<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ChatConversation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'guest_name',
        'guest_email',
        'session_id',
        'agent_id',
        'status',
        'priority',
        'subject',
        'initial_message',
        'customer_info',
        'started_at',
        'ended_at',
        'last_activity_at',
        'rating',
        'feedback',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'customer_info' => 'array',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the customer (user) for this conversation.
     */
    public function customer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the agent for this conversation.
     */
    public function agent()
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    /**
     * Get the messages for this conversation.
     */
    public function messages()
    {
        return $this->hasMany(ChatMessage::class, 'conversation_id');
    }

    /**
     * Get the latest message for this conversation.
     */
    public function latestMessage()
    {
        return $this->hasOne(ChatMessage::class, 'conversation_id')->latest();
    }

    /**
     * Get unread messages count for a specific user.
     */
    public function getUnreadCountForUser($userId)
    {
        return $this->messages()
                    ->where('user_id', '!=', $userId)
                    ->where('is_read', false)
                    ->count();
    }

    /**
     * Mark all messages as read for a specific user.
     */
    public function markAsReadForUser($userId)
    {
        $this->messages()
             ->where('user_id', '!=', $userId)
             ->where('is_read', false)
             ->update([
                 'is_read' => true,
                 'read_at' => now(),
             ]);
    }

    /**
     * Get customer display name.
     */
    public function getCustomerNameAttribute()
    {
        if ($this->customer) {
            return $this->customer->name;
        }

        return $this->guest_name ?: 'Guest User';
    }

    /**
     * Get customer email.
     */
    public function getCustomerEmailAttribute()
    {
        if ($this->customer) {
            return $this->customer->email;
        }

        return $this->guest_email ?: 'No email provided';
    }

    /**
     * Check if conversation is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if conversation is waiting for agent.
     */
    public function isWaiting()
    {
        return $this->status === 'waiting';
    }

    /**
     * Check if conversation is closed.
     */
    public function isClosed()
    {
        return $this->status === 'closed';
    }

    /**
     * Get conversation duration.
     */
    public function getDuration()
    {
        if (!$this->started_at) {
            return null;
        }

        $endTime = $this->ended_at ?: now();
        return $this->started_at->diffInMinutes($endTime);
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'waiting' => 'warning',
            'active' => 'success',
            'closed' => 'secondary',
            'transferred' => 'info',
        ];

        return $colors[$this->status] ?? 'primary';
    }

    /**
     * Get priority badge color.
     */
    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'secondary',
            'normal' => 'primary',
            'high' => 'warning',
            'urgent' => 'danger',
        ];

        return $colors[$this->priority] ?? 'primary';
    }

    /**
     * Assign agent to conversation.
     */
    public function assignAgent($agentId)
    {
        $this->update([
            'agent_id' => $agentId,
            'status' => 'active',
            'started_at' => $this->started_at ?: now(),
        ]);

        // Update agent's current conversation count
        $agentStatus = ChatAgentStatus::where('user_id', $agentId)->first();
        if ($agentStatus) {
            $agentStatus->increment('current_conversations');
        }

        // Add system message
        $this->messages()->create([
            'user_id' => null,
            'sender_type' => 'system',
            'message' => 'Agent has joined the conversation.',
            'message_type' => 'system',
        ]);
    }

    /**
     * Close conversation.
     */
    public function close($reason = null)
    {
        $this->update([
            'status' => 'closed',
            'ended_at' => now(),
        ]);

        // Update agent's current conversation count
        if ($this->agent_id) {
            $agentStatus = ChatAgentStatus::where('user_id', $this->agent_id)->first();
            if ($agentStatus && $agentStatus->current_conversations > 0) {
                $agentStatus->decrement('current_conversations');
            }
        }

        // Add system message
        $message = $reason ? "Conversation closed: {$reason}" : 'Conversation has been closed.';
        $this->messages()->create([
            'user_id' => null,
            'sender_type' => 'system',
            'message' => $message,
            'message_type' => 'system',
        ]);
    }

    /**
     * Update last activity timestamp.
     */
    public function updateActivity()
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Scope for active conversations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for waiting conversations.
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * Scope for conversations assigned to an agent.
     */
    public function scopeForAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * Scope for conversations by customer.
     */
    public function scopeForCustomer($query, $userId = null, $sessionId = null)
    {
        if ($userId) {
            return $query->where('user_id', $userId);
        }

        if ($sessionId) {
            return $query->where('session_id', $sessionId);
        }

        return $query->whereNull('id'); // Return empty result
    }

    /**
     * Scope for recent conversations.
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get available agents for assignment.
     */
    public static function getAvailableAgents()
    {
        return ChatAgentStatus::where('status', 'online')
                             ->whereRaw('current_conversations < max_conversations')
                             ->with('user')
                             ->get();
    }

    /**
     * Auto-assign to available agent.
     */
    public function autoAssign()
    {
        $availableAgents = self::getAvailableAgents();

        if ($availableAgents->isEmpty()) {
            return false;
        }

        // Find agent with least conversations
        $bestAgent = $availableAgents->sortBy('current_conversations')->first();

        if ($bestAgent && $bestAgent->auto_accept) {
            $this->assignAgent($bestAgent->user_id);
            return true;
        }

        return false;
    }
}
