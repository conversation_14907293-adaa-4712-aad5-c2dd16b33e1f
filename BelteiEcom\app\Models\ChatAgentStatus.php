<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatAgentStatus extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'chat_agent_status';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'status',
        'status_message',
        'max_conversations',
        'current_conversations',
        'last_activity_at',
        'skills',
        'auto_accept',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'skills' => 'array',
        'auto_accept' => 'boolean',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the user (agent) for this status.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get active conversations for this agent.
     */
    public function activeConversations()
    {
        return $this->hasMany(ChatConversation::class, 'agent_id', 'user_id')
                    ->where('status', 'active');
    }

    /**
     * Check if agent is online.
     */
    public function isOnline()
    {
        return $this->status === 'online';
    }

    /**
     * Check if agent is available for new conversations.
     */
    public function isAvailable()
    {
        return $this->isOnline() &&
               $this->current_conversations < $this->max_conversations;
    }

    /**
     * Check if agent is busy.
     */
    public function isBusy()
    {
        return $this->status === 'busy' ||
               $this->current_conversations >= $this->max_conversations;
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'online' => 'success',
            'away' => 'warning',
            'busy' => 'danger',
            'offline' => 'secondary',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * Get status icon.
     */
    public function getStatusIconAttribute()
    {
        $icons = [
            'online' => 'fas fa-circle',
            'away' => 'fas fa-clock',
            'busy' => 'fas fa-minus-circle',
            'offline' => 'fas fa-circle',
        ];

        return $icons[$this->status] ?? 'fas fa-circle';
    }

    /**
     * Set agent status to online.
     */
    public function setOnline($statusMessage = null)
    {
        $this->update([
            'status' => 'online',
            'status_message' => $statusMessage,
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Set agent status to away.
     */
    public function setAway($statusMessage = null)
    {
        $this->update([
            'status' => 'away',
            'status_message' => $statusMessage ?: 'Away from desk',
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Set agent status to busy.
     */
    public function setBusy($statusMessage = null)
    {
        $this->update([
            'status' => 'busy',
            'status_message' => $statusMessage ?: 'Busy with customers',
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Set agent status to offline.
     */
    public function setOffline()
    {
        $this->update([
            'status' => 'offline',
            'status_message' => null,
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Update last activity timestamp.
     */
    public function updateActivity()
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Get workload percentage.
     */
    public function getWorkloadPercentageAttribute()
    {
        if ($this->max_conversations <= 0) {
            return 0;
        }

        return round(($this->current_conversations / $this->max_conversations) * 100);
    }

    /**
     * Scope for online agents.
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * Scope for available agents.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'online')
                    ->whereRaw('current_conversations < max_conversations');
    }

    /**
     * Scope for agents with auto-accept enabled.
     */
    public function scopeAutoAccept($query)
    {
        return $query->where('auto_accept', true);
    }

    /**
     * Create or update agent status.
     */
    public static function updateOrCreateForUser($userId, $data = [])
    {
        return static::updateOrCreate(
            ['user_id' => $userId],
            array_merge([
                'status' => 'offline',
                'max_conversations' => 5,
                'current_conversations' => 0,
                'auto_accept' => true,
                'last_activity_at' => now(),
            ], $data)
        );
    }

    /**
     * Get agent statistics.
     */
    public function getStatistics($days = 30)
    {
        $startDate = now()->subDays($days);

        $conversations = ChatConversation::where('agent_id', $this->user_id)
                                        ->where('created_at', '>=', $startDate)
                                        ->get();

        return [
            'total_conversations' => $conversations->count(),
            'closed_conversations' => $conversations->where('status', 'closed')->count(),
            'average_rating' => $conversations->whereNotNull('rating')->avg('rating'),
            'total_messages' => ChatMessage::whereIn('conversation_id', $conversations->pluck('id'))
                                          ->where('sender_type', 'agent')
                                          ->count(),
            'average_response_time' => $this->calculateAverageResponseTime($conversations),
        ];
    }

    /**
     * Calculate average response time.
     */
    private function calculateAverageResponseTime($conversations)
    {
        $responseTimes = [];

        foreach ($conversations as $conversation) {
            $messages = $conversation->messages()
                                   ->orderBy('created_at')
                                   ->get();

            $lastCustomerMessage = null;

            foreach ($messages as $message) {
                if ($message->sender_type === 'customer') {
                    $lastCustomerMessage = $message;
                } elseif ($message->sender_type === 'agent' && $lastCustomerMessage) {
                    $responseTime = $lastCustomerMessage->created_at->diffInMinutes($message->created_at);
                    $responseTimes[] = $responseTime;
                    $lastCustomerMessage = null;
                }
            }
        }

        return empty($responseTimes) ? 0 : round(array_sum($responseTimes) / count($responseTimes), 2);
    }
}
