<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            // Smartphones (Category ID: 6)
            [
                'name' => 'iPhone 15 Pro',
                'description' => 'Latest Apple iPhone with advanced features and powerful performance.',
                'price' => 999.99,
                'stock' => 50,
                'category_id' => 6,
                'image' => 'products/iphone15pro.jpg',
            ],
            [
                'name' => 'Samsung Galaxy S24',
                'description' => 'Flagship Samsung smartphone with cutting-edge technology and stunning display.',
                'price' => 899.99,
                'stock' => 45,
                'category_id' => 6,
                'image' => 'products/galaxys24.jpg',
            ],
            [
                'name' => 'Google Pixel 8',
                'description' => 'Google\'s premium smartphone with exceptional camera capabilities and clean Android experience.',
                'price' => 799.99,
                'stock' => 30,
                'category_id' => 6,
                'image' => 'products/pixel8.jpg',
            ],

            // Laptops (Category ID: 7)
            [
                'name' => 'MacBook Pro 16"',
                'description' => 'Powerful Apple laptop for professionals with stunning Retina display and M2 chip.',
                'price' => 2499.99,
                'stock' => 25,
                'category_id' => 7,
                'image' => 'products/macbookpro.jpg',
            ],
            [
                'name' => 'Dell XPS 15',
                'description' => 'Premium Windows laptop with InfinityEdge display and high-performance components.',
                'price' => 1899.99,
                'stock' => 20,
                'category_id' => 7,
                'image' => 'products/dellxps15.jpg',
            ],
            [
                'name' => 'Lenovo ThinkPad X1 Carbon',
                'description' => 'Business laptop with legendary durability, security features, and excellent keyboard.',
                'price' => 1599.99,
                'stock' => 15,
                'category_id' => 7,
                'image' => 'products/thinkpadx1.jpg',
            ],

            // Audio (Category ID: 8)
            [
                'name' => 'Sony WH-1000XM5',
                'description' => 'Premium noise-cancelling headphones with exceptional sound quality and comfort.',
                'price' => 349.99,
                'stock' => 40,
                'category_id' => 8,
                'image' => 'products/sonywh1000xm5.jpg',
            ],
            [
                'name' => 'Bose QuietComfort Earbuds II',
                'description' => 'True wireless earbuds with industry-leading noise cancellation and rich sound.',
                'price' => 279.99,
                'stock' => 35,
                'category_id' => 8,
                'image' => 'products/boseqcearbuds.jpg',
            ],
            [
                'name' => 'JBL Flip 6',
                'description' => 'Portable Bluetooth speaker with powerful sound and waterproof design.',
                'price' => 129.99,
                'stock' => 60,
                'category_id' => 8,
                'image' => 'products/jblflip6.jpg',
            ],

            // Men's Clothing (Category ID: 9)
            [
                'name' => 'Classic Oxford Shirt',
                'description' => 'Timeless button-down shirt made from premium cotton fabric.',
                'price' => 59.99,
                'stock' => 100,
                'category_id' => 9,
                'image' => 'products/oxfordshirt.jpg',
            ],
            [
                'name' => 'Slim-Fit Chino Pants',
                'description' => 'Versatile and comfortable chino pants with modern slim fit.',
                'price' => 49.99,
                'stock' => 85,
                'category_id' => 9,
                'image' => 'products/chinopants.jpg',
            ],

            // Women's Clothing (Category ID: 10)
            [
                'name' => 'Wrap Midi Dress',
                'description' => 'Elegant midi dress with flattering wrap design and floral pattern.',
                'price' => 79.99,
                'stock' => 75,
                'category_id' => 10,
                'image' => 'products/wrapdress.jpg',
            ],
            [
                'name' => 'High-Waisted Jeans',
                'description' => 'Stylish high-waisted jeans with stretch denim for comfort and fit.',
                'price' => 69.99,
                'stock' => 90,
                'category_id' => 10,
                'image' => 'products/highwaistedjeans.jpg',
            ],

            // Kitchen Appliances (Category ID: 12)
            [
                'name' => 'Stand Mixer',
                'description' => 'Powerful stand mixer for baking and cooking with multiple attachments.',
                'price' => 299.99,
                'stock' => 30,
                'category_id' => 12,
                'image' => 'products/standmixer.jpg',
            ],
            [
                'name' => 'Air Fryer',
                'description' => 'Healthy cooking appliance that uses hot air to fry foods with little to no oil.',
                'price' => 129.99,
                'stock' => 45,
                'category_id' => 12,
                'image' => 'products/airfryer.jpg',
            ],

            // Fiction Books (Category ID: 15)
            [
                'name' => 'The Midnight Library',
                'description' => 'Bestselling novel about a library between life and death with infinite possibilities.',
                'price' => 16.99,
                'stock' => 50,
                'category_id' => 15,
                'image' => 'products/midnightlibrary.jpg',
            ],
            [
                'name' => 'Project Hail Mary',
                'description' => 'Science fiction novel about a lone astronaut who must save humanity from extinction.',
                'price' => 18.99,
                'stock' => 40,
                'category_id' => 15,
                'image' => 'products/projecthailmary.jpg',
            ],

            // Fitness (Category ID: 18)
            [
                'name' => 'Adjustable Dumbbell Set',
                'description' => 'Space-saving dumbbell set that adjusts from 5 to 52.5 pounds.',
                'price' => 349.99,
                'stock' => 20,
                'category_id' => 18,
                'image' => 'products/dumbbells.jpg',
            ],
            [
                'name' => 'Yoga Mat',
                'description' => 'Non-slip yoga mat with alignment lines for proper positioning.',
                'price' => 39.99,
                'stock' => 100,
                'category_id' => 18,
                'image' => 'products/yogamat.jpg',
            ],

            // Team Sports (Category ID: 20)
            [
                'name' => 'Basketball',
                'description' => 'Official size and weight basketball with superior grip and durability.',
                'price' => 29.99,
                'stock' => 75,
                'category_id' => 20,
                'image' => 'products/basketball.jpg',
            ],
            [
                'name' => 'Soccer Ball',
                'description' => 'Match-quality soccer ball with hand-stitched panels for precision play.',
                'price' => 24.99,
                'stock' => 80,
                'category_id' => 20,
                'image' => 'products/soccerball.jpg',
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
