<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductRecommendation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'recommended_product_id',
        'recommendation_type',
        'confidence_score',
        'metadata',
        'calculated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'calculated_at' => 'datetime',
        'confidence_score' => 'decimal:4',
    ];

    /**
     * Get the source product.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the recommended product.
     */
    public function recommendedProduct()
    {
        return $this->belongsTo(Product::class, 'recommended_product_id');
    }

    /**
     * Get recommendations for a product.
     */
    public static function getRecommendations($productId, $type = null, $limit = 6)
    {
        $query = static::where('product_id', $productId)
            ->with('recommendedProduct.category')
            ->orderByDesc('confidence_score');

        if ($type) {
            $query->where('recommendation_type', $type);
        }

        return $query->limit($limit)->get();
    }

    /**
     * Store recommendations for a product.
     */
    public static function storeRecommendations($productId, $recommendations, $type)
    {
        // Clear existing recommendations of this type
        static::where('product_id', $productId)
            ->where('recommendation_type', $type)
            ->delete();

        // Store new recommendations
        $data = [];
        foreach ($recommendations as $recommendation) {
            $data[] = [
                'product_id' => $productId,
                'recommended_product_id' => $recommendation['product_id'],
                'recommendation_type' => $type,
                'confidence_score' => $recommendation['score'],
                'metadata' => json_encode($recommendation['metadata'] ?? null),
                'calculated_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($data)) {
            static::insert($data);
        }
    }

    /**
     * Get mixed recommendations (combining different types).
     */
    public static function getMixedRecommendations($productId, $limit = 6)
    {
        $recommendations = collect();

        // Get 2 collaborative filtering recommendations
        $collaborative = static::getRecommendations($productId, 'collaborative', 2);
        $recommendations = $recommendations->merge($collaborative);

        // Get 2 content-based recommendations
        $contentBased = static::getRecommendations($productId, 'content_based', 2);
        $recommendations = $recommendations->merge($contentBased);

        // Get 2 trending recommendations
        $trending = static::getRecommendations($productId, 'trending', 2);
        $recommendations = $recommendations->merge($trending);

        // If we don't have enough, fill with similar products
        if ($recommendations->count() < $limit) {
            $similar = static::getRecommendations($productId, 'similar', $limit - $recommendations->count());
            $recommendations = $recommendations->merge($similar);
        }

        return $recommendations->unique('recommended_product_id')->take($limit);
    }
}
