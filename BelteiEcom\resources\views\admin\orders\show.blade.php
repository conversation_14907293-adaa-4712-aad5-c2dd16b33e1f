@extends('admin.layouts.app')

@section('content')
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Order #{{ $order->id }}</h1>
        <div>
            <a href="{{ route('admin.orders.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
            <a href="{{ route('admin.orders.edit', $order->id) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-edit"></i> Edit Order
            </a>
            @if(!in_array($order->status, ['delivered', 'cancelled']))
                <form action="{{ route('admin.orders.cancel', $order->id) }}" method="POST" class="d-inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to cancel this order?')">
                        <i class="fas fa-times"></i> Cancel Order
                    </button>
                </form>
            @endif
        </div>
    </div>

    <!-- Order Details -->
    <div class="row">
        <!-- Order Information -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Order ID:</strong> #{{ $order->id }}
                    </div>
                    <div class="mb-3">
                        <strong>Date:</strong> {{ $order->created_at->format('F j, Y, g:i a') }}
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <span class="badge badge-{{
                            $order->status == 'new' ? 'info' :
                            ($order->status == 'processing' ? 'warning' :
                            ($order->status == 'shipped' ? 'primary' :
                            ($order->status == 'delivered' ? 'success' : 'danger')))
                        }}">
                            {{ ucfirst($order->status) }}
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>Total Amount:</strong> ${{ number_format($order->total_amount, 2) }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Name:</strong> {{ $order->user->name }}
                    </div>
                    <div class="mb-3">
                        <strong>Email:</strong> {{ $order->user->email }}
                    </div>
                    <div class="mb-3">
                        <strong>Phone:</strong> {{ $order->phone_number ?? ($order->user->phone ?? 'N/A') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping Information -->
        <div class="col-xl-4 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Shipping Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Shipping Address:</strong>
                        <p class="mt-2">{{ $order->shipping_address }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Payment Method:</strong> Cash on Delivery
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Subtotal</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($order->orderItems as $item)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div style="width: 50px; height: 50px; overflow: hidden; margin-right: 10px;">
                                            @if($item->product->image)
                                                <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product->name }}" style="width: 100%; height: 100%; object-fit: cover;">
                                            @else
                                                <img src="https://via.placeholder.com/50?text=No+Image" alt="No Image" style="width: 100%; height: 100%; object-fit: cover;">
                                            @endif
                                        </div>
                                        <div>
                                            <a href="{{ route('admin.products.edit', $item->product->id) }}">{{ $item->product->name }}</a>
                                        </div>
                                    </div>
                                </td>
                                <td>${{ number_format($item->price, 2) }}</td>
                                <td>{{ $item->quantity }}</td>
                                <td>${{ number_format($item->price * $item->quantity, 2) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-right">Total:</th>
                            <th>${{ number_format($order->total_amount, 2) }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
@endsection
