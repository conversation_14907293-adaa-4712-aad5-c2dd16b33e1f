<?php

namespace BelteiEcom\Bakong;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// This file handles saving payment details to the database

/**
 * Save payment data to the database
 * 
 * @param array $paymentData Payment data to save
 * @return int|bool Payment ID if successful, false otherwise
 */
function savePayment($paymentData)
{
    try {
        // Check if bakong_payments table exists, if not create it
        createBakongPaymentsTableIfNotExists();
        
        // Connect to the database
        $pdo = DB::connection()->getPdo();
        
        // Insert payment data
        $sql = "INSERT INTO bakong_payments (
            order_id, 
            amount, 
            user_id, 
            user_name, 
            user_email, 
            phone_number, 
            shipping_address, 
            payment_method, 
            status, 
            created_at
        ) VALUES (
            :order_id, 
            :amount, 
            :user_id, 
            :user_name, 
            :user_email, 
            :phone_number, 
            :shipping_address, 
            :payment_method, 
            :status, 
            :created_at
        )";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':order_id' => $paymentData['order_id'],
            ':amount' => $paymentData['amount'],
            ':user_id' => $paymentData['user_id'],
            ':user_name' => $paymentData['user_name'],
            ':user_email' => $paymentData['user_email'],
            ':phone_number' => $paymentData['phone_number'],
            ':shipping_address' => $paymentData['shipping_address'],
            ':payment_method' => $paymentData['payment_method'],
            ':status' => $paymentData['status'],
            ':created_at' => $paymentData['created_at']
        ]);
        
        // Return the payment ID
        return $pdo->lastInsertId();
        
    } catch (\Exception $e) {
        // Log the error
        Log::error('KHQR Payment Save Error: ' . $e->getMessage());
        
        return false;
    }
}

/**
 * Create bakong_payments table if it doesn't exist
 */
function createBakongPaymentsTableIfNotExists()
{
    try {
        // Connect to the database
        $pdo = DB::connection()->getPdo();
        
        // Check if table exists
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'bakong_payments'");
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            // Table doesn't exist, create it
            $sql = "CREATE TABLE bakong_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                amount DECIMAL(10, 2) NOT NULL,
                user_id INT NOT NULL,
                user_name VARCHAR(255) NOT NULL,
                user_email VARCHAR(255) NOT NULL,
                phone_number VARCHAR(20) NOT NULL,
                shipping_address TEXT NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                status VARCHAR(50) NOT NULL,
                transaction_id VARCHAR(255) NULL,
                qr_code TEXT NULL,
                payment_date DATETIME NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NULL,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )";
            
            $pdo->exec($sql);
        }
        
    } catch (\Exception $e) {
        // Log the error
        Log::error('Create Bakong Payments Table Error: ' . $e->getMessage());
    }
}
