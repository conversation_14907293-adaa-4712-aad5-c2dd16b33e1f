@extends('admin.layouts.app')

@section('styles')
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .admin-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .admin-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .admin-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
        border: 3px solid rgba(250, 112, 154, 0.2);
    }

    .admin-avatar.current-user {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .admin-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .admin-email {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .admin-phone {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .admin-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .admin-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-view-admin {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-view-admin:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-edit-admin {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-edit-admin:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-delete-admin {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-delete-admin:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        color: white;
    }

    .btn-delete-admin:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .admins-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 1;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #fa709a;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .search-filters {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
    }

    .admin-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.7rem;
        font-weight: 600;
        color: white;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .badge-current {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .btn-verify-email {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-verify-email:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .email-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .email-verified {
        color: #28a745;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .email-unverified {
        color: #dc3545;
        font-size: 0.8rem;
        font-weight: 600;
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center" style="position: relative; z-index: 10;">
            <div>
                <h1 class="h2 mb-2">👑 Admin Management</h1>
                <p class="mb-0 opacity-75">Manage administrator accounts and permissions</p>
            </div>
            <div>
                <a href="{{ route('admin.admins.create') }}" class="btn btn-light btn-lg" style="position: relative; z-index: 1000; pointer-events: auto;">
                    <i class="fas fa-user-shield"></i> Add New Admin
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-cards slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-number">{{ $totalAdmins }}</div>
            <div class="stat-label">Total Admins</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ $newAdminsToday }}</div>
            <div class="stat-label">New Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ $newAdminsThisMonth }}</div>
            <div class="stat-label">New This Month</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #dc3545;">{{ $unverifiedAdmins }}</div>
            <div class="stat-label">Unverified Emails</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters slide-up" style="animation-delay: 0.3s;">
        <form method="GET" action="{{ route('admin.admins.index') }}">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="search" class="form-label">Search Admins</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Search by name or email...">
                </div>
                <div class="filter-group">
                    <label for="email_verified" class="form-label">Email Status</label>
                    <select class="form-control" id="email_verified" name="email_verified">
                        <option value="">All Admins</option>
                        <option value="1" {{ request('email_verified') == '1' ? 'selected' : '' }}>Verified Only</option>
                        <option value="0" {{ request('email_verified') == '0' ? 'selected' : '' }}>Unverified Only</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{{ route('admin.admins.index') }}" class="btn btn-secondary ml-2">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Admins Grid -->
    <div class="slide-up" style="animation-delay: 0.4s;">
        @if($admins->count() > 0)
            <div class="admins-grid">
                @foreach($admins as $admin)
                    <div class="admin-card">
                        @if($admin->id === auth()->id())
                            <div class="admin-badge badge-current">
                                <i class="fas fa-user"></i> You
                            </div>
                        @else
                            <div class="admin-badge">
                                <i class="fas fa-shield-alt"></i> Admin
                            </div>
                        @endif

                        <div class="admin-avatar {{ $admin->id === auth()->id() ? 'current-user' : '' }}" @if($admin->profile_picture) style="background-image: url('{{ asset('storage/' . $admin->profile_picture) }}'); background-size: cover; background-position: center;" @endif>
                            @if(!$admin->profile_picture)
                                {{ substr($admin->name, 0, 1) }}
                            @endif
                        </div>

                        <div class="admin-name">{{ $admin->name }}</div>
                        <div class="admin-email">
                            <i class="fas fa-envelope"></i> {{ $admin->email }}
                        </div>
                        <div class="email-status">
                            @if($admin->hasVerifiedEmail())
                                <span class="email-verified">
                                    <i class="fas fa-check-circle"></i> Email Verified
                                </span>
                            @else
                                <span class="email-unverified">
                                    <i class="fas fa-exclamation-circle"></i> Email Not Verified
                                </span>
                            @endif
                        </div>
                        @if($admin->phone)
                            <div class="admin-phone">
                                <i class="fas fa-phone"></i> {{ $admin->phone }}
                            </div>
                        @endif

                        <div class="admin-meta">
                            <div>
                                <i class="fas fa-calendar"></i> {{ $admin->created_at->format('M j, Y') }}
                            </div>
                            <div>
                                <i class="fas fa-clock"></i> {{ $admin->created_at->diffForHumans() }}
                            </div>
                        </div>

                        <div class="admin-actions">
                            <a href="{{ route('admin.admins.show', $admin->id) }}" class="btn btn-view-admin btn-sm" title="View Admin">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="{{ route('admin.admins.edit', $admin->id) }}" class="btn btn-edit-admin btn-sm" title="Edit Admin">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            @if(!$admin->hasVerifiedEmail())
                                <form action="{{ route('admin.admins.verify-email', $admin->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-verify-email btn-sm" onclick="return confirm('Are you sure you want to verify this admin\'s email?')" title="Verify Email">
                                        <i class="fas fa-check"></i> Verify
                                    </button>
                                </form>
                            @endif
                            @if($admin->id !== auth()->id() && $totalAdmins > 1)
                                <form action="{{ route('admin.admins.destroy', $admin->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-delete-admin btn-sm" onclick="return confirm('Are you sure you want to delete this admin?')" title="Delete Admin">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            @else
                                <button class="btn btn-delete-admin btn-sm" disabled title="Cannot delete">
                                    <i class="fas fa-ban"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="mt-4 d-flex justify-content-center">
                {{ $adminsPaginator->appends(request()->query())->links() }}
            </div>
        @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-user-shield fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">No admins found</h5>
                    <p class="text-gray-400">
                        @if(request('search'))
                            Try adjusting your search criteria.
                        @else
                            This shouldn't happen - there should always be at least one admin.
                        @endif
                    </p>
                </div>
            </div>
        @endif
    </div>
@endsection
