<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_recommendations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('recommended_product_id')->constrained('products')->onDelete('cascade');
            $table->string('recommendation_type'); // 'collaborative', 'content_based', 'trending', 'similar'
            $table->decimal('confidence_score', 5, 4)->default(0); // 0.0000 to 1.0000
            $table->json('metadata')->nullable(); // Additional data like reasons, tags, etc.
            $table->timestamp('calculated_at')->useCurrent();
            $table->timestamps();

            // Ensure unique recommendations per product and type
            $table->unique(['product_id', 'recommended_product_id', 'recommendation_type'], 'unique_product_recommendation');

            // Indexes for performance
            $table->index(['product_id', 'recommendation_type', 'confidence_score'], 'idx_product_rec_type_score');
            $table->index(['recommended_product_id'], 'idx_recommended_product');
            $table->index('calculated_at', 'idx_calculated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_recommendations');
    }
};
