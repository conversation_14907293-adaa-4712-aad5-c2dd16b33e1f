<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Display a listing of orders.
     */
    public function index()
    {
        $orders = Order::with('user')->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.orders.index', compact('orders'));
    }

    /**
     * Display the specified order.
     */
    public function show($id)
    {
        $order = Order::with(['user', 'orderItems.product'])->findOrFail($id);
        return view('admin.orders.show', compact('order'));
    }

    /**
     * Show the form for editing an order.
     */
    public function edit($id)
    {
        $order = Order::findOrFail($id);
        $statuses = ['new', 'processing', 'shipped', 'delivered', 'cancelled'];

        return view('admin.orders.edit', compact('order', 'statuses'));
    }

    /**
     * Update the specified order.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:new,processing,shipped,delivered,cancelled',
        ]);

        $order = Order::findOrFail($id);
        $order->status = $request->status;
        $order->save();

        return redirect()->route('admin.orders.show', $id)
                         ->with('success', 'Order status updated successfully!');
    }

    /**
     * Cancel the specified order.
     */
    public function cancel($id)
    {
        $order = Order::findOrFail($id);

        // Only allow cancellation if order is not delivered or already cancelled
        if (in_array($order->status, ['delivered', 'cancelled'])) {
            return redirect()->route('admin.orders.show', $id)
                             ->with('error', 'Cannot cancel an order that is already delivered or cancelled.');
        }

        $order->status = 'cancelled';
        $order->save();

        return redirect()->route('admin.orders.show', $id)
                         ->with('success', 'Order cancelled successfully!');
    }
}
