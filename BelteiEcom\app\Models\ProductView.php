<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductView extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'product_id',
        'session_id',
        'ip_address',
        'user_agent',
        'view_duration',
        'viewed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'viewed_at' => 'datetime',
    ];

    /**
     * Get the user that viewed the product.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product that was viewed.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Record a product view.
     */
    public static function recordView($productId, $userId = null, $sessionId = null, $viewDuration = 0)
    {
        return static::create([
            'user_id' => $userId,
            'product_id' => $productId,
            'session_id' => $sessionId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'view_duration' => $viewDuration,
            'viewed_at' => now(),
        ]);
    }

    /**
     * Get popular products based on views.
     */
    public static function getPopularProducts($limit = 10, $days = 30)
    {
        return static::select('product_id')
            ->selectRaw('COUNT(*) as view_count')
            ->where('viewed_at', '>=', now()->subDays($days))
            ->groupBy('product_id')
            ->orderByDesc('view_count')
            ->limit($limit)
            ->with('product')
            ->get()
            ->pluck('product');
    }

    /**
     * Get trending products (products with increasing views).
     */
    public static function getTrendingProducts($limit = 10)
    {
        $recentViews = static::select('product_id')
            ->selectRaw('COUNT(*) as recent_count')
            ->where('viewed_at', '>=', now()->subDays(7))
            ->groupBy('product_id');

        $olderViews = static::select('product_id')
            ->selectRaw('COUNT(*) as older_count')
            ->where('viewed_at', '>=', now()->subDays(30))
            ->where('viewed_at', '<', now()->subDays(7))
            ->groupBy('product_id');

        return static::select('recent.product_id')
            ->selectRaw('recent.recent_count, COALESCE(older.older_count, 0) as older_count')
            ->selectRaw('(recent.recent_count / GREATEST(COALESCE(older.older_count, 1), 1)) as trend_score')
            ->from($recentViews, 'recent')
            ->leftJoinSub($olderViews, 'older', function ($join) {
                $join->on('recent.product_id', '=', 'older.product_id');
            })
            ->orderByDesc('trend_score')
            ->limit($limit)
            ->get();
    }
}
