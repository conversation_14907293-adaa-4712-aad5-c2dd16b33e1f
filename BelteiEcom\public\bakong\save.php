<?php
// Connect to the database
$conn = mysqli_connect("localhost", "chheans1_bundavit", "chheans1_bundavit", "chheans1_bundavit");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Get the parameters from the URL
$qrData = $_GET['qr'];
$amount = $_GET['amount'];
$md5 = $_GET['md5'];
$username = $_GET['username'];

// Insert the data into the database
$sql = "INSERT INTO qrcode_data (qr_data, amount, md5, username) VALUES ('$qrData', '$amount', '$md5', '$username')";

if (mysqli_query($conn, $sql)) {
    // Redirect to qrcode.php with the parameters
    header("Location: qrcode.php?qr=$qrData&amount=$amount&md5=$md5&username=$username");
    exit;
} else {
    echo "Error: " . $sql . "<br>" . mysqli_error($conn);
}

// Close the database connection
mysqli_close($conn);
?>