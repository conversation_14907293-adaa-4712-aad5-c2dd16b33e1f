@extends('admin.layouts.app')

@section('styles')
<style>
    .customer-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .customer-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .customer-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .customer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        position: relative;
    }

    .customer-avatar.vip {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .customer-avatar.new {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .customer-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .customer-email {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .customer-phone {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .customer-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-number {
        font-size: 1.2rem;
        font-weight: 700;
        color: #667eea;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.2rem;
    }

    .customer-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-view-customer {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-view-customer:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-edit-customer {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-edit-customer:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-delete-customer {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-delete-customer:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        color: white;
    }

    .customers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        pointer-events: none;
        z-index: 1;
    }

    .page-header:hover::before {
        right: -30%;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .search-filters {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
    }

    .customer-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.7rem;
        font-weight: 600;
        color: white;
    }

    .badge-vip {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .badge-new {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .badge-active {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .btn-verify-email {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .btn-verify-email:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .email-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .email-verified {
        color: #28a745;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .email-unverified {
        color: #dc3545;
        font-size: 0.8rem;
        font-weight: 600;
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center" style="position: relative; z-index: 10;">
            <div>
                <h1 class="h2 mb-2">👥 Customer Management</h1>
                <p class="mb-0 opacity-75">Manage your customer base and relationships</p>
            </div>
            <div>
                <a href="{{ route('admin.customers.create') }}" class="btn btn-light btn-lg" style="position: relative; z-index: 1000; pointer-events: auto;">
                    <i class="fas fa-user-plus"></i> Add New Customer
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-cards slide-up" style="animation-delay: 0.2s;">
        <div class="stat-card">
            <div class="stat-number">{{ $totalCustomers }}</div>
            <div class="stat-label">Total Customers</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ $newCustomersToday }}</div>
            <div class="stat-label">New Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ $newCustomersThisMonth }}</div>
            <div class="stat-label">New This Month</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ $activeCustomers }}</div>
            <div class="stat-label">Active (30 days)</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #dc3545;">{{ $unverifiedCustomers }}</div>
            <div class="stat-label">Unverified Emails</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters slide-up" style="animation-delay: 0.3s;">
        <form method="GET" action="{{ route('admin.customers.index') }}">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="search" class="form-label">Search Customers</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Search by name, email, or phone...">
                </div>
                <div class="filter-group">
                    <label for="date_filter" class="form-label">Registration Date</label>
                    <select class="form-control" id="date_filter" name="date_filter">
                        <option value="">All Time</option>
                        <option value="today" {{ request('date_filter') == 'today' ? 'selected' : '' }}>Today</option>
                        <option value="week" {{ request('date_filter') == 'week' ? 'selected' : '' }}>This Week</option>
                        <option value="month" {{ request('date_filter') == 'month' ? 'selected' : '' }}>This Month</option>
                        <option value="year" {{ request('date_filter') == 'year' ? 'selected' : '' }}>This Year</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="email_verified" class="form-label">Email Status</label>
                    <select class="form-control" id="email_verified" name="email_verified">
                        <option value="">All Customers</option>
                        <option value="1" {{ request('email_verified') == '1' ? 'selected' : '' }}>Verified Only</option>
                        <option value="0" {{ request('email_verified') == '0' ? 'selected' : '' }}>Unverified Only</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{{ route('admin.customers.index') }}" class="btn btn-secondary ml-2">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Customers Grid -->
    <div class="slide-up" style="animation-delay: 0.4s;">
        @if($customers->count() > 0)
            <div class="customers-grid">
                @foreach($customers as $customer)
                    <div class="customer-card">
                        @php
                            $isNew = $customer->created_at->diffInDays(now()) <= 7;
                            $isVip = $customer->orders_sum_total_amount > 1000;
                            $isActive = $customer->orders()->where('created_at', '>=', now()->subDays(30))->exists();
                        @endphp

                        @if($isVip)
                            <div class="customer-badge badge-vip">
                                <i class="fas fa-crown"></i> VIP
                            </div>
                        @elseif($isNew)
                            <div class="customer-badge badge-new">
                                <i class="fas fa-star"></i> New
                            </div>
                        @elseif($isActive)
                            <div class="customer-badge badge-active">
                                <i class="fas fa-bolt"></i> Active
                            </div>
                        @endif

                        <div class="customer-avatar {{ $isVip ? 'vip' : ($isNew ? 'new' : '') }}">
                            {{ substr($customer->name, 0, 1) }}
                        </div>

                        <div class="customer-name">{{ $customer->name }}</div>
                        <div class="customer-email">
                            <i class="fas fa-envelope"></i> {{ $customer->email }}
                        </div>
                        <div class="email-status">
                            @if($customer->hasVerifiedEmail())
                                <span class="email-verified">
                                    <i class="fas fa-check-circle"></i> Email Verified
                                </span>
                            @else
                                <span class="email-unverified">
                                    <i class="fas fa-exclamation-circle"></i> Email Not Verified
                                </span>
                            @endif
                        </div>
                        @if($customer->phone)
                            <div class="customer-phone">
                                <i class="fas fa-phone"></i> {{ $customer->phone }}
                            </div>
                        @endif

                        <div class="customer-stats">
                            <div class="stat-item">
                                <div class="stat-number">{{ $customer->orders_count }}</div>
                                <div class="stat-label">Orders</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${{ number_format($customer->orders_sum_total_amount ?? 0, 0) }}</div>
                                <div class="stat-label">Total Spent</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" style="font-size: 0.9rem;">{{ $customer->created_at->format('M Y') }}</div>
                                <div class="stat-label">Joined</div>
                            </div>
                        </div>

                        <div class="customer-actions">
                            <a href="{{ route('admin.customers.show', $customer->id) }}" class="btn btn-view-customer btn-sm" title="View Customer">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="{{ route('admin.customers.edit', $customer->id) }}" class="btn btn-edit-customer btn-sm" title="Edit Customer">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            @if(!$customer->hasVerifiedEmail())
                                <form action="{{ route('admin.customers.verify-email', $customer->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-verify-email btn-sm" onclick="return confirm('Are you sure you want to verify this customer\'s email?')" title="Verify Email">
                                        <i class="fas fa-check"></i> Verify
                                    </button>
                                </form>
                            @endif
                            @if($customer->orders_count == 0)
                                <form action="{{ route('admin.customers.destroy', $customer->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-delete-customer btn-sm" onclick="return confirm('Are you sure you want to delete this customer?')" title="Delete Customer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="mt-4 d-flex justify-content-center">
                {{ $customersPaginator->appends(request()->query())->links() }}
            </div>
        @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">No customers found</h5>
                    <p class="text-gray-400">
                        @if(request('search') || request('date_filter'))
                            Try adjusting your search criteria or filters.
                        @else
                            Customers will appear here when they register on your store.
                        @endif
                    </p>
                    @if(!request('search') && !request('date_filter'))
                        <a href="{{ route('admin.customers.create') }}" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Add First Customer
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
@endsection
