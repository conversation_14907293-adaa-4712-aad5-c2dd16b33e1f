<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    /**
     * Display the user's wishlist.
     */
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to view your wishlist.');
        }

        $wishlistItems = Wishlist::with(['product.category'])
                                ->where('user_id', Auth::id())
                                ->orderBy('added_at', 'desc')
                                ->paginate(12);

        return view('wishlist.index', compact('wishlistItems'));
    }

    /**
     * Add product to wishlist.
     */
    public function store(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to add items to your wishlist.',
                'redirect' => route('login')
            ], 401);
        }

        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $userId = Auth::id();
        $productId = $request->product_id;

        // Check if already in wishlist
        if (Wishlist::isInWishlist($userId, $productId)) {
            return response()->json([
                'success' => false,
                'message' => 'Product is already in your wishlist!'
            ]);
        }

        // Add to wishlist
        $wishlistItem = Wishlist::addToWishlist($userId, $productId);
        $product = Product::find($productId);

        return response()->json([
            'success' => true,
            'message' => "'{$product->name}' has been added to your wishlist!",
            'wishlist_count' => Wishlist::where('user_id', $userId)->count()
        ]);
    }

    /**
     * Remove product from wishlist.
     */
    public function destroy(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to manage your wishlist.'
            ], 401);
        }

        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $userId = Auth::id();
        $productId = $request->product_id;

        $removed = Wishlist::removeFromWishlist($userId, $productId);
        $product = Product::find($productId);

        if ($removed) {
            return response()->json([
                'success' => true,
                'message' => "'{$product->name}' has been removed from your wishlist!",
                'wishlist_count' => Wishlist::where('user_id', $userId)->count()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Product was not found in your wishlist.'
        ]);
    }

    /**
     * Toggle product in wishlist (add if not present, remove if present).
     */
    public function toggle(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to manage your wishlist.',
                'redirect' => route('login')
            ], 401);
        }

        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $userId = Auth::id();
        $productId = $request->product_id;
        $product = Product::find($productId);

        if (Wishlist::isInWishlist($userId, $productId)) {
            // Remove from wishlist
            Wishlist::removeFromWishlist($userId, $productId);
            $action = 'removed';
            $message = "'{$product->name}' has been removed from your wishlist!";
            $inWishlist = false;
        } else {
            // Add to wishlist
            Wishlist::addToWishlist($userId, $productId);
            $action = 'added';
            $message = "'{$product->name}' has been added to your wishlist!";
            $inWishlist = true;
        }

        return response()->json([
            'success' => true,
            'action' => $action,
            'in_wishlist' => $inWishlist,
            'message' => $message,
            'wishlist_count' => Wishlist::where('user_id', $userId)->count()
        ]);
    }

    /**
     * Get wishlist count for authenticated user.
     */
    public function count()
    {
        if (!Auth::check()) {
            return response()->json(['count' => 0]);
        }

        $count = Wishlist::where('user_id', Auth::id())->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Clear entire wishlist.
     */
    public function clear()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to manage your wishlist.');
        }

        $deleted = Wishlist::where('user_id', Auth::id())->delete();

        return redirect()->route('wishlist.index')->with('success', "Cleared {$deleted} items from your wishlist.");
    }
}
