<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_agent_status', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Agent user
            $table->enum('status', ['online', 'away', 'busy', 'offline'])->default('offline');
            $table->text('status_message')->nullable(); // Custom status message
            $table->integer('max_conversations')->default(5); // Max concurrent chats
            $table->integer('current_conversations')->default(0); // Current active chats
            $table->timestamp('last_activity_at')->useCurrent();
            $table->json('skills')->nullable(); // Agent skills/departments
            $table->boolean('auto_accept')->default(true); // Auto-accept new chats
            $table->timestamps();

            // Ensure one status per agent
            $table->unique('user_id');

            // Indexes
            $table->index(['status', 'current_conversations']);
            $table->index('last_activity_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_agent_status');
    }
};
