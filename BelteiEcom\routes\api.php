<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Product API Routes
Route::prefix('products')->group(function () {
    Route::get('/', [App\Http\Controllers\Api\ProductController::class, 'index']);
    Route::get('/search', [App\Http\Controllers\Api\ProductController::class, 'search']);
    Route::get('/category/{categoryId}', [App\Http\Controllers\Api\ProductController::class, 'byCategory']);
    Route::get('/{id}', [App\Http\Controllers\Api\ProductController::class, 'show']);
});

// Bakong API Routes
Route::prefix('bakong')->group(function () {
    Route::post('/check-transaction', [App\Http\Controllers\BakongController::class, 'checkTransactionWithBakongApi']);
    Route::post('/check-order-transaction', [App\Http\Controllers\BakongController::class, 'checkTransactionByOrder']);
});
