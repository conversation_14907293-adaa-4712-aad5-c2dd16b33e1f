<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ApiPartner;

class ApiPartnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a demo partner for testing
        ApiPartner::create([
            'company_name' => 'Demo Partner Store',
            'contact_name' => 'John Demo',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'website' => 'https://demo-partner.com',
            'business_description' => 'Demo partner for testing API integration',
            'api_key' => 'bec_demo_12345678901234567890123456789012',
            'api_secret' => bcrypt('demo_secret_key'),
            'status' => 'approved',
            'tier' => 'basic',
            'commission_rate' => 15.00,
            'minimum_order' => 0.00,
            'allowed_categories' => null, // Access to all categories
            'settings' => [
                'rate_limit' => [
                    'requests_per_minute' => 60,
                    'requests_per_hour' => 1000
                ]
            ],
            'approved_at' => now(),
        ]);

        // Create a premium partner example
        ApiPartner::create([
            'company_name' => 'Premium Electronics Store',
            'contact_name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '+1987654321',
            'website' => 'https://premiumstore.com',
            'business_description' => 'High-volume electronics retailer',
            'api_key' => 'bec_premium_' . str_pad(random_int(100000000000, 999999999999), 12, '0', STR_PAD_LEFT),
            'api_secret' => bcrypt('premium_secret_key'),
            'status' => 'approved',
            'tier' => 'premium',
            'commission_rate' => 20.00,
            'minimum_order' => 100.00,
            'allowed_categories' => [1, 2, 3], // Limited to specific categories
            'settings' => [
                'rate_limit' => [
                    'requests_per_minute' => 120,
                    'requests_per_hour' => 5000
                ]
            ],
            'approved_at' => now(),
        ]);
    }
}
