<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Order Receipt #{{ $order->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 15px;
        }

        .order-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }

        .order-info-left, .order-info-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }

        .order-info-right {
            text-align: right;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        .customer-info, .payment-info {
            margin-bottom: 20px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .items-table .text-right {
            text-align: right;
        }

        .items-table .text-center {
            text-align: center;
        }

        .total-section {
            margin-top: 20px;
            text-align: right;
        }

        .total-row {
            margin-bottom: 5px;
        }

        .total-final {
            font-size: 16px;
            font-weight: bold;
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-top: 10px;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-processing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-pending {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-new {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Beltei Ecom') }}</div>
        <div>Your Trusted Online Store</div>
        <div class="receipt-title">ORDER RECEIPT</div>
    </div>

    <!-- Order Information -->
    <div class="order-info">
        <div class="order-info-left">
            <strong>Order #{{ $order->id }}</strong><br>
            Order Date: {{ $order->created_at->setTimezone('Asia/Phnom_Penh')->format('M d, Y H:i') }} (Cambodia Time)<br>
            Status: <span class="status-badge status-{{ $order->status }}">{{ ucfirst($order->status) }}</span>
        </div>
        <div class="order-info-right">
            Receipt Date: {{ now()->setTimezone('Asia/Phnom_Penh')->format('M d, Y H:i') }} (Cambodia Time)<br>
            Payment Method:
            @if($order->payment_method === 'cash_on_delivery')
                Cash on Delivery
            @else
                Bakong (KHQR)
            @endif
            <br>
            @if($payment)
            Transaction ID: {{ $payment->transaction_id }}
            @endif
        </div>
    </div>

    <!-- Customer Information -->
    <div class="section-title">Customer Information</div>
    <div class="customer-info">
        <strong>{{ $order->user->name }}</strong><br>
        Email: {{ $order->user->email }}<br>
        Phone: {{ $order->phone_number }}<br>
        <br>
        <strong>Shipping Address:</strong><br>
        {{ $order->shipping_address }}
    </div>

    <!-- Payment Information -->
    <div class="section-title">Payment Information</div>
    <div class="payment-info">
        @if($order->payment_method === 'cash_on_delivery')
            Payment Method: <strong>Cash on Delivery</strong><br>
            Payment Status: <span class="status-badge status-processing">Pending</span><br>
            Payment Note: Payment will be collected upon delivery<br>
            Order Amount: ${{ number_format($order->total_amount, 2) }}
        @elseif($payment)
            Payment Method: <strong>Bakong (KHQR)</strong><br>
            Payment Status: <span class="status-badge status-{{ $payment->status }}">{{ ucfirst($payment->status) }}</span><br>
            Payment Date: {{ $payment->payment_date ? $payment->payment_date->setTimezone('Asia/Phnom_Penh')->format('M d, Y H:i') . ' (Cambodia Time)' : 'N/A' }}<br>
            Transaction ID: {{ $payment->transaction_id }}<br>
            Amount Paid: ${{ number_format($payment->amount, 2) }}
        @else
            Payment Method: <strong>Bakong (KHQR)</strong><br>
            Payment Status: <span class="status-badge status-processing">Processing</span><br>
            Order Amount: ${{ number_format($order->total_amount, 2) }}
        @endif
    </div>

    <!-- Order Items -->
    <div class="section-title">Order Items</div>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Item</th>
                <th class="text-center" style="width: 15%;">Qty</th>
                <th class="text-right" style="width: 15%;">Price</th>
                <th class="text-right" style="width: 20%;">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->orderItems as $item)
            <tr>
                <td>
                    <strong>{{ $item->product->name }}</strong>
                    @if($item->product->description)
                    <br><small>{{ Str::limit($item->product->description, 100) }}</small>
                    @endif
                </td>
                <td class="text-center">{{ $item->quantity }}</td>
                <td class="text-right">${{ number_format($item->price, 2) }}</td>
                <td class="text-right">${{ number_format($item->price * $item->quantity, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Order Total -->
    <div class="total-section">
        <div class="total-row">
            <strong>Subtotal: ${{ number_format($order->total_amount, 2) }}</strong>
        </div>
        <div class="total-row">
            Shipping: Free
        </div>
        <div class="total-row">
            Tax: $0.00
        </div>
        <div class="total-final">
            <strong>Total: ${{ number_format($order->total_amount, 2) }}</strong>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated receipt. No signature required.</p>
        <p>For questions about this order, please contact us with your order number.</p>
        <p>Generated on {{ now()->setTimezone('Asia/Phnom_Penh')->format('M d, Y H:i:s') }} (Cambodia Time)</p>
    </div>
</body>
</html>
