@extends('admin.layouts.app')

@section('styles')
<style>
    .password-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .password-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
    }
    
    .password-header:hover::before {
        right: -30%;
    }
    
    .password-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 2rem;
        border: none;
    }
    
    .password-card:hover {
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .security-info {
        background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
        border-left: 4px solid #43e97b;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .security-info h5 {
        color: #155724;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .security-info ul {
        margin-bottom: 0;
        color: #155724;
    }
    
    .security-info li {
        margin-bottom: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
        position: relative;
    }
    
    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
        padding-right: 3rem;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 0;
        font-size: 1rem;
    }
    
    .password-toggle:hover {
        color: #667eea;
    }
    
    .password-strength {
        margin-top: 0.5rem;
        height: 4px;
        background: #e3e6f0;
        border-radius: 2px;
        overflow: hidden;
    }
    
    .password-strength-bar {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }
    
    .strength-weak { background: #dc3545; width: 25%; }
    .strength-fair { background: #fd7e14; width: 50%; }
    .strength-good { background: #ffc107; width: 75%; }
    .strength-strong { background: #28a745; width: 100%; }
    
    .password-requirements {
        margin-top: 1rem;
        font-size: 0.85rem;
    }
    
    .requirement {
        color: #6c757d;
        margin-bottom: 0.25rem;
        transition: color 0.3s ease;
    }
    
    .requirement.met {
        color: #28a745;
    }
    
    .requirement i {
        margin-right: 0.5rem;
        width: 12px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }
</style>
@endsection

@section('content')
    <!-- Password Header -->
    <div class="password-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">🔒 Password & Security</h1>
                <p class="mb-0 opacity-75">Change your password and manage security settings</p>
            </div>
            <div>
                <a href="{{ route('admin.settings.index') }}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to Settings
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Security Information -->
            <div class="security-info slide-up" style="animation-delay: 0.1s;">
                <h5><i class="fas fa-shield-alt"></i> Password Security Tips</h5>
                <ul>
                    <li>Use a strong password with at least 8 characters</li>
                    <li>Include uppercase and lowercase letters, numbers, and symbols</li>
                    <li>Don't use personal information or common words</li>
                    <li>Change your password regularly</li>
                    <li>Don't share your password with anyone</li>
                </ul>
            </div>

            <!-- Password Change Form -->
            <div class="password-card slide-up" style="animation-delay: 0.2s;">
                <form action="{{ route('admin.settings.password.update') }}" method="POST" id="passwordForm">
                    @csrf
                    @method('PUT')

                    <div class="form-group">
                        <label for="current_password" class="form-label">Current Password</label>
                        <div style="position: relative;">
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                   id="current_password" name="current_password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                        @error('current_password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">New Password</label>
                        <div style="position: relative;">
                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                   id="password" name="password" required onkeyup="checkPasswordStrength()">
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="password_icon"></i>
                            </button>
                        </div>
                        <div class="password-strength">
                            <div class="password-strength-bar" id="strengthBar"></div>
                        </div>
                        <div class="password-requirements">
                            <div class="requirement" id="length">
                                <i class="fas fa-times"></i> At least 8 characters
                            </div>
                            <div class="requirement" id="uppercase">
                                <i class="fas fa-times"></i> One uppercase letter
                            </div>
                            <div class="requirement" id="lowercase">
                                <i class="fas fa-times"></i> One lowercase letter
                            </div>
                            <div class="requirement" id="number">
                                <i class="fas fa-times"></i> One number
                            </div>
                        </div>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">Confirm New Password</label>
                        <div style="position: relative;">
                            <input type="password" class="form-control" 
                                   id="password_confirmation" name="password_confirmation" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                <i class="fas fa-eye" id="password_confirmation_icon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key"></i> Update Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('password').value;
    const strengthBar = document.getElementById('strengthBar');
    
    // Requirements
    const length = document.getElementById('length');
    const uppercase = document.getElementById('uppercase');
    const lowercase = document.getElementById('lowercase');
    const number = document.getElementById('number');
    
    let score = 0;
    
    // Check length
    if (password.length >= 8) {
        length.classList.add('met');
        length.querySelector('i').classList.remove('fa-times');
        length.querySelector('i').classList.add('fa-check');
        score++;
    } else {
        length.classList.remove('met');
        length.querySelector('i').classList.remove('fa-check');
        length.querySelector('i').classList.add('fa-times');
    }
    
    // Check uppercase
    if (/[A-Z]/.test(password)) {
        uppercase.classList.add('met');
        uppercase.querySelector('i').classList.remove('fa-times');
        uppercase.querySelector('i').classList.add('fa-check');
        score++;
    } else {
        uppercase.classList.remove('met');
        uppercase.querySelector('i').classList.remove('fa-check');
        uppercase.querySelector('i').classList.add('fa-times');
    }
    
    // Check lowercase
    if (/[a-z]/.test(password)) {
        lowercase.classList.add('met');
        lowercase.querySelector('i').classList.remove('fa-times');
        lowercase.querySelector('i').classList.add('fa-check');
        score++;
    } else {
        lowercase.classList.remove('met');
        lowercase.querySelector('i').classList.remove('fa-check');
        lowercase.querySelector('i').classList.add('fa-times');
    }
    
    // Check number
    if (/[0-9]/.test(password)) {
        number.classList.add('met');
        number.querySelector('i').classList.remove('fa-times');
        number.querySelector('i').classList.add('fa-check');
        score++;
    } else {
        number.classList.remove('met');
        number.querySelector('i').classList.remove('fa-check');
        number.querySelector('i').classList.add('fa-times');
    }
    
    // Update strength bar
    strengthBar.className = 'password-strength-bar';
    if (score === 1) {
        strengthBar.classList.add('strength-weak');
    } else if (score === 2) {
        strengthBar.classList.add('strength-fair');
    } else if (score === 3) {
        strengthBar.classList.add('strength-good');
    } else if (score === 4) {
        strengthBar.classList.add('strength-strong');
    }
}
</script>
@endsection
