<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Submitted Successfully - BelteiEcom Partners</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .success-container {
            max-width: 600px;
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: bounce 0.8s ease-out 0.3s both;
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -15px, 0);
            }
            70% {
                transform: translate3d(0, -7px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }

        .success-icon i {
            font-size: 2.5rem;
            color: white;
        }

        .success-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .success-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .info-cards {
            display: grid;
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }

        .info-card h3 {
            color: #667eea;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card p {
            color: #666;
            line-height: 1.5;
        }

        .timeline {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .timeline h3 {
            color: #1976d2;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .timeline-steps {
            display: grid;
            gap: 1rem;
        }

        .timeline-step {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .step-number {
            width: 30px;
            height: 30px;
            background: #1976d2;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .step-text {
            color: #333;
            font-weight: 500;
        }

        .action-buttons {
            display: grid;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            text-decoration: none;
        }

        .contact-info {
            background: #d4edda;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .contact-info h3 {
            color: #155724;
            margin-bottom: 1rem;
        }

        .contact-info p {
            color: #155724;
            margin-bottom: 0.5rem;
        }

        .contact-info a {
            color: #155724;
            font-weight: 600;
            text-decoration: none;
        }

        .contact-info a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .success-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>

        <h1 class="success-title">Application Submitted!</h1>
        <p class="success-subtitle">
            Thank you for applying to become a BelteiEcom partner. We've received your application and will review it shortly.
        </p>

        <div class="info-cards">
            <div class="info-card">
                <h3><i class="fas fa-envelope"></i> Confirmation Email Sent</h3>
                <p>We've sent a confirmation email with your application details. Please check your inbox (and spam folder).</p>
            </div>
        </div>

        <div class="timeline">
            <h3><i class="fas fa-clock"></i> What Happens Next?</h3>
            <div class="timeline-steps">
                <div class="timeline-step">
                    <div class="step-number">1</div>
                    <div class="step-text">Application Review (1-2 business days)</div>
                </div>
                <div class="timeline-step">
                    <div class="step-number">2</div>
                    <div class="step-text">Business Verification & Background Check</div>
                </div>
                <div class="timeline-step">
                    <div class="step-number">3</div>
                    <div class="step-text">Decision Email with API Credentials (if approved)</div>
                </div>
                <div class="timeline-step">
                    <div class="step-number">4</div>
                    <div class="step-text">Integration Support & Onboarding</div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="{{ route('api.docs') }}" class="btn btn-primary">
                <i class="fas fa-book"></i> Browse API Documentation
            </a>
            <a href="{{ route('home') }}" class="btn btn-secondary">
                <i class="fas fa-home"></i> Return to Homepage
            </a>
        </div>

        <div class="contact-info">
            <h3><i class="fas fa-headset"></i> Need Help?</h3>
            <p><strong>Partner Success Team</strong></p>
            <p><i class="fas fa-envelope"></i> Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><i class="fas fa-phone"></i> Phone: <a href="tel:+1234567890">+****************</a></p>
            <p><i class="fas fa-clock"></i> Business Hours: Monday - Friday, 9 AM - 6 PM EST</p>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate timeline steps
            const steps = document.querySelectorAll('.timeline-step');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.style.opacity = '0';
                    step.style.transform = 'translateX(-20px)';
                    step.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        step.style.opacity = '1';
                        step.style.transform = 'translateX(0)';
                    }, 100);
                }, index * 200 + 1000);
            });
        });
    </script>
</body>
</html>
