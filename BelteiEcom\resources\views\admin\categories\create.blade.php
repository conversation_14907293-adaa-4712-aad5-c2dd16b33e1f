@extends('admin.layouts.app')

@section('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: rgba(255,255,255,0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
        z-index: 1;
        pointer-events: none;
    }

    .page-header .d-flex {
        position: relative;
        z-index: 2;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }

    .form-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.5rem;
    }

    .form-group label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .category-type-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #667eea;
    }

    .category-type-info h6 {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .category-type-info p {
        color: #6c757d;
        margin-bottom: 0;
        font-size: 0.9rem;
    }
</style>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2">🏷️ Add New Category</h1>
                <p class="mb-0 opacity-75">Create a new category or subcategory for your products</p>
            </div>
            <div>
                <a href="{{ route('admin.categories.index') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-left"></i> Back to Categories
                </a>
            </div>
        </div>
    </div>

    <!-- Category Type Information -->
    <div class="category-type-info slide-up" style="animation-delay: 0.2s;">
        <h6><i class="fas fa-info-circle"></i> Category Types</h6>
        <p><strong>Parent Category:</strong> Select "None" to create a main category (e.g., Electronics, Clothing)</p>
        <p><strong>Sub-Category:</strong> Select an existing category to create a subcategory (e.g., Smartphones under Electronics)</p>
    </div>

    <!-- Create Category Form -->
    <div class="form-card slide-up" style="animation-delay: 0.4s;">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold"><i class="fas fa-plus-circle"></i> Category Information</h6>
        </div>
        <div class="card-body p-4">
            <form action="{{ route('admin.categories.store') }}" method="POST">
                @csrf

                <div class="form-group">
                    <label for="name"><i class="fas fa-tag"></i> Category Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" placeholder="Enter category name (e.g., Electronics, Smartphones)" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="description"><i class="fas fa-align-left"></i> Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3" placeholder="Describe what products belong in this category">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="parent_id"><i class="fas fa-sitemap"></i> Parent Category</label>
                    <select class="form-control @error('parent_id') is-invalid @enderror" id="parent_id" name="parent_id">
                        <option value="">🏠 None (Create Parent Category)</option>
                        @foreach($categories as $category)
                            @if(!$category->parent_id)
                                <option value="{{ $category->id }}" {{ old('parent_id') == $category->id ? 'selected' : '' }}>
                                    👑 {{ $category->name }} (Parent)
                                </option>
                            @endif
                        @endforeach
                    </select>
                    <small class="form-text text-muted">
                        <i class="fas fa-lightbulb"></i>
                        <strong>Tip:</strong> Select "None" to create a main category, or choose an existing parent category to create a subcategory.
                    </small>
                    @error('parent_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save"></i> Create Category
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
