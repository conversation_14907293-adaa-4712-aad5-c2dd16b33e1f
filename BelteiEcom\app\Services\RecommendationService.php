<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductView;
use App\Models\ProductRecommendation;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Review;
use App\Models\Wishlist;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class RecommendationService
{
    /**
     * Generate all types of recommendations for a product.
     */
    public function generateRecommendations($productId)
    {
        $product = Product::find($productId);
        if (!$product) {
            return false;
        }

        // Generate different types of recommendations
        $this->generateCollaborativeRecommendations($productId);
        $this->generateContentBasedRecommendations($productId);
        $this->generateSimilarProductRecommendations($productId);
        $this->generateTrendingRecommendations($productId);

        return true;
    }

    /**
     * Collaborative Filtering: "Customers who bought this also bought..."
     */
    protected function generateCollaborativeRecommendations($productId)
    {
        // Find users who bought this product
        $userIds = OrderItem::where('product_id', $productId)
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.status', 'completed')
            ->pluck('orders.user_id')
            ->unique();

        if ($userIds->isEmpty()) {
            return;
        }

        // Find other products these users bought
        $recommendations = OrderItem::select('product_id')
            ->selectRaw('COUNT(*) as purchase_count')
            ->selectRaw('COUNT(DISTINCT orders.user_id) as user_count')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.user_id', $userIds)
            ->where('order_items.product_id', '!=', $productId)
            ->where('orders.status', 'completed')
            ->groupBy('product_id')
            ->having('user_count', '>=', 2) // At least 2 users bought both products
            ->orderByDesc('purchase_count')
            ->limit(10)
            ->get();

        $recommendationData = [];
        $totalUsers = $userIds->count();

        foreach ($recommendations as $rec) {
            $confidence = min(($rec->user_count / $totalUsers) * ($rec->purchase_count / 10), 1.0);
            
            $recommendationData[] = [
                'product_id' => $rec->product_id,
                'score' => $confidence,
                'metadata' => [
                    'type' => 'collaborative',
                    'reason' => "Customers who bought this item also bought this",
                    'purchase_count' => $rec->purchase_count,
                    'user_count' => $rec->user_count
                ]
            ];
        }

        ProductRecommendation::storeRecommendations($productId, $recommendationData, 'collaborative');
    }

    /**
     * Content-Based Filtering: Similar products by category, price, features
     */
    protected function generateContentBasedRecommendations($productId)
    {
        $product = Product::find($productId);
        
        // Find products in same category with similar price range
        $priceMin = $product->price * 0.7; // 30% lower
        $priceMax = $product->price * 1.3; // 30% higher

        $recommendations = Product::where('id', '!=', $productId)
            ->where('category_id', $product->category_id)
            ->whereBetween('price', [$priceMin, $priceMax])
            ->where('stock', '>', 0)
            ->withCount('reviews')
            ->withAvg('reviews', 'rating')
            ->orderByDesc('reviews_avg_rating')
            ->orderByDesc('reviews_count')
            ->limit(10)
            ->get();

        $recommendationData = [];

        foreach ($recommendations as $rec) {
            // Calculate similarity score based on multiple factors
            $priceScore = 1 - (abs($product->price - $rec->price) / max($product->price, $rec->price));
            $ratingScore = ($rec->reviews_avg_rating ?: 0) / 5;
            $popularityScore = min(($rec->reviews_count ?: 0) / 50, 1); // Normalize to max 50 reviews
            
            $confidence = ($priceScore * 0.4 + $ratingScore * 0.3 + $popularityScore * 0.3);
            
            $recommendationData[] = [
                'product_id' => $rec->id,
                'score' => $confidence,
                'metadata' => [
                    'type' => 'content_based',
                    'reason' => "Similar products in {$product->category->name}",
                    'price_similarity' => round($priceScore, 2),
                    'rating' => $rec->reviews_avg_rating ?: 0
                ]
            ];
        }

        ProductRecommendation::storeRecommendations($productId, $recommendationData, 'content_based');
    }

    /**
     * Similar Products: Based on name similarity and category
     */
    protected function generateSimilarProductRecommendations($productId)
    {
        $product = Product::find($productId);
        
        // Extract keywords from product name
        $keywords = $this->extractKeywords($product->name);
        
        if (empty($keywords)) {
            return;
        }

        $recommendations = Product::where('id', '!=', $productId)
            ->where('stock', '>', 0)
            ->withCount('reviews')
            ->withAvg('reviews', 'rating')
            ->get()
            ->map(function ($rec) use ($keywords, $product) {
                $similarity = $this->calculateTextSimilarity($product->name, $rec->name);
                $categoryBonus = ($rec->category_id === $product->category_id) ? 0.2 : 0;

                return [
                    'product' => $rec,
                    'score' => min($similarity + $categoryBonus, 1.0)
                ];
            })
            ->filter(function ($item) {
                return $item['score'] > 0.3; // Minimum similarity threshold
            })
            ->sortByDesc('score')
            ->take(10);

        $recommendationData = [];

        foreach ($recommendations as $rec) {
            $recommendationData[] = [
                'product_id' => $rec['product']->id,
                'score' => $rec['score'],
                'metadata' => [
                    'type' => 'similar',
                    'reason' => "Similar to {$product->name}",
                    'similarity_score' => round($rec['score'], 2)
                ]
            ];
        }

        ProductRecommendation::storeRecommendations($productId, $recommendationData, 'similar');
    }

    /**
     * Trending Products: Popular items with recent activity
     */
    protected function generateTrendingRecommendations($productId)
    {
        $product = Product::find($productId);
        
        // Get trending products in same category
        $trending = Product::select('products.*')
            ->selectRaw('
                (
                    COALESCE(recent_views.view_count, 0) * 0.4 +
                    COALESCE(recent_orders.order_count, 0) * 0.3 +
                    COALESCE(recent_reviews.review_count, 0) * 0.2 +
                    COALESCE(recent_wishlists.wishlist_count, 0) * 0.1
                ) as trend_score
            ')
            ->withCount('reviews')
            ->withAvg('reviews', 'rating')
            ->leftJoin(DB::raw('(
                SELECT product_id, COUNT(*) as view_count 
                FROM product_views 
                WHERE viewed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY product_id
            ) as recent_views'), 'products.id', '=', 'recent_views.product_id')
            ->leftJoin(DB::raw('(
                SELECT oi.product_id, COUNT(*) as order_count 
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND o.status = "completed"
                GROUP BY oi.product_id
            ) as recent_orders'), 'products.id', '=', 'recent_orders.product_id')
            ->leftJoin(DB::raw('(
                SELECT product_id, COUNT(*) as review_count 
                FROM reviews 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY product_id
            ) as recent_reviews'), 'products.id', '=', 'recent_reviews.product_id')
            ->leftJoin(DB::raw('(
                SELECT product_id, COUNT(*) as wishlist_count 
                FROM wishlists 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY product_id
            ) as recent_wishlists'), 'products.id', '=', 'recent_wishlists.product_id')
            ->where('products.id', '!=', $productId)
            ->where('products.stock', '>', 0)
            ->where('products.category_id', $product->category_id)
            ->orderByDesc('trend_score')
            ->limit(10)
            ->get();

        $recommendationData = [];

        foreach ($trending as $rec) {
            if ($rec->trend_score > 0) {
                $confidence = min($rec->trend_score / 10, 1.0); // Normalize score
                
                $recommendationData[] = [
                    'product_id' => $rec->id,
                    'score' => $confidence,
                    'metadata' => [
                        'type' => 'trending',
                        'reason' => "Trending in {$product->category->name}",
                        'trend_score' => round($rec->trend_score, 2)
                    ]
                ];
            }
        }

        ProductRecommendation::storeRecommendations($productId, $recommendationData, 'trending');
    }

    /**
     * Extract keywords from product name
     */
    protected function extractKeywords($text)
    {
        // Remove common words and extract meaningful keywords
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        $words = preg_split('/\s+/', strtolower($text));
        
        return array_filter($words, function ($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords);
        });
    }

    /**
     * Calculate text similarity between two strings
     */
    protected function calculateTextSimilarity($text1, $text2)
    {
        $words1 = $this->extractKeywords($text1);
        $words2 = $this->extractKeywords($text2);
        
        if (empty($words1) || empty($words2)) {
            return 0;
        }
        
        $intersection = array_intersect($words1, $words2);
        $union = array_unique(array_merge($words1, $words2));
        
        return count($intersection) / count($union); // Jaccard similarity
    }

    /**
     * Get personalized recommendations for a user
     */
    public function getPersonalizedRecommendations($userId, $limit = 12)
    {
        $cacheKey = "user_recommendations_{$userId}";
        
        return Cache::remember($cacheKey, 3600, function () use ($userId, $limit) {
            // Get user's purchase history
            $purchasedProducts = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.user_id', $userId)
                ->where('orders.status', 'completed')
                ->pluck('order_items.product_id')
                ->unique();

            // Get user's viewed products
            $viewedProducts = ProductView::where('user_id', $userId)
                ->where('viewed_at', '>=', now()->subDays(30))
                ->pluck('product_id')
                ->unique();

            // Get user's wishlist
            $wishlistedProducts = Wishlist::where('user_id', $userId)
                ->pluck('product_id')
                ->unique();

            // Combine all user interactions
            $userProducts = $purchasedProducts->merge($viewedProducts)->merge($wishlistedProducts)->unique();

            if ($userProducts->isEmpty()) {
                // New user - return trending products
                return ProductView::getPopularProducts($limit);
            }

            // Get recommendations based on user's products
            $recommendations = collect();
            
            foreach ($userProducts as $productId) {
                $productRecs = ProductRecommendation::getMixedRecommendations($productId, 3);
                $recommendations = $recommendations->merge($productRecs);
            }

            // Filter out products user already has
            $allUserProducts = $purchasedProducts->merge($viewedProducts)->merge($wishlistedProducts);
            
            return $recommendations
                ->whereNotIn('recommended_product_id', $allUserProducts)
                ->unique('recommended_product_id')
                ->sortByDesc('confidence_score')
                ->take($limit)
                ->pluck('recommendedProduct')
                ->filter(); // Remove null values
        });
    }

    /**
     * Record a product view for recommendation tracking
     */
    public function recordProductView($productId, $userId = null, $viewDuration = 0)
    {
        $sessionId = $userId ? null : session()->getId();
        
        ProductView::recordView($productId, $userId, $sessionId, $viewDuration);
        
        // Trigger recommendation generation for popular products
        $this->maybeGenerateRecommendations($productId);
    }

    /**
     * Maybe generate recommendations based on product popularity
     */
    protected function maybeGenerateRecommendations($productId)
    {
        $cacheKey = "recommendations_generated_{$productId}";
        
        if (!Cache::has($cacheKey)) {
            // Check if product has enough data for recommendations
            $viewCount = ProductView::where('product_id', $productId)->count();
            
            if ($viewCount >= 10) { // Generate recommendations after 10 views
                $this->generateRecommendations($productId);
                Cache::put($cacheKey, true, 3600); // Cache for 1 hour
            }
        }
    }
}
