@extends('admin.layouts.app')

@section('title', 'Chat Conversation - ' . $conversation->customer_name)

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.chat.index') }}">Live Chat</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.chat.conversations') }}">Conversations</a>
                    </li>
                    <li class="breadcrumb-item active">{{ $conversation->customer_name }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-comment-dots text-primary"></i>
                Chat with {{ $conversation->customer_name }}
                <span class="badge badge-{{ $conversation->status_color }}">
                    {{ ucfirst($conversation->status) }}
                </span>
            </h1>
        </div>
        <div class="btn-group">
            <a href="{{ route('admin.chat.conversations') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            @if($conversation->status === 'active')
                <button class="btn btn-warning btn-sm" onclick="transferConversation()">
                    <i class="fas fa-exchange-alt"></i> Transfer
                </button>
                <button class="btn btn-danger btn-sm" onclick="closeConversation()">
                    <i class="fas fa-times"></i> Close Chat
                </button>
            @endif
        </div>
    </div>

    <div class="row">
        <!-- Chat Interface -->
        <div class="col-lg-8">
            <div class="card shadow">
                <!-- Chat Header -->
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-md mr-3">
                                <div class="bg-white bg-opacity-20 rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $conversation->customer_name }}</h6>
                                <small>{{ $conversation->customer_email }}</small>
                                @if($conversation->customer)
                                    <span class="badge badge-light badge-sm ml-2">Registered User</span>
                                @else
                                    <span class="badge badge-secondary badge-sm ml-2">Guest</span>
                                @endif
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="badge badge-{{ $conversation->priority_color }} mb-1">
                                {{ ucfirst($conversation->priority) }} Priority
                            </div>
                            <br>
                            <small>Started: {{ $conversation->created_at->format('M j, Y H:i') }}</small>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div class="card-body p-0">
                    <div id="chat-messages" class="chat-messages">
                        @foreach($messages as $message)
                            <div class="message {{ $message->sender_type }}" data-message-id="{{ $message->id }}">
                                <div class="message-content">
                                    <div class="message-header">
                                        <strong class="sender-name">{{ $message->sender_name }}</strong>
                                        <small class="message-time">{{ $message->created_at->format('H:i') }}</small>
                                    </div>
                                    <div class="message-text">{{ $message->message }}</div>
                                    @if($message->is_edited)
                                        <small class="text-muted"><i class="fas fa-edit"></i> Edited</small>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Chat Input -->
                @if($conversation->status === 'active')
                    <div class="card-footer">
                        <form id="message-form" class="message-form">
                            <div class="input-group">
                                <input type="text" id="message-input" class="form-control" 
                                       placeholder="Type your message..." maxlength="1000" required>
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Send
                                    </button>
                                </div>
                            </div>
                        </form>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Press Enter to send, Shift+Enter for new line
                            </small>
                        </div>
                    </div>
                @else
                    <div class="card-footer bg-light text-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            This conversation is {{ $conversation->status }}. No new messages can be sent.
                        </small>
                    </div>
                @endif
            </div>
        </div>

        <!-- Conversation Details -->
        <div class="col-lg-4">
            <!-- Customer Information -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        Customer Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-lg mb-3">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                        </div>
                        <h6>{{ $conversation->customer_name }}</h6>
                        <p class="text-muted">{{ $conversation->customer_email }}</p>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-primary">{{ $conversation->messages->count() }}</h5>
                            <small class="text-muted">Messages</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-info">
                                @if($conversation->getDuration())
                                    {{ $conversation->getDuration() }}m
                                @else
                                    -
                                @endif
                            </h5>
                            <small class="text-muted">Duration</small>
                        </div>
                    </div>
                    
                    @if($conversation->customer_info)
                        <hr>
                        <h6 class="text-muted mb-2">Session Info:</h6>
                        <small class="text-muted">
                            <strong>IP:</strong> {{ $conversation->customer_info['ip_address'] ?? 'Unknown' }}<br>
                            <strong>Browser:</strong> {{ Str::limit($conversation->customer_info['user_agent'] ?? 'Unknown', 50) }}<br>
                            <strong>Page:</strong> {{ $conversation->customer_info['url'] ?? 'Unknown' }}
                        </small>
                    @endif
                </div>
            </div>

            <!-- Conversation Details -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        Conversation Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Subject:</strong><br>
                        <span class="text-muted">{{ $conversation->subject ?: 'General Inquiry' }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Priority:</strong><br>
                        <span class="badge badge-{{ $conversation->priority_color }}">
                            {{ ucfirst($conversation->priority) }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Status:</strong><br>
                        <span class="badge badge-{{ $conversation->status_color }}">
                            {{ ucfirst($conversation->status) }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Agent:</strong><br>
                        @if($conversation->agent)
                            <span class="text-primary">{{ $conversation->agent->name }}</span>
                        @else
                            <span class="text-muted">Unassigned</span>
                        @endif
                    </div>
                    
                    <div class="mb-3">
                        <strong>Started:</strong><br>
                        <small class="text-muted">{{ $conversation->created_at->format('M j, Y H:i:s') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Last Activity:</strong><br>
                        <small class="text-muted">{{ $conversation->last_activity_at->diffForHumans() }}</small>
                    </div>
                    
                    @if($conversation->ended_at)
                        <div class="mb-3">
                            <strong>Ended:</strong><br>
                            <small class="text-muted">{{ $conversation->ended_at->format('M j, Y H:i:s') }}</small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Rating & Feedback -->
            @if($conversation->rating || $conversation->feedback)
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-star"></i>
                            Customer Feedback
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($conversation->rating)
                            <div class="mb-3">
                                <strong>Rating:</strong><br>
                                <div class="text-warning">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star{{ $i <= $conversation->rating ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                                <small class="text-muted">({{ $conversation->rating }}/5)</small>
                            </div>
                        @endif
                        
                        @if($conversation->feedback)
                            <div class="mb-3">
                                <strong>Feedback:</strong><br>
                                <p class="text-muted">{{ $conversation->feedback }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 20px;
    display: flex;
}

.message.customer {
    justify-content: flex-end;
}

.message.agent {
    justify-content: flex-start;
}

.message.system {
    justify-content: center;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.message.customer .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.agent .message-content {
    background: #e9ecef;
    color: #333;
    border-bottom-left-radius: 5px;
}

.message.system .message-content {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    text-align: center;
    font-style: italic;
    max-width: 80%;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.sender-name {
    font-size: 12px;
    opacity: 0.8;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
}

.message-text {
    line-height: 1.4;
    word-wrap: break-word;
}

.avatar-md, .avatar-lg {
    flex-shrink: 0;
}

.badge-sm {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
}

.bg-opacity-20 {
    background-color: rgba(255, 255, 255, 0.2) !important;
}
</style>

<script>
let lastMessageId = {{ $messages->last()->id ?? 0 }};
let chatInterval = null;

// Start polling for new messages
document.addEventListener('DOMContentLoaded', function() {
    @if($conversation->status === 'active')
        startPolling();
    @endif
    
    // Set up message form
    document.getElementById('message-form').addEventListener('submit', sendMessage);
    
    // Auto-scroll to bottom
    scrollToBottom();
});

function sendMessage(e) {
    e.preventDefault();
    
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add message to UI immediately
    addMessageToUI(message, 'agent', 'Just now', '{{ Auth::user()->name }}');
    messageInput.value = '';
    
    fetch('{{ route("admin.chat.send-message", $conversation->id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('Error sending message: ' + data.message);
            location.reload();
        } else {
            lastMessageId = data.message.id;
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        alert('An error occurred while sending the message.');
    });
}

function addMessageToUI(message, senderType, time, senderName) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${senderType}`;
    
    messageDiv.innerHTML = `
        <div class="message-content">
            <div class="message-header">
                <strong class="sender-name">${senderName || (senderType === 'customer' ? '{{ $conversation->customer_name }}' : 'Agent')}</strong>
                <small class="message-time">${time}</small>
            </div>
            <div class="message-text">${message}</div>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    scrollToBottom();
}

function scrollToBottom() {
    const messagesContainer = document.getElementById('chat-messages');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function startPolling() {
    if (chatInterval) clearInterval(chatInterval);
    
    chatInterval = setInterval(() => {
        checkNewMessages();
    }, 3000); // Poll every 3 seconds
}

function checkNewMessages() {
    fetch(`{{ route('chat.get-messages', $conversation->id) }}?last_message_id=${lastMessageId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.messages.length > 0) {
            data.messages.forEach(message => {
                if (message.sender_type !== 'agent') { // Don't add our own messages
                    addMessageToUI(message.message, message.sender_type, message.formatted_time, message.sender_name);
                }
                lastMessageId = Math.max(lastMessageId, message.id);
            });
        }
    })
    .catch(error => {
        console.error('Error checking messages:', error);
    });
}

function transferConversation() {
    // You can implement a transfer modal here
    alert('Transfer functionality would open a modal to select target agent.');
}

function closeConversation() {
    if (confirm('Are you sure you want to close this conversation?')) {
        const reason = prompt('Reason for closing (optional):');
        
        fetch('{{ route("admin.chat.close", $conversation->id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while closing the conversation.');
        });
    }
}

// Handle Enter key in message input
document.getElementById('message-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        document.getElementById('message-form').dispatchEvent(new Event('submit'));
    }
});
</script>
@endsection
