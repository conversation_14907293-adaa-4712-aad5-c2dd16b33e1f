<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Category;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix category hierarchy by organizing subcategories under proper parent categories

        // First, find or create main parent categories
        $electronics = Category::firstOrCreate(['name' => 'Electronics'], [
            'description' => 'Electronic devices and accessories',
            'slug' => 'electronics',
            'parent_id' => null
        ]);

        $clothing = Category::firstOrCreate(['name' => 'Clothing'], [
            'description' => 'Apparel and fashion items',
            'slug' => 'clothing',
            'parent_id' => null
        ]);

        $homeKitchen = Category::firstOrCreate(['name' => 'Home & Kitchen'], [
            'description' => 'Home appliances and kitchen essentials',
            'slug' => 'home-kitchen',
            'parent_id' => null
        ]);

        $books = Category::firstOrCreate(['name' => 'Books'], [
            'description' => 'Books, e-books, and publications',
            'slug' => 'books',
            'parent_id' => null
        ]);

        $sports = Category::firstOrCreate(['name' => 'Sports & Outdoors'], [
            'description' => 'Sports equipment and outdoor gear',
            'slug' => 'sports-outdoors',
            'parent_id' => null
        ]);

        // Now organize subcategories under their proper parents

        // Electronics subcategories
        $electronicsSubcategories = [
            'Smartphones', 'Laptops', 'Audio', 'Tablets', 'Cameras', 'Gaming'
        ];

        foreach ($electronicsSubcategories as $subcat) {
            Category::where('name', $subcat)->update(['parent_id' => $electronics->id]);
        }

        // Clothing subcategories
        $clothingSubcategories = [
            "Men's Clothing", "Women's Clothing", 'Accessories', 'Shoes', 'Bags'
        ];

        foreach ($clothingSubcategories as $subcat) {
            Category::where('name', $subcat)->update(['parent_id' => $clothing->id]);
        }

        // Home & Kitchen subcategories
        $homeSubcategories = [
            'Kitchen Appliances', 'Furniture', 'Decor', 'Bedding', 'Storage'
        ];

        foreach ($homeSubcategories as $subcat) {
            Category::where('name', $subcat)->update(['parent_id' => $homeKitchen->id]);
        }

        // Books subcategories
        $bookSubcategories = [
            'Fiction', 'Non-Fiction', 'Educational', 'Comics', 'Magazines'
        ];

        foreach ($bookSubcategories as $subcat) {
            Category::where('name', $subcat)->update(['parent_id' => $books->id]);
        }

        // Sports subcategories
        $sportsSubcategories = [
            'Fitness', 'Outdoor Recreation', 'Team Sports', 'Water Sports'
        ];

        foreach ($sportsSubcategories as $subcat) {
            Category::where('name', $subcat)->update(['parent_id' => $sports->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset all categories to have no parent (make them all parent categories)
        Category::whereNotNull('parent_id')->update(['parent_id' => null]);
    }
};
