<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KHQR Payment Request - BelteiEcom</title>
    <script src="https://github.com/davidhuotkeo/bakong-khqr/releases/download/bakong-khqr-1.0.6/khqr-1.0.6.min.js"></script>
</head>

<body>
    <div id="response"></div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const { BakongKHQR, khqrData, MerchantInfo } = window.BakongKHQR;

            const merchantInfo = {
                bakongAccountID: "chhunlichhean_kun@wing",
                merchantName: "BelteiEcom",
                merchantCity: "Phnom Penh",
                merchantId: "1263629",
                acquiringBank: "Bakong",
            };

            const optionalData = {
                currency: khqrData.currency.usd,
                amount: {{ $payment->amount }},
                billNumber: "ORDER{{ $order->id }}",
            };

            function generateBillNumber() {
                const currentDate = new Date();
                const formattedDate = currentDate.toISOString().replace(/[-:.TZ]/g, "");
                const day = formattedDate.substr(0, 8);
                const randomNumber = Math.floor(Math.random() * 1000);
                return `KHQR${day}${randomNumber}`;
            }

            const merchantInfoInstance = new MerchantInfo(
                merchantInfo.bakongAccountID,
                merchantInfo.merchantName,
                merchantInfo.merchantCity,
                merchantInfo.merchantId,
                merchantInfo.acquiringBank,
                optionalData
            );

            const khqr = new BakongKHQR();
            const response = khqr.generateMerchant(merchantInfoInstance);

            // Display the response data on the frontend
            const responseElement = document.getElementById("response");
            responseElement.textContent = "Processing payment request...";

            // Check if the response has the necessary data
            if (response && response.status && response.status.code === 0 && response.data && response.data.qr && response.data.md5) {
                const qrData = response.data.qr;
                const md5 = response.data.md5;
                const username = "{{ $username }}";
                const paymentId = {{ $payment->id }};

                // Redirect to save.php with the parameters
                const saveUrl = '/bakong/save?qr=' + encodeURIComponent(qrData) +
                    '&amount=' + optionalData.amount +
                    '&md5=' + encodeURIComponent(md5) +
                    '&username=' + encodeURIComponent(username) +
                    '&payment_id=' + paymentId;

                // Redirect to save.php
                window.location.href = saveUrl;
            } else {
                responseElement.textContent = "Error generating KHQR code. Please try again.";
                console.error("Error generating KHQR code:", response);

                // Redirect back to checkout after 3 seconds
                setTimeout(function() {
                    window.location.href = '{{ route("orders.checkout") }}';
                }, 3000);
            }
        });
    </script>
</body>

</html>
